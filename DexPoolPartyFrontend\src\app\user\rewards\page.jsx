"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Gift, Users, TrendingUp, Coins, Copy, Trophy, ArrowUpRight, Wallet, Share2 } from "lucide-react"
import { toast } from 'sonner';

// Assuming you have a useAuth hook to get the user ID
// import { useAuth } from '@/hooks/useAuth'; // Uncomment and replace with your actual auth hook

export default function RewardsPage() {
  const user = JSON.parse(localStorage.getItem("user") || 'null');
  const [rewardData, setRewardData] = useState(null)
  const [leaderboard, setLeaderboard] = useState([])
  const [referralCodeInput, setReferralCodeInput] = useState("") // Renamed to avoid conflict with actual referralCode
  const [withdrawAmount, setWithdrawAmount] = useState("")
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")

  // Mock user ID - replace with actual user authentication
  // const { user } = useAuth(); // Uncomment this line if you have a useAuth hook
  const userId = user.id; // For demonstration, keep mock userId, replace with user?.id or similar from auth

  useEffect(() => {
    if (userId) { // Only fetch if userId is available
      fetchRewardData(userId);
      fetchLeaderboard();
    }
  }, [userId]); // Re-run when userId changes (e.g., after login)

  const fetchRewardData = async (currentUserId) => {
    try {
      setLoading(true);
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/reward/user/${currentUserId}`); // API call
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setRewardData(data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching reward data:", error);
      toast.error("Failed to load reward data.", {
        description: error.message,
        duration: 5000,
      });
      setLoading(false);
    }
  };

  const fetchLeaderboard = async () => {
    try {
      const response = await fetch(`/api/rewards/leaderboard`); // API call for leaderboard
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setLeaderboard(data);
    } catch (error) {
      console.error("Error fetching leaderboard:", error);
      toast.error("Failed to load leaderboard.", {
        description: error.message,
        duration: 5000,
      });
    }
  };

  const copyReferralCode = () => {
    if (rewardData?.user.referralCode) {
      navigator.clipboard.writeText(rewardData.user.referralCode)
      toast.success("Referral code copied to clipboard", {
        description: "Share it with your friends!",
        duration: 3000,
      })
    }
  }

  const shareReferralLink = () => {
    const referralLink = `https://dexpoolparty.com/signup?ref=${rewardData?.user.referralCode}`
    navigator.clipboard.writeText(referralLink)
    toast.info("Referral link copied!", {
      description: "You can now share this link to invite others.",
      duration: 3000,
    })
  }

  const applyReferralCode = async () => {
    if (!referralCodeInput.trim()) {
      toast.warning("Please enter a referral code.", {
        duration: 3000,
      })
      return
    }

    try {
      const response = await fetch('/api/rewards/referral/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, referralCode: referralCodeInput }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to apply referral code.");
      }

      const result = await response.json();
      toast.success(result.message, {
        description: `Referral bonus: ${result.referralBonus}, Welcome bonus: ${result.welcomeBonus}`,
        duration: 3000,
      });
      setReferralCodeInput("");
      fetchRewardData(userId); // Re-fetch data to reflect the bonus
    } catch (error) {
      toast.error("Failed to apply referral code.", {
        description: error.message,
        duration: 3000,
      });
    }
  };

  const withdrawRewards = async () => {
    const amount = Number.parseFloat(withdrawAmount)
    if (isNaN(amount) || amount <= 0) {
      toast.warning("Please enter a valid amount.", {
        duration: 3000,
      })
      return
    }

    if (amount < 100) {
      toast.warning("Minimum withdrawal amount is 100 tokens.", {
        duration: 3000,
      })
      return
    }

    if (amount > (rewardData?.user.rewards.availableBalance || 0)) {
      toast.error("Insufficient balance.", {
        description: "You do not have enough tokens for this withdrawal.",
        duration: 3000,
      })
      return
    }

    try {
      const response = await fetch('/api/rewards/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, amount }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Withdrawal failed.");
      }

      const result = await response.json();
      toast.success(result.message, {
        description: `Remaining balance: ${result.remainingBalance.toLocaleString()} tokens.`,
        duration: 5000,
      });
      setWithdrawAmount("");
      fetchRewardData(userId); // Re-fetch data to reflect the withdrawal
    } catch (error) {
      toast.error("Withdrawal failed.", {
        description: error.message,
        duration: 5000,
      });
    }
  }

  // You can also add a function for daily bonus if you have one
  const claimDailyBonus = async () => {
    try {
      const response = await fetch('/api/rewards/daily-bonus', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to claim daily bonus.");
      }

      const result = await response.json();
      toast.success(result.message, {
        description: `You received ${result.bonusAmount} tokens.`,
        duration: 3000,
      });
      fetchRewardData(userId); // Re-fetch data to reflect the bonus
    } catch (error) {
      toast.error("Failed to claim daily bonus.", {
        description: error.message,
        duration: 3000,
      });
    }
  };


  if (loading) {
    return (
      <div className="min-h-screen text-gray-100 container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-[#2A2A2A] rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-[#2A2A2A] rounded"></div>
            ))}
          </div>
          <div className="h-10 bg-[#2A2A2A] rounded w-full"></div> {/* Tabs list skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-64 bg-[#2A2A2A] rounded col-span-1"></div> {/* Card skeleton */}
            <div className="h-64 bg-[#2A2A2A] rounded col-span-1"></div> {/* Card skeleton */}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen text-gray-100 container mx-auto p-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Rewards Center</h1>
          <p className="text-gray-400">Earn rewards through trading and referrals</p>
        </div>
        <Badge variant="secondary" className="text-lg px-4 py-2 bg-[#2A2A2A] text-green-400 border-green-500">
          <Coins className="w-4 h-4 mr-2" />
          {rewardData?.user.rewards.availableBalance.toLocaleString()} Available
        </Badge>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Total Earned</CardTitle>
            <Gift className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{rewardData?.user.rewards.totalEarned.toLocaleString()}</div>
            <p className="text-xs text-green-400">+12% from last month</p> {/* This line is static, consider making dynamic */}
          </CardContent>
        </Card>

        <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Referral Earnings</CardTitle>
            <Users className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{rewardData?.user.rewards.referralEarnings.toLocaleString()}</div>
            <p className="text-xs text-gray-400">{rewardData?.user.referrals.length} active referrals</p>
          </CardContent>
        </Card>

        <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Trade Bonus</CardTitle>
            <TrendingUp className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{rewardData?.user.rewards.tradeBonus.toLocaleString()}</div>
            <p className="text-xs text-gray-400">From {rewardData?.user.stats.totalTrades} trades</p>
          </CardContent>
        </Card>

        <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Available Balance</CardTitle>
            <Wallet className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{rewardData?.user.rewards.availableBalance.toLocaleString()}</div>
            <p className="text-xs text-gray-400">Ready to withdraw</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-[#2A2A2A] text-gray-300">
          <TabsTrigger value="overview" className="data-[state=active]:bg-gray-700 text-white data-[state=active]:shadow-sm">Overview</TabsTrigger>
          <TabsTrigger value="referrals" className="data-[state=active]:bg-gray-700 text-white data-[state=active]:shadow-sm">Referrals</TabsTrigger>
          <TabsTrigger value="leaderboard" className="data-[state=active]:bg-gray-700 text-white data-[state=active]:shadow-sm">Leaderboard</TabsTrigger>
          <TabsTrigger value="withdraw" className="data-[state=active]:bg-gray-700 text-white data-[state=active]:shadow-sm">Withdraw</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Rewards */}
            <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Recent Rewards</CardTitle>
                <CardDescription className="text-gray-400">Your latest reward activities</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {rewardData?.recentRewards.map((reward, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border border-gray-700 rounded-lg ">
                    <div className="flex items-center space-x-3">
                      <div
                        className={`p-2 rounded-full ${reward.type === "referral"
                            ? "bg-blue-900 text-blue-300"
                            : reward.type === "trade_bonus"
                              ? "bg-green-900 text-green-300"
                              : "bg-purple-900 text-purple-300"
                          }`}
                      >
                        {reward.type === "referral" ? (
                          <Users className="w-4 h-4" />
                        ) : reward.type === "trade_bonus" ? (
                          <TrendingUp className="w-4 h-4" />
                        ) : (
                          <Gift className="w-4 h-4" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-white">{reward.description}</p>
                        <p className="text-sm text-gray-400">
                          {new Date(reward.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="bg-gray-700 text-green-400 border-green-500">+{reward.amount}</Badge>
                  </div>
                ))}
                {!rewardData?.recentRewards.length && (
                  <div className="text-center py-8 text-gray-500">
                    <Gift className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No recent rewards. Start earning now!</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Reward Breakdown */}
            <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Reward Breakdown</CardTitle>
                <CardDescription className="text-gray-400">Distribution of your earnings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {rewardData?.rewardStats.map((stat) => (
                  <div key={stat._id} className="space-y-2">
                    <div className="flex justify-between text-sm text-gray-300">
                      <span className="capitalize">{stat._id.replace("_", " ")}</span>
                      <span>
                        {stat.total} tokens ({stat.count} times)
                      </span>
                    </div>
                    <Progress value={(stat.total / (rewardData.user.rewards.totalEarned || 1)) * 100} className="h-2 bg-gray-700 [&::-webkit-progress-bar]:bg-gray-700 [&::-webkit-progress-value]:bg-blue-500 [&::-moz-progress-bar]:bg-blue-500" />
                  </div>
                ))}
                {!rewardData?.rewardStats.length && (
                  <div className="text-center py-8 text-gray-500">
                    <Coins className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No reward stats available yet.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="referrals" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Referral Code */}
            <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Your Referral Code</CardTitle>
                <CardDescription className="text-gray-400">Share this code to earn rewards</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Input
                    value={rewardData?.user.referralCode || ""}
                    readOnly
                    className="font-mono text-lg text-white border-gray-700"
                  />
                  <Button onClick={copyReferralCode} size="icon" className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
                <Button onClick={shareReferralLink} className="w-full bg-green-600 hover:bg-green-700 text-white">
                  <Share2 className="w-4 h-4 mr-2" />
                  Share Referral Link
                </Button>
                <div className="text-sm text-gray-400">
                  <p>• Earn 100 tokens for each successful referral</p>
                  <p>• Get 10% of your referrals' trade bonuses</p>
                  <p>• No limit on referral earnings</p>
                </div>
              </CardContent>
            </Card>

            {/* Apply Referral Code */}
            <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Apply Referral Code</CardTitle>
                <CardDescription className="text-gray-400">Enter a referral code to get bonus</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="referral-input" className="text-gray-300">Referral Code</Label>
                  <Input
                    id="referral-input"
                    placeholder="Enter referral code"
                    value={referralCodeInput}
                    onChange={(e) => setReferralCodeInput(e.target.value.toUpperCase())}
                    className=" text-white border-gray-700"
                  />
                </div>
                <Button onClick={applyReferralCode} className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                  Apply Code
                </Button>
                <div className="text-sm text-gray-400">
                  <p>• Get 50 tokens welcome bonus</p>
                  <p>• Can only be used once per account</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Referral List */}
          <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Your Referrals ({rewardData?.user.referrals.length || 0})</CardTitle>
              <CardDescription className="text-gray-400">Users who joined using your referral code</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {rewardData?.user.referrals.map((referral, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-gray-700 rounded-lg ">
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={referral.userId.profileImage || "/placeholder.svg"} />
                        <AvatarFallback className="bg-gray-700 text-gray-300">{referral.userId.username.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-white">{referral.userId.username}</p>
                        <p className="text-sm text-gray-400">
                          Joined {new Date(referral.joinedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-green-400">+{referral.totalRewardEarned} tokens</p>
                      <p className="text-sm text-gray-400">earned from this referral</p>
                    </div>
                  </div>
                ))}
                {!rewardData?.user.referrals.length && (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No referrals yet. Share your code to start earning!</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="leaderboard" className="space-y-6">
          <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Top Earners</CardTitle>
              <CardDescription className="text-gray-400">See how you rank against other users</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {leaderboard.map((user, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-gray-700 rounded-lg ">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-yellow-500 to-orange-600 text-white font-bold">
                        {index < 3 ? <Trophy className="w-4 h-4" /> : index + 1}
                      </div>
                      <Avatar>
                        <AvatarImage src={user.profileImage || "/placeholder.svg"} />
                        <AvatarFallback className="bg-gray-700 text-gray-300">{user.username.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-white">{user.username}</p>
                        <p className="text-sm text-gray-400">{user.referrals.length} referrals</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg text-green-400">{user.rewards.totalEarned.toLocaleString()}</p>
                      <p className="text-sm text-gray-400">total earned</p>
                    </div>
                  </div>
                ))}
                {!leaderboard.length && (
                  <div className="text-center py-8 text-gray-500">
                    <Trophy className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No leaderboard data yet. Be the first to earn!</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="withdraw" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Withdraw Rewards</CardTitle>
                <CardDescription className="text-gray-400">Convert your rewards to tokens</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="withdraw-amount" className="text-gray-300">Amount to Withdraw</Label>
                  <Input
                    id="withdraw-amount"
                    type="number"
                    placeholder="Enter amount"
                    value={withdrawAmount}
                    onChange={(e) => setWithdrawAmount(e.target.value)}
                    min="100"
                    max={rewardData?.user.rewards.availableBalance}
                    className=" text-white border-gray-700"
                  />
                  <p className="text-sm text-gray-400">
                    Available: {rewardData?.user.rewards.availableBalance.toLocaleString()} tokens
                  </p>
                </div>
                <Button onClick={withdrawRewards} className="w-full bg-indigo-600 hover:bg-indigo-700 text-white">
                  <ArrowUpRight className="w-4 h-4 mr-2" />
                  Withdraw Rewards
                </Button>
                <div className="text-sm text-gray-400 space-y-1">
                  <p>• Minimum withdrawal: 100 tokens</p>
                  <p>• Processing time: 24-48 hours</p>
                  <p>• No withdrawal fees</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-[#2A2A2A] text-gray-100 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Withdrawal History</CardTitle>
                <CardDescription className="text-gray-400">Your recent withdrawals</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Assuming withdrawal history is part of `recentRewards` or a separate endpoint */}
                <div className="space-y-4">
                  {/* Filter recent rewards to show only withdrawals, or fetch from a dedicated endpoint */}
                  {rewardData?.recentRewards
                    ?.filter(reward => reward.type === 'withdrawal')
                    .map((withdrawal, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-gray-700 rounded-lg ">
                        <div>
                          <p className="font-medium text-white">Withdrawal</p>
                          <p className="text-sm text-gray-400">
                            {new Date(withdrawal.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-red-400">
                            {withdrawal.amount < 0 ? withdrawal.amount : -withdrawal.amount} tokens
                          </p>
                          {/* Assuming status would be part of reward object for withdrawals */}
                          <Badge variant="secondary" className="bg-gray-700 text-gray-300 border-gray-600">
                            {withdrawal.status || "Completed"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  {/* If no withdrawal history found */}
                  {!rewardData?.recentRewards?.filter(reward => reward.type === 'withdrawal').length && (
                    <div className="text-center py-8 text-gray-500">
                      <Wallet className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>No withdrawal history yet.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}