'use client';
import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:4000';

// TradingViewWidget is dynamically imported to avoid SSR issues
const TradingViewWidget = dynamic(
  () => import('@/components/TradingViewWidget'),
  { ssr: false }
);

const DUMMY_INITIAL_BALANCE = 100;

const TestnetHome = () => {
  const [tokens, setTokens] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [modal, setModal] = useState({ open: false, type: '', token: null });
  const [amount, setAmount] = useState('');
  const [price, setPrice] = useState('');
  const [txLoading, setTxLoading] = useState(false);
  const [txMessage, setTxMessage] = useState('');
  const [history, setHistory] = useState([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  // Phantom wallet state
  const [phantomAvailable, setPhantomAvailable] = useState(false);
  const [walletAddress, setWalletAddress] = useState('');
  const [solBalance, setSolBalance] = useState(DUMMY_INITIAL_BALANCE);
  const [walletError, setWalletError] = useState('');

  // Detect Phantom
  useEffect(() => {
    if (typeof window !== 'undefined' && window.solana && window.solana.isPhantom) {
      setPhantomAvailable(true);
      // Auto-connect if already connected
      window.solana.connect({ onlyIfTrusted: true }).then(({ publicKey }) => {
        if (publicKey) handleWalletConnect(publicKey.toString());
      }).catch(() => {});
    }
  }, []);

  // Load dummy balance from localStorage
  useEffect(() => {
    if (walletAddress) {
      const stored = localStorage.getItem(`dummy-sol-balance-${walletAddress}`);
      if (stored) setSolBalance(Number(stored));
      else setSolBalance(DUMMY_INITIAL_BALANCE);
    }
  }, [walletAddress]);

  // Save dummy balance to localStorage
  useEffect(() => {
    if (walletAddress) {
      localStorage.setItem(`dummy-sol-balance-${walletAddress}`, solBalance);
    }
  }, [solBalance, walletAddress]);

  const handleWalletConnect = async (address) => {
    setWalletAddress(address);
    setWalletError('');
  };

  const connectPhantom = async () => {
    setWalletError('');
    if (!phantomAvailable) {
      setWalletError('Phantom wallet not detected.');
      return;
    }
    try {
      const resp = await window.solana.connect();
      handleWalletConnect(resp.publicKey.toString());
    } catch (err) {
      setWalletError('Wallet connection failed.');
    }
  };

  useEffect(() => {
    fetch(`${BACKEND_URL}/trades/testnet/tokens`)
      .then(res => res.json())
      .then(setTokens)
      .catch(err => setError('Failed to fetch tokens'))
      .finally(() => setLoading(false));
  }, []);

  // Use dummy userId for demo
  const dummyUserId = 'demo-user-id';

  useEffect(() => {
    setHistoryLoading(true);
    fetch(`${BACKEND_URL}/trades/testnet/transactions?userId=${dummyUserId}`)
      .then(res => res.json())
      .then(setHistory)
      .catch(() => setHistory([]))
      .finally(() => setHistoryLoading(false));
  }, []);

  const openModal = (type, token) => {
    setModal({ open: true, type, token });
    setAmount('');
    setPrice(token.price || '');
    setTxMessage('');
    setWalletError('');
  };

  const closeModal = () => {
    setModal({ open: false, type: '', token: null });
    setAmount('');
    setPrice('');
    setTxMessage('');
    setWalletError('');
  };

  const handleTx = async () => {
    setTxLoading(true);
    setTxMessage('');
    setWalletError('');
    if (!walletAddress) {
      setWalletError('Please connect your Phantom wallet.');
      setTxLoading(false);
      return;
    }
    const totalCost = Number(amount) * Number(price);
    if (modal.type === 'buy' && totalCost > solBalance) {
      setWalletError('Not enough SOL balance.');
      setTxLoading(false);
      return;
    }
    try {
      const res = await fetch(`${BACKEND_URL}/trades/testnet/transaction`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tokenId: modal.token._id,
          userId: dummyUserId,
          userWallet: walletAddress,
          type: modal.type,
          amount: Number(amount),
          price: Number(price),
        }),
      });
      const data = await res.json();
      if (res.ok) {
        setTxMessage('Transaction successful!');
        // Update dummy balance
        if (modal.type === 'buy') setSolBalance(bal => bal - totalCost);
        else setSolBalance(bal => bal + totalCost);
        // Refresh history
        fetch(`${BACKEND_URL}/trades/testnet/transactions?userId=${dummyUserId}`)
          .then(res => res.json())
          .then(setHistory)
          .catch(() => {});
      } else {
        setTxMessage(data.message || 'Transaction failed');
      }
    } catch (err) {
      setTxMessage('Transaction error');
    } finally {
      setTxLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#0f1115] text-white p-6 font-sans w-[90%] mx-auto">
      <h1 className="text-3xl font-bold mb-8">Testnet Tokens</h1>
      {/* Wallet Connect Section */}
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-4">
          <img src="/phantom.svg" alt="Phantom" className="w-8 h-8" />
          {walletAddress ? (
            <span className="text-green-400 font-mono">{walletAddress.slice(0, 6)}...{walletAddress.slice(-4)}</span>
          ) : (
            <button onClick={connectPhantom} className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-semibold transition">Connect Phantom Wallet</button>
          )}
        </div>
        <div className="flex items-center gap-2">
          <span className="text-gray-400">Testnet SOL Balance:</span>
          <span className="text-yellow-400 font-bold">{solBalance.toFixed(2)} SOL</span>
        </div>
      </div>
      {walletError && <p className="text-red-500 mb-4">{walletError}</p>}
      {loading && <p>Loading tokens...</p>}
      {error && <p className="text-red-500">{error}</p>}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
        {/* Chart Section */}
        <div className="lg:col-span-7 bg-[#1e2128] rounded-2xl p-4 md:p-6 shadow-lg mb-8 lg:mb-0 flex flex-col items-center justify-center min-h-[400px]">
          {/* TradingView Widget for BTCUSD as placeholder */}
          <TradingViewWidget symbol="BTCUSD" />
        </div>
        {/* Buy/Sell Panel */}
        <div className="lg:col-span-5 flex flex-col gap-6">
          {tokens.map(token => (
            <div key={token._id} className="bg-[#23262f] rounded-2xl p-6 shadow-lg flex flex-col gap-4">
              <div className="flex items-center gap-4 mb-2">
                <img src={token.media || '/token.png'} alt={token.name} className="w-10 h-10 rounded-full bg-[#333] object-cover" />
                <div>
                  <h3 className="text-xl font-bold">{token.name} <span className="text-gray-400 font-normal">({token.symbol})</span></h3>
                  <p className="text-gray-400 text-sm">{token.description}</p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-lg font-semibold">Price:</span>
                <span className="text-green-400 text-lg">{token.price}</span>
              </div>
              <div className="flex gap-2 mt-2">
                <button onClick={() => openModal('buy', token)} className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-semibold transition">Buy</button>
                <button onClick={() => openModal('sell', token)} className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold transition">Sell</button>
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* Modal for Buy/Sell */}
      {modal.open && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-[#181a20] p-8 rounded-2xl min-w-[340px] max-w-[90vw] shadow-2xl">
            <h2 className="text-2xl font-bold mb-4">{modal.type === 'buy' ? 'Buy' : 'Sell'} {modal.token.name}</h2>
            <div className="mb-4">
              <label className="block text-gray-300 mb-1">Amount</label>
              <input type="number" value={amount} onChange={e => setAmount(e.target.value)} min="0" className="w-full p-2 rounded bg-[#23262f] text-white border border-[#333] focus:outline-none focus:ring-2 focus:ring-purple-500" />
            </div>
            <div className="mb-4">
              <label className="block text-gray-300 mb-1">Price</label>
              <input type="number" value={price} onChange={e => setPrice(e.target.value)} min="0" className="w-full p-2 rounded bg-[#23262f] text-white border border-[#333] focus:outline-none focus:ring-2 focus:ring-purple-500" />
            </div>
            <button onClick={handleTx} disabled={txLoading || !amount || !price} className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg font-semibold transition mb-2">
              {txLoading ? 'Processing...' : modal.type === 'buy' ? 'Buy' : 'Sell'}
            </button>
            <button onClick={closeModal} className="w-full bg-gray-700 hover:bg-gray-800 text-white py-2 rounded-lg font-semibold transition">Cancel</button>
            {txMessage && <p className="mt-3 text-center text-sm text-yellow-400">{txMessage}</p>}
            {walletError && <p className="mt-3 text-center text-sm text-red-500">{walletError}</p>}
          </div>
        </div>
      )}
      {/* Transaction History */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-4">Your Testnet Transaction History</h2>
        {historyLoading ? (
          <p>Loading history...</p>
        ) : history.length === 0 ? (
          <p className="text-gray-400">No transactions yet.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-[#181a20] rounded-2xl overflow-hidden">
              <thead>
                <tr>
                  <th className="px-4 py-2 text-left text-gray-400">Token</th>
                  <th className="px-4 py-2 text-left text-gray-400">Type</th>
                  <th className="px-4 py-2 text-left text-gray-400">Amount</th>
                  <th className="px-4 py-2 text-left text-gray-400">Price</th>
                  <th className="px-4 py-2 text-left text-gray-400">Total</th>
                  <th className="px-4 py-2 text-left text-gray-400">Time</th>
                </tr>
              </thead>
              <tbody>
                {history.map(tx => (
                  <tr key={tx._id} className="border-t border-[#23262f]">
                    <td className="px-4 py-2">{tx.token?.name || 'N/A'}</td>
                    <td className="px-4 py-2">{tx.type}</td>
                    <td className="px-4 py-2">{tx.amount}</td>
                    <td className="px-4 py-2">{tx.price}</td>
                    <td className="px-4 py-2">{tx.totalValue}</td>
                    <td className="px-4 py-2">{new Date(tx.timestamp).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestnetHome; 