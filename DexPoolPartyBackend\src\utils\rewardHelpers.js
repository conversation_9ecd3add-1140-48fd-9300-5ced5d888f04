const User = require("../models/User")
const Reward = require("../models/Reward")

/**
 * Generate a unique referral code
 */
async function generateUniqueReferralCode() {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
  let code
  let isUnique = false

  while (!isUnique) {
    code = ""
    for (let i = 0; i < 8; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length))
    }

    const existingUser = await User.findOne({ referralCode: code })
    if (!existingUser) {
      isUnique = true
    }
  }

  return code
}

/**
 * Award milestone bonus based on user activity
 */
async function checkMilestoneBonus(userId) {
  try {
    const user = await User.findOne({ _id: userId })
    if (!user) return

    const milestones = [
      { trades: 10, bonus: 50, description: "First 10 trades milestone" },
      { trades: 50, bonus: 200, description: "50 trades milestone" },
      { trades: 100, bonus: 500, description: "100 trades milestone" },
      { trades: 500, bonus: 2000, description: "500 trades milestone" },
    ]

    for (const milestone of milestones) {
      if (user.stats.totalTrades >= milestone.trades) {
        // Check if milestone already awarded
        const existingMilestone = await Reward.findOne({
          userId,
          type: "milestone_bonus",
          "metadata.milestone": milestone.description,
        })

        if (!existingMilestone) {
          // Award milestone bonus
          user.rewards.loyaltyBonus += milestone.bonus
          user.rewards.totalEarned += milestone.bonus
          user.rewards.availableBalance += milestone.bonus

          await user.save()

          await new Reward({
            userId,
            type: "milestone_bonus",
            amount: milestone.bonus,
            description: milestone.description,
            metadata: { milestone: milestone.description, trades: milestone.trades },
          }).save()

          console.log(`Milestone bonus awarded: ${milestone.description} - ${milestone.bonus} tokens`)
        }
      }
    }
  } catch (error) {
    console.error("Error checking milestone bonus:", error)
  }
}

/**
 * Calculate referral tier bonus based on number of referrals
 */
function calculateReferralTierBonus(referralCount) {
  if (referralCount >= 50) return 0.15 // 15% bonus for 50+ referrals
  if (referralCount >= 20) return 0.12 // 12% bonus for 20+ referrals
  if (referralCount >= 10) return 0.1 // 10% bonus for 10+ referrals
  if (referralCount >= 5) return 0.08 // 8% bonus for 5+ referrals
  return 0.05 // 5% base bonus
}

/**
 * Award loyalty bonus for active users
 */
async function awardLoyaltyBonus(userId, activityScore) {
  try {
    const user = await User.findOne({ _id: userId })
    if (!user) return

    let bonusAmount = 0
    let description = ""

    if (activityScore >= 100) {
      bonusAmount = 100
      description = "High activity loyalty bonus"
    } else if (activityScore >= 50) {
      bonusAmount = 50
      description = "Medium activity loyalty bonus"
    } else if (activityScore >= 25) {
      bonusAmount = 25
      description = "Basic activity loyalty bonus"
    }

    if (bonusAmount > 0) {
      user.rewards.loyaltyBonus += bonusAmount
      user.rewards.totalEarned += bonusAmount
      user.rewards.availableBalance += bonusAmount

      await user.save()

      await new Reward({
        userId,
        type: "loyalty_bonus",
        amount: bonusAmount,
        description,
        metadata: { activityScore },
      }).save()
    }
  } catch (error) {
    console.error("Error awarding loyalty bonus:", error)
  }
}

/**
 * Get user's referral statistics
 */
async function getReferralStats(userId) {
  try {
    const user = await User.findOne({ _id: userId }).populate("referrals.userId", "username stats")
    if (!user) return null

    const totalReferrals = user.referrals.length
    const activeReferrals = user.referrals.filter(
      (ref) => ref.userId && ref.userId.stats && ref.userId.stats.totalTrades > 0,
    ).length

    const totalReferralEarnings = user.referrals.reduce((sum, ref) => sum + ref.totalRewardEarned, 0)

    return {
      totalReferrals,
      activeReferrals,
      totalReferralEarnings,
      referralTierBonus: calculateReferralTierBonus(totalReferrals),
    }
  } catch (error) {
    console.error("Error getting referral stats:", error)
    return null
  }
}

module.exports = {
  generateUniqueReferralCode,
  checkMilestoneBonus,
  calculateReferralTierBonus,
  awardLoyaltyBonus,
  getReferralStats,
}
