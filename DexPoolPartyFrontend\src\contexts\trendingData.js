// context/TrendingContext.js (Create this file if it doesn't exist, or ensure it's correct)

'use client';
import { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { fetchTrendingFromBitquery, fetchMnesFromBitquery } from '../lib/fetchTrendingFromBitquery'; // Adjust path as needed

// Initialize createContext with a default value (e.g., undefined)
// This can help with TypeScript but also makes it clear no default context exists.
const TrendingContext = createContext(undefined);

export const TrendingProvider = ({ children }) => {
  const [trendingData, setTrendingData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [mnesData, setMnesData] = useState([]);
  const [loadingMnes, setLoadingMnes] = useState(true);
  const [errorMnes, setErrorMnes] = useState(null);

  const refreshTrending = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchTrendingFromBitquery();
      
      setTrendingData(data);
    } catch (err) {
      console.error("Error in refreshTrending:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshMnes = useCallback(async () => {
    setLoadingMnes(true);
    setErrorMnes(null);
    try {
      const data = await fetchMnesFromBitquery();
      setMnesData(data);
    } catch (err) {
      console.error("Error in refreshMnes:", err);
      setErrorMnes(err.message);
    } finally {
      setLoadingMnes(false);
    }
  }, []);

  useEffect(() => {
    refreshTrending();
    refreshMnes();
  }, [refreshTrending, refreshMnes]);

  return (
    <TrendingContext.Provider
      value={{
        trendingData,
        loading,
        error,
        refreshTrending,
        mnesData,
        loadingMnes,
        errorMnes,
        refreshMnes,
      }}
    >
      {children}
    </TrendingContext.Provider>
  );
};

export const useTrending = () => {
  const context = useContext(TrendingContext);
  if (context === undefined) {
    throw new Error('useTrending must be used within a TrendingProvider');
  }
  return context;
};