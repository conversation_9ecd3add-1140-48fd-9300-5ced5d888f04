'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { showToast } from '@/utils/toast';
import { useAuth } from '@/contexts/AuthContext';
import { Heart } from 'lucide-react';

export default function CommentSection({ tokenId, initialComments = [] }) {
  const { user } = useAuth();
  const [comments, setComments] = useState(initialComments);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    const fetchComments = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}/tokens/${tokenId}/comments`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('authToken')}`,
            },
          }
        );
        if (!response.ok) {
          throw new Error('Failed to fetch comments');
        }
        const { comments: fetchedComments } = await response.json();
        setComments(fetchedComments);
      } catch (error) {
        console.error('Error fetching comments:', error);
        showToast.error('Failed to load comments');
      } finally {
        setLoading(false);
      }
    };

    fetchComments();
  }, [tokenId]);

  const handleAddComment = async (e) => {
    e.preventDefault();
    
    if (!user) {
      showToast.error('Please login to comment');
      return;
    }
    
    if (!newComment.trim()) {
      showToast.error('Comment cannot be empty');
      return;
    }

    try {
      setSubmitting(true);
      const token = localStorage.getItem('authToken')
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/tokens/${tokenId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          content: newComment.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add comment');
      }

      const { comment } = await response.json();
      setComments(prevComments => [comment, ...prevComments]);
      setNewComment('');
      showToast.success('Comment added successfully');
    } catch (error) {
      console.error('Error adding comment:', error);
      showToast.error('Failed to add comment');
    } finally {
      setSubmitting(false);
    }
  };

  const handleLike = async (commentId, liked) => {
    if (!user) return;
    const url = `${process.env.NEXT_PUBLIC_BACKEND_URL}/tokens/${tokenId}/comments/${commentId}/${liked ? 'unlike' : 'like'}`;
    const res = await fetch(url, {
      method: 'POST',
      headers: { Authorization: `Bearer ${localStorage.getItem('authToken')}` },
    });
    const data = await res.json();
    setComments(comments =>
      comments.map(c =>
        c._id === commentId
          ? { ...c, likes: liked ? c.likes.filter(id => id !== user.id) : [...(c.likes || []), user.id] }
          : c
      )
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Comment Form */}
      <form onSubmit={handleAddComment} className="space-y-4">
        <div className="flex gap-3">
          <Image
            src={user?.profileImage || '/king.png'}
            alt={user?.username || 'User'}
            width={40}
            height={40}
            className="rounded-full w-10 h-10"
          />
          <div className="flex-1">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder={user ? "Write a comment..." : "Please login to comment"}
              disabled={!user || submitting}
              className="w-full h-24 p-3 bg-[#252830] border border-[#353545] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 disabled:opacity-50"
            />
            <div className="flex justify-end mt-2">
              <button
                type="submit"
                disabled={!user || submitting}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {submitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Posting...
                  </>
                ) : (
                  'Post Comment'
                )}
              </button>
            </div>
          </div>
        </div>
      </form>

      {/* Comments List */}
      <div className="space-y-6">
        {comments.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-400">No comments yet</p>
            <p className="text-sm text-gray-500 mt-1">Be the first to share your thoughts!</p>
          </div>
        ) : (
          comments.map((comment) => {
            const liked = comment.likes?.includes(user?.id);
            return (
              <div key={comment._id} className="flex gap-3">
                <Image
                  src={comment.profileImage || '/king.png'}
                  alt={comment.username}
                  width={40}
                  height={40}
                  className="rounded-full w-10 h-10"
                />
                <div className="flex-1">
                  <div className="bg-[#252830] rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{comment.username}</span>
                      <span className="text-xs text-gray-400">
                        {formatDate(comment.createdAt)}
                      </span>
                      <button
                        onClick={() => handleLike(comment._id, liked)}
                        className={`flex items-center gap-1 ml-2 ${liked ? 'text-red-500' : 'text-gray-400'} hover:text-red-400 transition`}
                        disabled={!user}
                        title={liked ? 'Unlike' : 'Like'}
                      >
                        <Heart fill={liked ? 'currentColor' : 'none'} className="w-4 h-4" />
                        <span className="text-xs">{comment.likes?.length || 0}</span>
                      </button>
                    </div>
                    <p className="text-gray-300 whitespace-pre-wrap">{comment.content}</p>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}