"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { API_BASE_URL } from "@/services/api";
import { showToast } from '@/utils/toast';
import { useAuth } from "@/contexts/AuthContext";

export default function VerifyEmailPage() {
  const { user: contextUser, token: contextToken, updateUser } = useAuth();
  const router = useRouter();

  const [user, setUser] = useState(contextUser || null);
  const [token, setToken] = useState(contextToken || null);
  const [status, setStatus] = useState("Verifying...");
  const [loading, setLoading] = useState(true);
  const [verificationTried, setVerificationTried] = useState(false); // 🛑 prevent loop

  useEffect(() => {
    // Load user/token from localStorage if not in context
    if (!user || !token) {
      try {
        const storedUser = localStorage.getItem("user");
        const storedToken = localStorage.getItem("authToken");
        if (storedUser && storedToken) {
          setUser(JSON.parse(storedUser));
          setToken(storedToken);
        } else {
          showToast.warning("Please log in first.");
          router.push("/");
        }
      } catch (e) {
        console.error("Failed to parse user/token from localStorage", e);
        showToast.warning("Please log in first.");
        router.push("/");
      } finally {
        setLoading(false);
      }
    }
  }, []);

  useEffect(() => {
    // Prevent re-verification if already attempted
    if (!user || !token || verificationTried) return;

    const verifyEmail = async () => {
      setVerificationTried(true); // ✅ Set flag to avoid repeated attempts

      try {
        if (typeof user.reload === "function") {
          await user.reload();
        }

        if (user.emailVerified) {
          setStatus("Email already verified. Redirecting...");
          showToast.dismiss && showToast.dismiss(); // clear loading/toasts
          showToast.info("Email is already verified.");
          setTimeout(() => router.push("/profile"), 2000);
          return;
        }

        setStatus("Verifying your email...");
        showToast.dismiss && showToast.dismiss();
        // Optionally show a loading toast if desired

        const response = await fetch(`${API_BASE_URL}/users/confirm-email`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });

        showToast.dismiss && showToast.dismiss();

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to confirm email");
        }

        const { user: updatedUser, token: newToken } = await response.json();

        updateUser(updatedUser, newToken);
        localStorage.setItem("user", JSON.stringify(updatedUser));
        localStorage.setItem("authToken", newToken);

        setStatus("Email verified! Redirecting...");
        showToast.success("Email verified successfully!");
        setTimeout(() => router.push("/profile"), 2000);
      } catch (error) {
        console.error("Verification error:", error);
        showToast.dismiss && showToast.dismiss();
        setStatus("An error occurred while verifying your email.");
        showToast.error("Error verifying email.");
      } finally {
        setLoading(false);
      }
    };

    verifyEmail();
  }, [user, token, verificationTried, updateUser, router]);

  return (
    <div className="flex items-center justify-center h-screen">
      {loading ? (
        <div className="flex flex-col items-center space-y-4">
          <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-blue-600">{status}</p>
        </div>
      ) : (
        <div className="flex flex-col items-center space-y-4">
          <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-blue-700">{status}</p>
        </div>

      )}
    </div>
  );
}
