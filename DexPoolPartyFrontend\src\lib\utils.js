import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export function getImageUrl(mediaPath) {
  if (!mediaPath) return '/king.png';
  if (mediaPath.startsWith('http') || mediaPath.startsWith('data:')) return mediaPath;
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:4000/api';
  const backendRoot = backendUrl.replace(/\/api$/, '');
  const normalizedPath = mediaPath.replace(/\\/g, '/');
  return `${backendRoot}/uploads/${normalizedPath}`;
}
