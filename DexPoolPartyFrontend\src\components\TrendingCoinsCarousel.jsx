'use client';
import { useTrending } from '@/contexts/trendingData.js';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from './ui/carousel';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { getImageUrl } from '@/lib/utils';
import { useRouter } from 'next/navigation';

export default function TrendingCoinsCarousel() {
  const { trendingData, loading, error, refreshData } = useTrending();
  console.log(trendingData);
  
  const [resolvedImages, setResolvedImages] = useState({});
  const router = useRouter();
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 300000); // 5 minutes
    return () => clearInterval(interval);
  }, [refreshData]);

  useEffect(() => {
    if (!trendingData || trendingData.length === 0) {
      setResolvedImages({});
      return;
    }

    // Helper: fetch metadata from logo and get image URL
    async function fetchMetadata(uri) {
      try {
        const res = await fetch(uri);
        if (!res.ok) throw new Error('Network response not ok');
        const json = await res.json();
        return json.image || null;
      } catch {
        return null;
      }
    }

    async function fetchAllMetadata() {
      const newImages = {};
      await Promise.all(
        trendingData.map(async (coin) => {
          if (coin.logo && coin.logo.startsWith('http')) {
            const imgUrl = await fetchMetadata(coin.logo);
            newImages[coin.id] = imgUrl;
          } else {
            newImages[coin.id] = null;
          }
        })
      );
      setResolvedImages(newImages);
    }

    fetchAllMetadata();
  }, [trendingData]);


  const formatNumber = (num) => {
    const parsedNum = Number(num);
    if (!isFinite(parsedNum)) return 'N/A';
    if (parsedNum < 0.01) return parsedNum.toFixed(6);
    if (parsedNum < 1000) return parsedNum.toFixed(2);
    return new Intl.NumberFormat('en-US', {
      notation: 'compact',
      maximumFractionDigits: 1,
    }).format(parsedNum);
  };

  const skeletonCards = Array.from({ length: 4 }).map((_, idx) => (
    <CarouselItem
      key={idx}
      className="md:basis-1/2 lg:basis-1/3 xl:basis-1/4 basis-2/3"
    >
      <div className="flex flex-row w-full gap-3 p-4 bg-[#1e1e1e] rounded-xl animate-pulse">
        <div className="w-[74px] h-[74px] bg-gray-700 rounded-md flex-shrink-0" />
        <div className="flex flex-col flex-1 gap-3">
          <div className="h-4 bg-gray-700 rounded w-1/2" />
          <div className="h-4 bg-gray-700 rounded w-3/4" />
          <div className="h-4 bg-gray-700 rounded w-1/3" />
          <div className="flex gap-2">
            <div className="h-3 w-5 bg-gray-700 rounded" />
            <div className="h-3 w-20 bg-gray-700 rounded" />
          </div>
        </div>
      </div>
    </CarouselItem>
  ));

  if (loading) {
    return (
      <div className="px-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">Trending Coins</h2>
        </div>
        <Carousel opts={{ align: 'start', loop: true }} className="w-full max-w-7xl mx-auto">
          <CarouselContent>{skeletonCards}</CarouselContent>
        </Carousel>
      </div>
    );
  }

  if (error) {
    return (
      <div className="px-4 py-8 text-red-500 text-center">
        Error loading data: {error}
        <button
          onClick={refreshData}
          className="ml-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!trendingData || trendingData.length === 0) {
    return (
      <div className="px-4 py-8 text-center text-gray-400">
        No trending coins found
        <button
          onClick={refreshData}
          className="ml-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh
        </button>
      </div>
    );
  }

  return (
    <div className="px-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-white">Trending Coins</h2>
        <button
          onClick={refreshData}
          className="px-3 py-1 text-sm bg-[#1C2530] text-gray-300 rounded hover:bg-[#2F3741]"
        >
          Refresh
        </button>
      </div>

      <Carousel opts={{ align: 'start', loop: true }} className="w-full max-w-7xl mx-auto">
        <CarouselContent>
          {trendingData.map((coin, idx) => {
            // Use resolved image URL if available, else fallback to /token.png
            const imageUrl = getImageUrl(resolvedImages[coin.id] || coin.logo);

            return (
              <CarouselItem key={idx} className="md:basis-1/2 lg:basis-1/3 xl:basis-1/4 basis-2/3 p-2">
                <Link href={`/${coin.id}`} className="group transition-all">
                  <div className="flex flex-row items-center gap-4 p-4 bg-[#1e1e1e] hover:bg-[#2a2a2a] rounded-xl transition-colors duration-300 shadow-md cursor-pointer">
                    <div className="w-[74px] h-[74px] relative flex-shrink-0">
                      <Image
                        alt={coin.name}
                        width={68}
                        height={68}
                        src={imageUrl}
                        className="rounded-lg object-cover w-full h-full border border-gray-700"
                        unoptimized // to avoid Next.js image check loop for IPFS or unknown
                        onError={() => { }}
                      />
                    </div>

                    <div className="flex flex-col flex-1 min-w-0">
                      <div className="flex justify-between items-center gap-2">
                        <h3 className="text-lg font-semibold text-white truncate">{coin.symbol}</h3>
                        <p className="text-sm text-yellow-400 font-medium truncate max-w-[100px] text-right">
                          ${formatNumber(coin.quote?.USD?.market_cap)}
                        </p>
                      </div>

                      <p className="text-sm text-gray-400 truncate">{coin.name}</p>

                      <div className="flex justify-between items-center mt-1 gap-2">
                        <p
                          className={`text-sm font-medium truncate ${coin.quote?.USD?.percent_change_24h >= 0 ? 'text-green-400' : 'text-red-500'
                            }`}
                        >
                          ${formatNumber(coin.quote?.USD?.price)}
                        </p>
                        <p className="text-sm text-gray-400 truncate max-w-[80px] text-right">
                          Vol: ${formatNumber(coin.quote?.USD?.volume_24h)}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <CarouselPrevious className="ml-3 bg-[#1e1e1e] text-white hover:text-white hover:bg-[#2a2a2a]" />
        <CarouselNext className="mr-3 bg-[#1e1e1e] text-white hover:text-white hover:bg-[#2a2a2a]" />
      </Carousel>
    </div>
  );
}
