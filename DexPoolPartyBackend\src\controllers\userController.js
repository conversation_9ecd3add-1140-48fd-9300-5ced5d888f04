const User = require("../models/user.model");
const admin = require("firebase-admin");
const jwt = require("jsonwebtoken");
const path = require("path");
const fs = require("fs");
const { default: mongoose } = require("mongoose");

// Initialize Firebase Admin SDK (should be done once in app initialization)
let firebaseInitialized = false;

const initializeFirebase = () => {
  if (!firebaseInitialized) {
    try {
      const serviceAccount = require("../config/firebase-dev.json");
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
      firebaseInitialized = true;
      console.log("Firebase Admin SDK initialized successfully");
    } catch (error) {
      console.error("Firebase initialization error:", error);
      throw new Error("Failed to initialize Firebase");
    }
  }
};

// Initialize Firebase on module load
initializeFirebase();

// Helper function to verify Firebase ID token
const verifyFirebaseToken = async (idToken) => {
  try {
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    console.error("Firebase token verification error:", error);
    throw new Error("Invalid Firebase token");
  }
};

const syncUserWithMongoDB = async (firebaseUser, additionalData = {}) => {
  try {
    console.log(
      `🔍 [SYNC] Looking for user with Firebase UID: ${firebaseUser.uid}, email: ${firebaseUser.email}`
    );

    let user = await User.findOne({
      $or: [{ _id: firebaseUser.uid }, { email: firebaseUser.email }],
    });

    if (user) {
      console.log(`👤 [SYNC] Found existing user:`, {
        id: user._id,
        email: user.email,
        username: user.username,
        hasCustomUsername: user.hasCustomUsername,
        authProvider: user.authProvider,
      });
    }

    if (!user) {
      const userData = {
        _id: firebaseUser.uid,
        email: firebaseUser.email,
        username:
          firebaseUser.name ||
          firebaseUser.display_name ||
          firebaseUser.email?.split("@")[0] ||
          "user",
        profileImage: firebaseUser.picture,
        emailVerified: firebaseUser.email_verified || false,
        authProvider: additionalData.authProvider || "firebase",
        isActive: true,
        status: "active",
        createdAt: new Date(),
        ...additionalData,
      };

      user = new User(userData);
      console.log("Creating new user:", {
        email: userData.email,
        authProvider: userData.authProvider,
        mongoId: userData._id,
      });
    } else {
      if (user._id !== firebaseUser.uid) {
        console.warn(
          `MongoDB _id (${user._id}) and Firebase UID (${firebaseUser.uid}) mismatch for user ${firebaseUser.email}. Updating existing record to match Firebase UID.`
        );
        user.firebaseUid = firebaseUser.uid;
      }

      user.emailVerified = firebaseUser.email_verified || user.emailVerified;

      // Migration logic: detect if user has likely customized their username
      // Always check if hasCustomUsername field exists and set it properly
      const googleName = firebaseUser.name || firebaseUser.display_name;

      if (user.hasCustomUsername === undefined || user.hasCustomUsername === null) {
        // Field doesn't exist, determine if username is custom
        if (googleName && user.username && user.username !== googleName) {
          // Username differs from Google name, likely customized
          user.hasCustomUsername = true;
          console.log(
            `🔧 [MIGRATION] User ${user.email} has custom username: "${user.username}" vs Google: "${googleName}" - Setting hasCustomUsername=true`
          );
        } else {
          user.hasCustomUsername = false;
          console.log(
            `🔧 [MIGRATION] User ${user.email} using Google username: "${googleName}" - Setting hasCustomUsername=false`
          );
        }
      } else {
        console.log(
          `ℹ️ [SYNC] User ${user.email} hasCustomUsername already set: ${user.hasCustomUsername}`
        );
      }

      // Only update username from Google if user hasn't customized it
      const googleName = firebaseUser.name || firebaseUser.display_name;
      if (googleName && !user.hasCustomUsername) {
        console.log(
          `🔄 [SYNC] Updating username from Google: "${user.username}" -> "${googleName}"`
        );
        user.username = googleName;
      } else if (googleName && user.hasCustomUsername) {
        console.log(
          `🚫 [SYNC] Preserving custom username: "${user.username}" (Google: "${googleName}")`
        );
      }
      if (firebaseUser.picture) user.profileImage = firebaseUser.picture;
      user.lastLoginAt = new Date();
      Object.assign(user, additionalData);
      console.log("Updating existing user:", {
        email: user.email,
        id: user._id,
      });
    }

    await user.save();
    console.log(
      `💾 [SAVE] User saved with username: "${user.username}", hasCustomUsername: ${user.hasCustomUsername}`
    );
    return user;
  } catch (error) {
    console.error("Error syncing user with MongoDB:", error);
    throw error;
  }
};

// Helper function to generate JWT token
const generateJWTToken = (user, expiresIn = "7d") => {
  console.log("🔑 Generating JWT token for user:", {
    id: user._id,
    email: user.email,
    authProvider: user.authProvider,
  });

  // Now that _id and firebaseUid are the same, we can simply send the _id in the JWT
  // and it implicitly serves as the Firebase UID.
  const tokenPayload = {
    id: user._id, // This is the user's MongoDB _id, which is also their Firebase UID
    email: user.email,
    authProvider: user.authProvider,
    // You can add more data here if necessary, but keep it minimal for security
    isWalletConnected: user.isWalletConnected,
    walletAddress: user.walletAddress,
    emailVerified: user.emailVerified,
  };

  const token = jwt.sign(tokenPayload, process.env.JWT_SECRET, { expiresIn });

  return token;
};

// Helper function to format user response
const formatUserResponse = (user) => {
  const response = {
    id: user._id,
    firebaseUid: user._id, // Explicitly use _id as firebaseUid in the response
    email: user.email,
    username: user.username,
    bio: user.bio,
    socialLinks: user.socialLinks,
    profileImage: user.profileImage,
    walletAddress: user.walletAddress,
    authProvider: user.authProvider,
    emailVerified: user.emailVerified,
    isWalletConnected: user.isWalletConnected,
    isActive: user.isActive,
    status: user.status,
    createdAt: user.createdAt,
    lastLoginAt: user.lastLoginAt,
  };
  return response;
};

// CRUD Operations
exports.getAllUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, status = "active" } = req.query;
    const skip = (page - 1) * limit;

    const query = status !== "all" ? { status } : {};

    const users = await User.find(query)
      .skip(skip)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 })
      .select("-__v");

    const total = await User.countDocuments(query);

    res.json({
      users: users.map(formatUserResponse),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalUsers: total,
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Get all users error:", error);
    res
      .status(500)
      .json({ message: "Error fetching users", error: error.message });
  }
};

exports.updateUserProfile = async (req, res) => {
  try {
    const id = req.user.id;

    if (!id) {
      return res.status(401).json({ message: "User ID not found in token" });
    }

    const updates = { ...req.body };

    // Parse socialLinks if it exists and is a string
    if (updates.socialLinks && typeof updates.socialLinks === "string") {
      try {
        updates.socialLinks = JSON.parse(updates.socialLinks);
      } catch (err) {
        return res.status(400).json({ message: "Invalid socialLinks format" });
      }
    }

    // ✅ Parse socialLinks if it exists and is a string
    if (updates.socialLinks && typeof updates.socialLinks === "string") {
      try {
        updates.socialLinks = JSON.parse(updates.socialLinks);
      } catch (err) {
        return res.status(400).json({ message: "Invalid socialLinks format" });
      }
    }

    const user = await User.findOne({ _id: id });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // ✅ Handle profile image upload or clear
    if (req.file) {
      if (user.profileImage) {
        const oldImagePath = path.join(process.cwd(), user.profileImage);
        fs.unlink(oldImagePath, (err) => {
          if (err) {
            console.error(
              `Failed to delete old profile image: ${oldImagePath}`,
              err
            );
          } else {
            console.log(`Old profile image deleted: ${oldImagePath}`);
          }
        });
      }
      updates.profileImage = `uploads/${req.file.filename}`;
    } else if (updates.profileImage === "") {
      if (user.profileImage) {
        const oldImagePath = path.join(process.cwd(), user.profileImage);
        fs.unlink(oldImagePath, (err) => {
          if (err) {
            console.error(
              `Failed to delete old profile image on clear: ${oldImagePath}`,
              err
            );
          } else {
            console.log(`Old profile image deleted on clear: ${oldImagePath}`);
          }
        });
      }
      updates.profileImage = "";
    }

    // Mark username as custom if it's being updated
    if (updates.username) {
      updates.hasCustomUsername = true;
      console.log(
        `✏️ [PROFILE UPDATE] Setting hasCustomUsername=true for user ${id}, new username: "${updates.username}"`
      );
    }

    updates.updatedAt = new Date();

    console.log(`🔄 [UPDATE] About to update user ${id} with:`, updates);

    const updatedUser = await User.findByIdAndUpdate(id, updates, {
      new: true,
      runValidators: true,
    });

    if (!updatedUser) {
      return res
        .status(404)
        .json({ message: "User not found after update attempt" });
    }

    console.log(`✅ [UPDATE] User updated successfully:`, {
      id: updatedUser._id,
      username: updatedUser.username,
      hasCustomUsername: updatedUser.hasCustomUsername,
      email: updatedUser.email,
    });

    const formattedUser = formatUserResponse(updatedUser);

    res.json({
      message: "Profile updated successfully",
      user: formattedUser,
    });
  } catch (error) {
    console.error("Update user profile error:", error);
    res
      .status(500)
      .json({ message: "Error updating profile", error: error.message });
  }
};

exports.getUserById = async (req, res) => {
  try {
    const user = await User.findOne({ _id: req.params.id }).select("-__v");
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.json(formatUserResponse(user));
  } catch (error) {
    console.error("Get user by ID error:", error);
    res
      .status(500)
      .json({ message: "Error fetching user", error: error.message });
  }
};

exports.updateUser = async (req, res) => {
  try {
    const allowedUpdates = ["username", "profileImage", "bio", "preferences"];
    const updates = {};

    // Filter only allowed updates
    Object.keys(req.body).forEach((key) => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    if (Object.keys(updates).length === 0) {
      return res.status(400).json({ message: "No valid updates provided" });
    }

    // Mark username as custom if it's being updated
    if (updates.username) {
      updates.hasCustomUsername = true;
      console.log(
        `✏️ [USER UPDATE] Setting hasCustomUsername=true for user ${req.params.id}, new username: "${updates.username}"`
      );
    }

    updates.updatedAt = new Date();

    const user = await User.findOneAndUpdate({ _id: req.params.id }, updates, {
      new: true,
      runValidators: true,
    }).select("-__v");

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    res.json({
      message: "User updated successfully",
      user: formatUserResponse(user),
    });
  } catch (error) {
    console.error("Update user error:", error);
    res
      .status(500)
      .json({ message: "Error updating user", error: error.message });
  }
};

exports.softDeleteUser = async (req, res) => {
  try {
    const user = await User.findOneAndUpdate(
      { _id: req.params.id },
      {
        status: "inactive",
        isActive: false,
        deactivatedAt: new Date(),
      },
      { new: true }
    ).select("-__v");

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    res.json({
      message: "User deactivated successfully",
      user: formatUserResponse(user),
    });
  } catch (error) {
    console.error("Soft delete user error:", error);
    res
      .status(500)
      .json({ message: "Error deactivating user", error: error.message });
  }
};

// Authentication Methods

// Google OAuth with Firebase
exports.loginWithGoogle = async (req, res) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({ message: "Firebase ID token is required" });
    }

    // Verify Firebase token
    const decodedToken = await verifyFirebaseToken(idToken);

    // Check if email is verified
    if (!decodedToken.email_verified) {
      return res.status(403).json({
        message: "Email not verified. Please verify your email address first.",
        emailVerified: false,
        needsVerification: true,
      });
    }

    // Sync user with MongoDB
    const user = await syncUserWithMongoDB(decodedToken, {
      authProvider: "google",
    });

    // Generate JWT token
    const appToken = generateJWTToken(user);

    console.log("🎉 Google login successful for user:", user.email);
    console.log("Response includes JWT token for API authentication");

    res.json({
      message: "Google login successful",
      token: appToken,
      firebaseToken: idToken,
      user: formatUserResponse(user),
    });
  } catch (error) {
    console.error("Google login error:", error);
    res.status(500).json({
      message: "Google authentication failed",
      error: error.message,
    });
  }
};

exports.loginWithApple = async (req, res) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({ message: "Firebase ID token is required" });
    }

    console.log("🍎 [APPLE] Starting Apple authentication process");

    const decodedToken = await verifyFirebaseToken(idToken);
    console.log("🍎 [APPLE] Firebase token verified:", {
      uid: decodedToken.uid,
      email: decodedToken.email,
      emailVerified: decodedToken.email_verified,
      provider: decodedToken.firebase?.sign_in_provider,
    });

    // Apple Sign-In users typically have verified emails, but let's check
    if (!decodedToken.email_verified) {
      console.log("🍎 [APPLE] Email not verified for Apple user");
      return res.status(403).json({
        message:
          "Email verification required. Please verify your email address.",
        emailVerified: false,
        needsVerification: true,
      });
    }

    // Extract user data from Apple authentication
    const userData = {
      authProvider: "apple",
      emailVerified: decodedToken.email_verified,
      lastLoginAt: new Date(),
    };

    // Handle Apple-specific user data
    if (decodedToken.name) {
      userData.username = decodedToken.name;
    } else if (decodedToken.display_name) {
      userData.username = decodedToken.display_name;
    }

    // Apple doesn't always provide a profile picture, but we can check
    if (decodedToken.picture) {
      userData.profileImage = decodedToken.picture;
    }

    const user = await syncUserWithMongoDB(decodedToken, userData);
    console.log("🍎 [APPLE] User synced with MongoDB:", {
      userId: user._id,
      email: user.email,
      username: user.username,
    });

    const appToken = generateJWTToken(user);

    console.log("🎉 Apple login successful for user:", user.email);
    console.log("Response includes JWT token for API authentication");

    res.json({
      message: "Signed in with Apple successfully",
      token: appToken,
      firebaseToken: idToken,
      user: formatUserResponse(user),
    });
  } catch (error) {
    console.error("🍎 [APPLE] Apple login error:", error);

    // Handle specific Firebase authentication errors
    if (error.message.includes("Invalid Firebase token")) {
      return res.status(401).json({
        message: "Invalid authentication token. Please try signing in again.",
        error: "INVALID_TOKEN",
      });
    }

    if (error.message.includes("Firebase token verification error")) {
      return res.status(401).json({
        message:
          "Authentication token expired or invalid. Please sign in again.",
        error: "TOKEN_EXPIRED",
      });
    }

    res.status(500).json({
      message: "Apple authentication failed. Please try again.",
      error: error.message,
    });
  }
};

exports.signupWithEmail = async (req, res) => {
  try {
    const { idToken, isNewUser } = req.body; // Receive the isNewUser flag

    if (!idToken) {
      return res.status(400).json({ message: "Firebase ID token is required" });
    }

    // Verify Firebase token
    const decodedToken = await verifyFirebaseToken(idToken);

    let user;
    let message;

    if (isNewUser) {
      // Frontend tried to sign up (because signIn failed with user-not-found)
      // Create new user in MongoDB
      user = await syncUserWithMongoDB(decodedToken, { authProvider: "email" });
      message = "Account created successfully!";

      // If the new account's email is not verified, return 403 to frontend
      if (!decodedToken.email_verified) {
        return res.status(403).json({
          message:
            "Account created! Please verify your email address to complete registration. Check your inbox for the verification link.",
          emailVerified: false,
          needsVerification: true,
        });
      }

      // If new user and email is already verified (e.g., via other Firebase methods), log them in
      const appToken = generateJWTToken(user);
      res.status(201).json({
        message: "Account created and signed in successfully!",
        token: appToken,
        firebaseToken: idToken,
        user: formatUserResponse(user),
      });
    } else {
      // If isNewUser is false, it means the frontend successfully signed in an existing user
      // with Firebase. We just need to sync with MongoDB and check verification status.

      user = await syncUserWithMongoDB(decodedToken, { authProvider: "email" });
      message = "Signed in successfully!";

      if (!decodedToken.email_verified) {
        // If an existing user logs in but their email is not verified,
        // we should also inform them and return 403.
        return res.status(403).json({
          message:
            "Please verify your email address before signing in. A new verification link has been sent.",
          emailVerified: false,
          needsVerification: true,
        });
      }

      const appToken = generateJWTToken(user);

      res.status(200).json({
        message: message,
        token: appToken,
        firebaseToken: idToken,
        user: formatUserResponse(user),
      });
    }
  } catch (error) {
    console.error("Email signup/login error:", error.message);
    res.status(500).json({
      message: "Authentication failed",
      error: error.message,
    });
  }
};

// Resend verification email
exports.resendVerificationEmail = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: "Email is required" });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: "Invalid email format" });
    }

    // Get the user from Firebase Auth
    const userRecord = await admin.auth().getUserByEmail(email);

    if (!userRecord) {
      return res.status(404).json({ message: "User not found" });
    }

    if (userRecord.emailVerified) {
      return res.status(400).json({ message: "Email is already verified" });
    }

    // Generate email verification link
    const actionCodeSettings = {
      url:
        process.env.EMAIL_VERIFICATION_REDIRECT_URL ||
        "http://localhost:3000/verify-email",
      handleCodeInApp: false,
    };

    const verificationLink = await admin
      .auth()
      .generateEmailVerificationLink(email, actionCodeSettings);

    // Here you would typically send the email using your email service
    // For now, we'll just return success
    console.log("Verification link generated:", verificationLink);

    res.json({
      message: "Verification email sent successfully! Please check your inbox.",
      success: true,
    });
  } catch (error) {
    console.error("Resend verification error:", error);

    if (error.code === "auth/user-not-found") {
      return res
        .status(404)
        .json({ message: "No account found with this email address" });
    }

    if (error.errorInfo?.message?.includes("TOO_MANY_ATTEMPTS_TRY_LATER")) {
      return res.status(429).json({
        message:
          "Too many requests. Please wait a few minutes before trying again.",
      });
    }

    res.status(500).json({
      message: "Failed to resend verification email",
      error: error.message,
    });
  }
};

// Logout user (revoke refresh tokens)
exports.logout = async (req, res) => {
  try {
    // Assuming req.user.id is the MongoDB _id, which is now firebaseUid
    const firebaseUid = req.user.id;

    if (firebaseUid) {
      // Revoke all refresh tokens for the user
      await admin.auth().revokeRefreshTokens(firebaseUid);
      console.log("Refresh tokens revoked for user:", firebaseUid);
    }

    res.json({
      message: "Logged out successfully",
      success: true,
    });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({
      message: "Logout failed",
      error: error.message,
    });
  }
};

// Verify JWT token and get current user
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await User.findOne({ _id: req.user.id }).select("-__v");

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    if (!user.isActive) {
      return res.status(403).json({ message: "Account is deactivated" });
    }

    res.json({
      user: formatUserResponse(user),
    });
  } catch (error) {
    console.error("Get current user error:", error);
    res.status(500).json({
      message: "Error fetching user data",
      error: error.message,
    });
  }
};

// Refresh JWT token
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ message: "Refresh token is required" });
    }

    // Verify the refresh token with Firebase
    const decodedToken = await admin.auth().verifyIdToken(refreshToken);

    // Get user from database using the UID from the decoded token
    const user = await User.findOne({ _id: decodedToken.uid }); // Use decodedToken.uid as _id

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    if (!user.isActive) {
      return res.status(403).json({ message: "Account is deactivated" });
    }

    // Generate new JWT token
    const newAppToken = generateJWTToken(user);

    res.json({
      message: "Token refreshed successfully",
      token: newAppToken,
      user: formatUserResponse(user),
    });
  } catch (error) {
    console.error("Refresh token error:", error);
    res.status(401).json({
      message: "Invalid refresh token",
      error: error.message,
    });
  }
};

// Delete user account permanently
exports.deleteAccount = async (req, res) => {
  try {
    const userId = req.params.id || req.user.id;

    // Get user from database
    const user = await User.findOne({ _id: userId });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Delete user from Firebase Auth using the MongoDB _id (which is firebaseUid)
    try {
      await admin.auth().deleteUser(user._id); // Use user._id as Firebase UID
      console.log("User deleted from Firebase Auth:", user._id);
    } catch (firebaseError) {
      console.error("Firebase user deletion error:", firebaseError);
      // Continue with MongoDB deletion even if Firebase deletion fails
    }

    // Delete user from MongoDB
    await User.findOneAndDelete({ _id: userId });

    res.json({
      message: "Account deleted successfully",
      success: true,
    });
  } catch (error) {
    console.error("Delete account error:", error);
    res.status(500).json({
      message: "Error deleting account",
      error: error.message,
    });
  }
};

// Password reset (generate reset link)
exports.resetPassword = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: "Email is required" });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: "Invalid email format" });
    }

    // Check if user exists in Firebase
    try {
      await admin.auth().getUserByEmail(email);
    } catch (error) {
      if (error.code === "auth/user-not-found") {
        return res
          .status(404)
          .json({ message: "No account found with this email address" });
      }
      throw error;
    }

    // Generate password reset link
    const actionCodeSettings = {
      url:
        process.env.PASSWORD_RESET_REDIRECT_URL ||
        "http://localhost:3000/reset-password",
      handleCodeInApp: false,
    };

    const resetLink = await admin
      .auth()
      .generatePasswordResetLink(email, actionCodeSettings);

    // Here you would typically send the email using your email service
    console.log("Password reset link generated:", resetLink);

    res.json({
      message:
        "Password reset email sent successfully! Please check your inbox.",
      success: true,
    });
  } catch (error) {
    console.error("Password reset error:", error);
    res.status(500).json({
      message: "Failed to send password reset email",
      error: error.message,
    });
  }
};

// Update user email
exports.updateEmail = async (req, res) => {
  try {
    const { newEmail } = req.body;
    const userId = req.user.id; // userId is the MongoDB _id, which is Firebase UID

    if (!newEmail) {
      return res.status(400).json({ message: "New email is required" });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
      return res.status(400).json({ message: "Invalid email format" });
    }

    // Get user from database
    const user = await User.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Update email in Firebase using user._id (Firebase UID)
    await admin.auth().updateUser(user._id, {
      email: newEmail,
      emailVerified: false, // Email needs to be verified again
    });

    // Update email in MongoDB
    user.email = newEmail;
    user.emailVerified = false;
    user.updatedAt = new Date();
    await user.save();

    res.json({
      message:
        "Email updated successfully. Please verify your new email address.",
      user: formatUserResponse(user),
      needsVerification: true,
    });
  } catch (error) {
    console.error("Update email error:", error);

    if (error.code === "auth/email-already-exists") {
      return res
        .status(409)
        .json({ message: "Email is already in use by another account" });
    }

    res.status(500).json({
      message: "Failed to update email",
      error: error.message,
    });
  }
};

// Wallet user connect (with Firebase Custom Auth integration)
exports.createUserWithWallet = async (req, res) => {
  try {
    const { walletAddress } = req.body;

    if (!walletAddress) {
      return res.status(400).json({ message: "Wallet address is required" });
    }

    const normalizedWalletAddress = walletAddress.toLowerCase();

    // Check MongoDB first
    let user = await User.findOne({ walletAddress: normalizedWalletAddress });
    let firebaseUid;
    let customToken;

    if (user) {
      // Existing user found in MongoDB
      firebaseUid = user._id;

      try {
        // Ensure Firebase user exists
        await admin.auth().getUser(firebaseUid);
      } catch (error) {
        if (error.code === "auth/user-not-found") {
          // Firebase user missing – create it again with same UID
          await admin.auth().createUser({
            uid: firebaseUid,
            displayName: `${normalizedWalletAddress.slice(
              0,
              4
            )}...${normalizedWalletAddress.slice(-4)}`,
          });
        } else {
          console.error("Firebase error:", error);
          throw error;
        }
      }

      await admin.auth().setCustomUserClaims(firebaseUid, {
        walletAddress: normalizedWalletAddress,
      });

      appToken = generateJWTToken(user);

      return res.status(200).json({
        message: "User logged in",
        user: formatUserResponse(user),
        appToken,
      });
    } else {
      // No user in MongoDB —> Create Firebase user first
      const fbUser = await admin.auth().createUser({
        displayName: `${normalizedWalletAddress.slice(
          0,
          4
        )}...${normalizedWalletAddress.slice(-4)}`,
      });

      firebaseUid = fbUser.uid;

      // Set custom claims
      await admin.auth().setCustomUserClaims(firebaseUid, {
        walletAddress: normalizedWalletAddress,
      });

      // Create user in MongoDB using Firebase UID
      user = new User({
        _id: firebaseUid,
        walletAddress: normalizedWalletAddress,
        authProvider: "wallet",
        username: fbUser.displayName,
        isWalletConnected: true,
      });

      await user.save();

      const appToken = generateJWTToken(user);

      return res.status(201).json({
        message: "User created successfully",
        user: formatUserResponse(user),
        appToken,
      });
    }
  } catch (error) {
    console.error("Wallet user creation error:", error);
    return res.status(500).json({
      message: "Error connecting wallet or creating user",
      error: error.message || "Unknown error",
    });
  }
};

exports.verifyWallet = async (req, res) => {
  try {
    const { walletAddress } = req.body; // userId is now implicitly req.user.id (Firebase UID)
    const userId = req.user.id; // This is the Firebase UID from the JWT

    if (!userId) {
      return res.status(401).json({ message: "User not authenticated." });
    }

    if (!walletAddress) {
      return res.status(400).json({ message: "Wallet address is required." });
    }

    const normalizedWalletAddress = walletAddress.toLowerCase();

    // 1. Find the current authenticated user by their _id (Firebase UID)
    let user = await User.findOne({ _id: userId });

    if (!user) {
      return res
        .status(404)
        .json({ message: "Authenticated user not found in database." });
    }

    // 2. Check if this wallet address is already linked to another user
    const existingWalletOwner = await User.findOne({
      walletAddress: normalizedWalletAddress,
      _id: { $ne: userId }, // Exclude the current user
    });

    if (existingWalletOwner) {
      return res.status(409).json({
        message: "This wallet address is already linked to another account.",
      });
    }

    // 3. Link the wallet address and set isWalletConnected to true
    user.walletAddress = normalizedWalletAddress;
    user.isWalletConnected = true;
    user.updatedAt = new Date(); // Update timestamp

    await user.save();

    // 4. Generate a new JWT token with updated user information
    const appToken = generateJWTToken(user); // Pass the updated user object

    res.status(200).json({
      message: "Wallet verified and linked successfully.",
      isWalletConnected: true,
      user: formatUserResponse(user), // Send back the updated user object
      token: appToken, // Send back the new token
    });
  } catch (error) {
    console.error("Verify wallet error:", error);
    res.status(500).json({
      message: "Failed to verify wallet address.",
      error: error.message,
    });
  }
};

exports.linkEmail = async (req, res) => {
  try {
    const { email } = req.body;
    const userId = req.user.id; // This is the Firebase UID from the JWT, identifying the existing user

    // --- Input Validation ---
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated." });
    }
    if (!email) {
      return res.status(400).json({ message: "Email is required." });
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: "Invalid email format." });
    }

    // --- Find Existing User in MongoDB ---
    // Assuming 'User' is your Mongoose model or similar ORM
    const user = await User.findOne({ _id: userId });
    if (!user) {
      return res
        .status(404)
        .json({ message: "Authenticated user not found in database." });
    }

    // --- Check for Email Duplication (within your database) ---
    // Ensure the new email isn't already linked to a DIFFERENT user in your DB
    const existingEmailUserInDb = await User.findOne({
      email: email.toLowerCase(),
      _id: { $ne: userId }, // Exclude the current user's own document
    });

    if (existingEmailUserInDb) {
      return res.status(409).json({
        message:
          "This email is already linked to another account in your database.",
      });
    }

    // --- Update Email in Firebase Authentication ---
    // This will update the user's email in Firebase Auth
    try {
      await admin.auth().updateUser(user._id, {
        email: email,
        emailVerified: false, // Set to false, as the new email needs to be verified
      });
      console.log(
        `Firebase Auth user (UID: ${user._id}) email updated to: ${email}`
      );
    } catch (firebaseError) {
      // Catch specific Firebase errors like email already existing in Firebase Auth
      if (firebaseError.code === "auth/email-already-exists") {
        return res.status(409).json({
          message:
            "This email is already associated with a different account in Firebase. Please try a different email or log in with that account.",
        });
      }
      console.error("Firebase updateUser email error:", firebaseError);
      // Re-throw other Firebase errors to be caught by the outer catch block
      throw firebaseError;
    }

    // --- Generate and Trigger Email Verification Link via Firebase ---
    // Firebase handles sending the verification email itself after this call,
    // assuming your Firebase project's email templates are enabled.
    const actionCodeSettings = {
      url:
        process.env.EMAIL_VERIFICATION_REDIRECT_URL ||
        "http://localhost:3000/verify-email",
      handleCodeInApp: false, // Set to true if you want to handle verification within your mobile app
    };

    let verificationLink;
    try {
      verificationLink = await admin
        .auth()
        .generateEmailVerificationLink(email, actionCodeSettings);
      console.log(
        "Firebase email verification link generated successfully:",
        verificationLink
      );
    } catch (linkGenerationError) {
      console.error(
        "Error generating Firebase email verification link:",
        linkGenerationError
      );
      // This indicates an issue with Firebase's ability to create the link (e.g., malformed URL)
      return res.status(500).json({
        message:
          "Failed to generate verification link via Firebase. Check Firebase project settings.",
        error: linkGenerationError.message,
      });
    }

    // --- Update User in MongoDB ---
    // Reflect the pending email verification status in your database
    user.email = email.toLowerCase();
    user.emailVerified = false; // It's false until the user clicks the link
    user.updatedAt = new Date();
    await user.save();

    // --- Generate New JWT Token for Frontend (if needed) ---
    // This token might be needed if the frontend relies on the `email` claim immediately.
    // The `emailVerified` claim will still be 'false' in this new token.
    const appToken = generateJWTToken(user);

    res.status(200).json({
      message:
        "Verification email sent! Please check your inbox to verify your email address.",
      user: formatUserResponse(user), // Assuming this formats the user object for response
      token: appToken,
      needsVerification: true, // Indicate to frontend that verification is pending
    });
  } catch (error) {
    console.error("Link email function caught an unexpected error:", error);
    res
      .status(500)
      .json({ message: "Failed to link email address.", error: error.message });
  }
};

const sendEmailVerificationLink = async (email) => {
  try {
    const actionCodeSettings = {
      url:
        process.env.EMAIL_VERIFICATION_REDIRECT_URL ||
        "http://localhost:3000/verify-email",
      handleCodeInApp: false,
    };
    const link = await admin
      .auth()
      .generateEmailVerificationLink(email, actionCodeSettings);
    console.log(`Verification link generated for ${email}: ${link}`);
    // In a real application, you would integrate with an email service here
    // e.g., sendgrid.sendEmail({ to: email, subject: 'Verify your email', html: `<a href="${link}">Verify Email</a>` });
    return true;
  } catch (error) {
    console.error(`Error sending verification email to ${email}:`, error);
    throw new Error("Failed to send verification email.");
  }
};

exports.linkGoogle = async (req, res) => {
  try {
    const { idToken } = req.body;
    const userId = req.user.id;

    if (!userId) {
      return res.status(401).json({ message: "User not authenticated." });
    }
    if (!idToken) {
      return res.status(400).json({ message: "Google ID token is required." });
    }

    const decodedToken = await verifyFirebaseToken(idToken);
    const googleUid = decodedToken.uid;
    const googleEmail = decodedToken.email;

    const user = await User.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ message: "Authenticated user not found." });
    }

    if (user._id !== userId) {
      console.warn(
        `Authenticated user _id (${user._id}) does not match JWT ID (${userId}). Potential inconsistency.`
      );
    }

    const existingGoogleUser = await User.findOne({
      $or: [{ _id: googleUid }, { email: googleEmail }],
      _id: { $ne: userId },
    });

    if (existingGoogleUser) {
      return res.status(409).json({
        message:
          "This Google account is already linked to another user account. Please try a different account or log in with it.",
      });
    }

    try {
      const userRecord = await admin.auth().getUser(user._id);
      const currentProviders = userRecord.providerData.map((p) => p.providerId);

      if (currentProviders.includes("google.com")) {
        return res
          .status(400)
          .json({ message: "Google account is already linked to this user." });
      }

      await admin.auth().updateUser(user._id, {
        email: googleEmail || user.email,
        emailVerified: decodedToken.email_verified || user.emailVerified,
        displayName:
          decodedToken.name || decodedToken.display_name || user.username,
        photoURL: decodedToken.picture || user.profileImage,
      });

      user.email = googleEmail || user.email;
      user.emailVerified = decodedToken.email_verified || user.emailVerified;
      user.profileImage = decodedToken.picture || user.profileImage;
      user.username =
        decodedToken.name || decodedToken.display_name || user.username;
      user.authProvider = "google"; // Update auth provider to reflect primary linkage
      user.lastLoginAt = new Date();
      user.updatedAt = new Date();

      await user.save();

      const appToken = generateJWTToken(user); // Pass the updated user object

      res.status(200).json({
        message: "Google account linked successfully.",
        user: formatUserResponse(user),
        token: appToken,
      });
    } catch (firebaseError) {
      console.error(
        "Firebase update user with Google info error:",
        firebaseError
      );
      return res.status(500).json({
        message: "Failed to link Google account in Firebase Auth.",
        error: firebaseError.message,
      });
    }
  } catch (error) {
    console.error("Link Google account error:", error);
    res.status(500).json({
      message: "Failed to link Google account.",
      error: error.message,
    });
  }
};

exports.confirmEmail = async (req, res) => {
  try {
    console.log("hitted");

    const userId = req.user.id;
    const user = await User.findOne({ _id: userId });

    if (!user) return res.status(404).json({ message: "User not found" });

    user.emailVerified = true;
    await user.save();

    const newToken = generateJWTToken(user);

    res.status(200).json({
      message: "Email verified successfully.",
      user: formatUserResponse(user),
      token: newToken,
    });
  } catch (error) {
    console.error("Email confirmation error:", error);
    res
      .status(500)
      .json({ message: "Failed to confirm email.", error: error.message });
  }
};

exports.getPaperTradeHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log("HI");

    console.log(userId);

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const user = await User.findOne({ _id: userId }).select(
      "paperTradeHistory"
    );

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const total = user.paperTradeHistory.length;
    const paginatedHistory = user.paperTradeHistory
      .sort(
        (a, b) =>
          new Date(b.buy?.timestamp || 0) - new Date(a.buy?.timestamp || 0)
      ) // sort by most recent
      .slice(skip, skip + limit);

    res.status(200).json({
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      history: paginatedHistory,
    });
  } catch (error) {
    console.error("Get paper trade history error:", error);
    res
      .status(500)
      .json({ message: "Failed to fetch trade history", error: error.message });
  }
};

exports.addPaperTradeHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    let trades = req.body;

    // Allow single object or array
    if (!Array.isArray(trades)) {
      trades = [trades];
    }

    // Validate each trade object
    const validTrades = trades.map((t) => {
      const { token, buy, sell, state = "pending" } = t;

      if (!token || !token.name || !token.symbol || !token.address) {
        throw new Error("Each trade must include a complete 'token' object.");
      }

      return {
        tradeId: new mongoose.Types.ObjectId(),
        token,
        buy,
        sell,
        state,
      };
    });

    const user = await User.findOne({ _id: userId });
    if (!user) return res.status(404).json({ message: "User not found" });

    // Push all trades to paperTradeHistory
    user.paperTradeHistory.push(...validTrades);
    await user.save();

    res.status(201).json({
      message: `${validTrades.length} trade(s) added successfully.`,
      trades: validTrades,
    });
  } catch (error) {
    console.error("Add trade error:", error);
    res.status(500).json({
      message: "Server error",
      error: error.message,
    });
  }
};
