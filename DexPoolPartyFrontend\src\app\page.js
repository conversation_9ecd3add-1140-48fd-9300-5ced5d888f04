// app/page.js (or pages/index.js, depending on your Next.js setup)

"use client";
import { CoinMarketProvider } from '../../context/context.js'; // Assuming this is your original CMC context
import HomePage from "./homePage/Page.jsx";
import { AuthProvider } from "@/components/AuthProvider.js";
// Import the TrendingProvider and useTrending from your dedicated context file
import { TrendingProvider } from "@/contexts/trendingData.js";

export default function Home() {
  // REMOVE all the state, useCallback, and useEffect from here
  // const [trendingData, setTrendingData] = useState([]);
  // const [loading, setLoading] = useState(true);
  // const [error, setError] = useState(null);
  // const [mnesData, setMnesData] = useState([]);
  // const [loadingMnes, setLoadingMnes] = useState(true);
  // const [errorMnes, setErrorMnes] = useState(null);
  // const refreshTrending = useCallback(...);
  // const refreshMnes = useCallback(...);
  // useEffect(...);

  return (
    <div className="flex-1 flex justify-center overflow-y-auto ">
      <AuthProvider>
        {/* Wrap your components with TrendingProvider */}
        <TrendingProvider>
          {/* <TokenProvider> */}
            <CoinMarketProvider>
              <HomePage />
              {/* <CmcTable /> */}
            </CoinMarketProvider>
          {/* </TokenProvider> */}
        </TrendingProvider>
      </AuthProvider>
    </div>
  );
}

// REMOVE useTrending export from here, it should be in TrendingContext.js
// export const useTrending = () => {
//   const context = useContext(TrendingContext);
//   if (context === undefined) {
//     throw new Error('useTrending must be used within a TrendingProvider');
//   }
//   return context;
// };