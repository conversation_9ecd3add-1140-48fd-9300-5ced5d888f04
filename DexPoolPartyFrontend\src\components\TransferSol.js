import { clusterApiUrl, Connection, PublicKey, SystemProgram, Transaction } from '@solana/web3.js';
import { useEffect, useRef, useState } from 'react';

const network = "devnet";
const defaultDest = 'A3gWSs3vB6T1hwbEP5ENJgnydBJzH3TL1QjPWASYkDbK';

const TransferSol = ({ provider }) => {
  const connection = useRef(new Connection(clusterApiUrl(network)));

  const [destAddr, setDestAddr] = useState(defaultDest);
  const [lamports, setLamports] = useState(10000);
  const [txid, setTxid] = useState(null);
  const [slot, setSlot] = useState(null);
  const [myBalance, setMyBalance] = useState(0);
  const [rxBalance, setRxBalance] = useState(0);

  useEffect(() => {
    connection.current.getBalance(provider.publicKey).then(setMyBalance);
  }, [provider.publicKey]);

  useEffect(() => {
    connection.current.getBalance(new PublicKey(destAddr)).then(setRxBalance);
  }, [destAddr]);

  const handleChangeAddr = (e) => {
    setDestAddr(e.target.value);
  };

  const handleChangeLamp = (e) => {
    setLamports(parseInt(e.target.value));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const transaction = new Transaction({
      feePayer: provider.publicKey,
      recentBlockhash: (await connection.current.getRecentBlockhash()).blockhash
    });

    transaction.add(
      SystemProgram.transfer({
        fromPubkey: provider.publicKey,
        toPubkey: new PublicKey(destAddr),
        lamports: lamports,
      })
    );

    await provider.signTransaction(transaction);

    connection.current.sendRawTransaction(transaction.serialize())
      .then((id) => {
        console.log(`Transaction ID: ${id}`);
        setTxid(id);
        connection.current.confirmTransaction(id)
          .then((confirmation) => {
            console.log(`Confirmation slot: ${confirmation.context.slot}`);
            setSlot(confirmation.context.slot);
            connection.current.getBalance(provider.publicKey).then(setMyBalance);
            connection.current.getBalance(new PublicKey(destAddr)).then(setRxBalance);
          });
      })
      .catch(console.error);
  };

  return (
    <form onSubmit={handleSubmit}>
      <label>Enter address of destination</label><br />
      <input type="text" value={destAddr} onChange={handleChangeAddr} /><br />
      <label>Amount of lamports</label><br />
      <input type="number" value={lamports} onChange={handleChangeLamp} /><br />
      <input type="submit" value="Send lamports" />
      <hr />
      <p>My Balance: {myBalance} lamports</p>
      <p>Recipient Balance: {rxBalance} lamports</p>
      <hr />
      {txid ? <p>Transaction id: <span style={{ fontSize: '0.7em' }}>{txid}</span></p> : null}
      {slot ? <p>Confirmation slot: {slot}</p> : null}
    </form>
  );
};

export default TransferSol;
