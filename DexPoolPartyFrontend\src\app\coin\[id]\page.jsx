'use client';

import { useParams } from 'next/navigation';
import React, { useState, useEffect, use } from 'react';
import Image from 'next/image';
import { toast } from 'sonner';
import CommentSection from '@/components/CommentSection';
import Reply from '@/components/Reply';
import ReplyModal from '@/components/ReplyModal';

// Helper: Determine token type by mint address (simple mapping, can be extended)
const TOKEN_TYPE_MAP = {
  // Add known mint addresses here for fast lookup
  // 'mintAddress': 'bonk' | 'boop' | 'pumpfun'
};

function getTokenType(mint) {
  if (TOKEN_TYPE_MAP[mint]) return TOKEN_TYPE_MAP[mint];
  // Fallback: try all APIs in order
  return null;
}

// Hook to fetch token data from the correct API
function useTokenData(mint) {
  const [data, setData] = useState(null);
  const [type, setType] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    if (!mint) return;
    let cancelled = false;
    async function fetchData() {
      setLoading(true);
      setError(null);
      setData(null);
      // Try each API in order
      const apis = [
        { type: 'bonk', url: `/api/bonkcoins?mint=${mint}&limit=1` },
        { type: 'boop', url: `/api/boopfuncoins?mint=${mint}&limit=1` },
        { type: 'pumpfun', url: `/api/pumpfuncoins?mint=${mint}&limit=1` },
      ];
      for (const api of apis) {
        try {
          const res = await fetch(api.url);
          const json = await res.json();
          if (json.success && Array.isArray(json.data) && json.data.length > 0) {
            if (!cancelled) {
              setData(json.data[0]);
              setType(api.type);
              setLoading(false);
            }
            return;
          }
        } catch (e) {
          // Ignore and try next
        }
      }
      if (!cancelled) {
        setError('Token not found or unsupported.');
        setLoading(false);
      }
    }
    fetchData();
    return () => { cancelled = true; };
  }, [mint]);

  return { data, type, loading, error };
}

// Add hook to fetch analytics/trade data
function useTokenAnalytics(mint) {
  const [analytics, setAnalytics] = useState({ price: null, marketCap: null, volume: null });
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    if (!mint) return;
    let cancelled = false;
    async function fetchAnalytics() {
      setLoading(true);
      // Fetch token data for price, market cap, volume
      const res = await fetch(`/api/bonkcoins?mint=${mint}&limit=1`);
      const json = await res.json();
      let price = null, marketCap = null, volume = null;
      if (json.success && Array.isArray(json.data) && json.data.length > 0) {
        price = json.data[0].priceUSD || null;
        marketCap = json.data[0].marketCapUSD || null;
        // Optionally, fetch volume from token-chart or DEXTrades
      }
      if (!cancelled) setAnalytics({ price, marketCap, volume });
      setLoading(false);
    }
    fetchAnalytics();
    return () => { cancelled = true; };
  }, [mint]);
  return { ...analytics, loading };
}

function useTokenTrades(mint, hours = 24) {
  const [trades, setTrades] = useState([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    if (!mint) return;
    let cancelled = false;
    async function fetchTrades() {
      setLoading(true);
      const res = await fetch(`/api/token-trades?mint=${mint}&limit=100`); // fetch more for 24h
      const json = await res.json();
      if (!cancelled && json.success) setTrades(json.data);
      setLoading(false);
    }
    fetchTrades();
    return () => { cancelled = true; };
  }, [mint, hours]);
  return { trades, loading };
}

function get24hVolume(trades) {
  const now = Date.now();
  const cutoff = now - 24 * 60 * 60 * 1000;
  return trades
    .filter(trade => trade.time && new Date(trade.time).getTime() >= cutoff)
    .reduce((sum, trade) => sum + (Number(trade.priceUSD) * Number(trade.amount) || 0), 0);
}

// Refactored hook to fetch all token data in parallel
function useAllTokenData(mint) {
  const [results, setResults] = useState({ bonk: null, boop: null, pumpfun: null });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!mint) return;
    let cancelled = false;
    async function fetchAll() {
      setLoading(true);
      setError(null);
      const apis = [
        { type: 'bonk', url: `/api/bonkcoins?mint=${mint}&limit=1` },
        { type: 'boop', url: `/api/boopfuncoins?mint=${mint}&limit=1` },
        { type: 'pumpfun', url: `/api/pumpfuncoins?mint=${mint}&limit=1` },
      ];
      const fetches = apis.map(api => fetch(api.url).then(res => res.json()).then(json => (json.success && Array.isArray(json.data) && json.data.length > 0) ? json.data[0] : null).catch(() => null));
      const [bonk, boop, pumpfun] = await Promise.all(fetches);
      if (!cancelled) setResults({ bonk, boop, pumpfun });
      setLoading(false);
    }
    fetchAll();
    return () => { cancelled = true; };
  }, [mint]);
  return { ...results, loading, error };
}

export default function CoinDetailsPage() {
  // Unwrap params with React.use()
  const params = use(useParams());
  const mint = params?.id;
  console.log('CoinDetailsPage mint:', mint);
  if (!mint || typeof mint !== 'string' || mint.length < 32) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-[#181c24] to-[#23272f] text-white">
        <h1 className="text-3xl font-bold mb-4">Invalid or Missing Token</h1>
        <p className="text-gray-400 mb-6">No valid mint address provided in the URL.</p>
        <a href="/" className="px-6 py-2 bg-blue-600 rounded-lg text-white font-semibold hover:bg-blue-700 transition">Go Home</a>
      </div>
    );
  }
  const { bonk, boop, pumpfun, loading, error } = useAllTokenData(mint);
  const { price, marketCap, loading: analyticsLoading } = useTokenAnalytics(mint);
  const { trades, loading: tradesLoading } = useTokenTrades(mint);
  const volume = get24hVolume(trades);
  const [activeTab, setActiveTab] = useState('info');
  const hasAny = bonk || boop || pumpfun;

  // Platform badge colors
  const platformColors = {
    BONK: 'bg-pink-600 text-white',
    BOOP: 'bg-purple-600 text-white',
    PumpFun: 'bg-yellow-400 text-black',
  };

  // Helper for last updated
  function lastUpdated(d) {
    if (!d?.fetchedAt && !d?.createdAtFormatted) return null;
    const t = d.fetchedAt || d.createdAtFormatted;
    return `Last updated: ${new Date(t).toLocaleString()}`;
  }

  // Reply modal state
  const [replyTo, setReplyTo] = useState(null);
  const [replyModalVisible, setReplyModalVisible] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#181c24] to-[#23272f]">
        <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-500"></div>
      </div>
    );
  }

  if (!hasAny) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-[#181c24] to-[#23272f] text-white p-6">
        <div className="bg-[#23272f] rounded-3xl shadow-2xl p-10 max-w-xl w-full flex flex-col items-center">
          <Image src="/token.png" alt="Unknown Token" width={80} height={80} className="mb-4 opacity-60" />
          <h1 className="text-3xl font-bold mb-2">Unknown Token</h1>
          <p className="text-gray-400 mb-4 text-center">No data found for this mint address on any supported platform.<br/>It may not be a BONK, BOOP, or PumpFun token, or it may not have launched yet.</p>
          <a href="/" className="px-6 py-2 bg-blue-600 rounded-lg text-white font-semibold hover:bg-blue-700 transition mb-4">Go Home</a>
          <div className="bg-[#181c24] rounded-xl p-4 mt-2 w-full text-left border border-blue-900">
            <h2 className="text-lg font-bold mb-2 text-blue-400">Token Not Found?</h2>
            <ul className="list-disc pl-6 text-gray-300 text-sm space-y-1">
              <li>Check the mint address for typos.</li>
              <li>Make sure the token is a real BONK, BOOP, or PumpFun token.</li>
              <li>Some tokens may not be indexed yet—try again later.</li>
              <li>If you believe this is an error, contact support or check the project docs.</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

        return (
    <div className="min-h-screen bg-gradient-to-br from-[#181c24] to-[#23272f] text-white p-4 md:p-8 font-sans">
      <div className="max-w-5xl mx-auto rounded-3xl shadow-2xl bg-[#23272f]/80 backdrop-blur-lg p-0 md:p-0 overflow-hidden">
        {/* Header */}
        <div className="flex flex-col md:flex-row items-center gap-6 p-8 bg-[#1e2128] border-b border-[#23272f]">
          <Image
            src={bonk?.logo || boop?.logo || pumpfun?.logo || '/token.png'}
            alt={bonk?.name || boop?.name || pumpfun?.name || 'Token'}
            width={80}
            height={80}
            className="rounded-full border-4 border-blue-500 shadow-lg"
          />
          <div className="flex-1">
            <h1 className="text-3xl md:text-4xl font-bold mb-2 flex items-center gap-2">
              {bonk?.name || boop?.name || pumpfun?.name || 'Unknown Token'}
              <span className="text-lg text-blue-400 bg-blue-900 px-2 py-1 rounded-lg ml-2 uppercase tracking-wider">{bonk?.symbol || boop?.symbol || pumpfun?.symbol || ''}</span>
            </h1>
            <div className="flex flex-wrap gap-2 text-gray-400 text-sm">
              <span>Mint: <span className="font-mono text-blue-300">{mint}</span></span>
              <span>Type: <span className="capitalize">{[bonk && 'BONK', boop && 'BOOP', pumpfun && 'PumpFun'].filter(Boolean).join(', ')}</span></span>
              <span>Created: {bonk?.createdAtFormatted || boop?.createdAtFormatted || pumpfun?.createdAtFormatted}</span>
              <span>Decimals: {bonk?.decimals || boop?.decimals || pumpfun?.decimals}</span>
            </div>
          </div>
        </div>
        {/* Data Sections */}
        <div className="flex flex-col gap-8 p-8">
          {[['BONK', bonk], ['BOOP', boop], ['PumpFun', pumpfun]].filter(([_, d]) => d).map(([label, d]) => (
            <div key={label} className="bg-gradient-to-br from-[#23272f] to-[#181c24] rounded-2xl shadow-lg p-6 md:p-10 flex flex-col md:flex-row items-center gap-8 hover:shadow-blue-900/30 transition-shadow border border-blue-900">
              <div className="flex flex-col items-center md:items-start w-full md:w-1/3">
                <span className={`px-3 py-1 rounded-full text-xs font-bold mb-2 ${platformColors[label]}`}>{label}</span>
                <Image src={d.logo || '/token.png'} alt={d.name} width={64} height={64} className="rounded-full border-2 border-blue-500 mb-2" />
                <span className="text-xl font-bold mb-1">{d.name}</span>
                <span className="text-blue-400 font-mono text-sm mb-2">{d.symbol}</span>
                <span className="text-gray-400 text-xs mb-2">{lastUpdated(d)}</span>
              </div>
              <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-[#1e2128] rounded-xl p-6 flex flex-col items-center shadow-md">
                  <span className="text-gray-400 text-sm mb-1">Current Price</span>
                  <span className="text-2xl font-bold text-blue-400">{d.priceUSD ? `$${Number(d.priceUSD).toLocaleString(undefined, { maximumFractionDigits: 8 })}` : 'N/A'}</span>
                </div>
                <div className="bg-[#1e2128] rounded-xl p-6 flex flex-col items-center shadow-md">
                  <span className="text-gray-400 text-sm mb-1">Market Cap</span>
                  <span className="text-2xl font-bold text-green-400">{d.marketCapUSD ? `$${Number(d.marketCapUSD).toLocaleString(undefined, { maximumFractionDigits: 2 })}` : 'N/A'}</span>
                </div>
                <div className="bg-[#1e2128] rounded-xl p-6 flex flex-col items-center shadow-md">
                  <span className="text-gray-400 text-sm mb-1">Supply</span>
                  <span className="text-2xl font-bold text-yellow-400">{d.supply ? Number(d.supply).toLocaleString() : 'N/A'}</span>
                </div>
              </div>
              <div className="flex-1 mt-6 md:mt-0 md:ml-8">
                <div className="text-gray-200 text-lg whitespace-pre-wrap mb-4">{d.description || 'No description available.'}</div>
                {d.links && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    {d.links.telegram && (
                      <a href={d.links.telegram} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 bg-[#2c2c2e] p-3 rounded-lg hover:bg-[#353538] transition-colors">
                        <Image src="/telegram.svg" alt="Telegram" width={24} height={24} />
                        <span>Telegram</span>
                      </a>
                    )}
                    {d.links.website && (
                      <a href={d.links.website} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 bg-[#2c2c2e] p-3 rounded-lg hover:bg-[#353538] transition-colors">
                        <Image src="/globe.svg" alt="Website" width={24} height={24} />
                        <span>Website</span>
                      </a>
                    )}
                    {d.links.twitter && (
                      <a href={d.links.twitter} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 bg-[#2c2c2e] p-3 rounded-lg hover:bg-[#353538] transition-colors">
                        <Image src="/X.avif" alt="Twitter" width={24} height={24} />
                        <span>Twitter</span>
                      </a>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        {/* Analytics Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 p-8 bg-[#181c24]">
          <div className="bg-[#23272f] rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-blue-900/30 transition-shadow">
            <span className="text-gray-400 text-sm mb-1">Current Price</span>
            <span className="text-2xl font-bold text-blue-400">{analyticsLoading ? '...' : price ? `$${Number(price).toLocaleString(undefined, { maximumFractionDigits: 8 })}` : 'N/A'}</span>
          </div>
          <div className="bg-[#23272f] rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-green-900/30 transition-shadow">
            <span className="text-gray-400 text-sm mb-1">Market Cap</span>
            <span className="text-2xl font-bold text-green-400">{analyticsLoading ? '...' : marketCap ? `$${Number(marketCap).toLocaleString(undefined, { maximumFractionDigits: 2 })}` : 'N/A'}</span>
          </div>
          <div className="bg-[#23272f] rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-yellow-900/30 transition-shadow">
            <span className="text-gray-400 text-sm mb-1">24h Volume</span>
            <span className="text-2xl font-bold text-yellow-400">{tradesLoading ? '...' : `$${Number(volume).toLocaleString(undefined, { maximumFractionDigits: 2 })}`}</span>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex gap-4 border-b border-gray-700 mb-8 px-8 bg-[#181c24]">
          {['info', 'trades', 'analytics', 'comments'].map(tab => (
            <button
              key={tab}
              className={`px-4 py-2 font-semibold transition rounded-t-lg ${activeTab === tab ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white hover:bg-blue-900'}`}
              onClick={() => setActiveTab(tab)}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="min-h-[200px] px-8 pb-8">
          {activeTab === 'info' && (
            <div className="space-y-6">
              <p className="text-lg text-gray-200 whitespace-pre-wrap">{bonk?.description || boop?.description || pumpfun?.description || 'No description available.'}</p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {bonk?.links?.telegram && (
                  <a href={bonk.links.telegram} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 bg-[#2c2c2e] p-3 rounded-lg hover:bg-[#353538] transition-colors">
                    <Image src="/telegram.svg" alt="Telegram" width={24} height={24} />
                    <span>Telegram</span>
                  </a>
                )}
                {bonk?.links?.website && (
                  <a href={bonk.links.website} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 bg-[#2c2c2e] p-3 rounded-lg hover:bg-[#353538] transition-colors">
                    <Image src="/globe.svg" alt="Website" width={24} height={24} />
                    <span>Website</span>
                  </a>
                )}
                {bonk?.links?.twitter && (
                  <a href={bonk.links.twitter} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 bg-[#2c2c2e] p-3 rounded-lg hover:bg-[#353538] transition-colors">
                    <Image src="/X.avif" alt="Twitter" width={24} height={24} />
                    <span>Twitter</span>
                  </a>
                )}
              </div>
            </div>
          )}
          {activeTab === 'trades' && (
            <div className="py-4 text-gray-200">
              <h3 className="text-xl font-semibold mb-4">Recent Trades</h3>
              {tradesLoading ? (
                <div className="text-center text-gray-400">Loading trades...</div>
              ) : trades.length === 0 ? (
                <div className="text-center text-gray-400">No recent trades found.</div>
              ) : (
                <div className="overflow-x-auto rounded-xl shadow-lg bg-[#23272f]">
                  <table className="min-w-full text-sm">
                    <thead>
                      <tr className="bg-[#1e2128] text-gray-400">
                        <th className="px-4 py-2">Time</th>
                        <th className="px-4 py-2">Buyer</th>
                        <th className="px-4 py-2">Amount</th>
                        <th className="px-4 py-2">Price (USD)</th>
                        <th className="px-4 py-2">Protocol</th>
                      </tr>
                    </thead>
                    <tbody>
                      {trades.map((trade, i) => (
                        <tr key={i} className="border-b border-[#353545] hover:bg-[#181c24] transition-colors">
                          <td className="px-4 py-2">{trade.time ? new Date(trade.time).toLocaleString() : '-'}</td>
                          <td className="px-4 py-2 font-mono text-blue-300">{trade.buyer ? trade.buyer.slice(0, 6) + '...' : '-'}</td>
                          <td className="px-4 py-2">{trade.amount ? Number(trade.amount).toLocaleString() : '-'}</td>
                          <td className="px-4 py-2">{trade.priceUSD ? `$${Number(trade.priceUSD).toLocaleString(undefined, { maximumFractionDigits: 8 })}` : '-'}</td>
                          <td className="px-4 py-2">{trade.protocol || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
          </div>
              )}
            </div>
          )}
          {activeTab === 'analytics' && (
            <div className="py-4 text-gray-200">
              <h3 className="text-xl font-semibold mb-4">Analytics</h3>
              <p className="text-gray-400">Analytics integration coming soon.</p>
            </div>
          )}
          {activeTab === 'comments' && (
          <div className="py-4">
              <div className="max-w-3xl mx-auto mt-12 mb-8">
                <h2 className="text-2xl font-bold mb-6 text-blue-300">Comments & Replies</h2>
                <CommentSection
                  tokenId={mint}
                  initialComments={[]}
                  onReply={(comment) => {
                    setReplyTo(comment);
                    setReplyModalVisible(true);
                  }}
                  renderReply={(reply) => <Reply {...reply} />}
                />
                <ReplyModal
                  visible={replyModalVisible}
                  onClose={() => setReplyModalVisible(false)}
                  onSubmit={(replyData) => {
                    // TODO: Implement reply posting logic (API call)
                    setReplyModalVisible(false);
                  }}
                />
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
}
