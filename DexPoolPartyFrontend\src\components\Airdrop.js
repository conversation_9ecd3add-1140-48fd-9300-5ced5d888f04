import { useEffect, useRef, useState } from "react";
import { Connection, clusterApiUrl } from "@solana/web3.js";

const network = "devnet";

const Airdrop = ({ pubkey }) => {
  const connection = useRef(new Connection(clusterApiUrl(network)));

  const [publickey] = useState(pubkey.toBase58());
  const [lamports, setLamports] = useState(100000);
  const [txid, setTxid] = useState(null);
  const [slot, setSlot] = useState(null);
  const [balance, setBalance] = useState(0);

  useEffect(() => {
    connection.current.getBalance(pubkey).then(setBalance);
  }, [pubkey]);

  function handleSubmit(event) {
    event.preventDefault();
    if (!pubkey) return;

    connection.current
      .requestAirdrop(pubkey, lamports)
      .then((id) => {
        console.log(`Transaction ID ${id}`);
        setTxid(id);
        connection.current.confirmTransaction(id).then((confirmation) => {
          console.log(`Confirmation slot: ${confirmation.context.slot}`);
          setSlot(confirmation.context.slot);
          connection.current.getBalance(pubkey).then(setBalance);
        });
      })
      .catch(console.error);
  }

  function handleChange(event) {
    setLamports(parseInt(event.target.value));
  }

  return (
    <div>
      <p className="p15">&nbsp;</p>
      <form onSubmit={handleSubmit}>
        <label>Public Key to receive airdrop</label><br />
        <input type="text" readOnly value={publickey} className="input-text" /><br />
        <label>Lamports to request</label><br />
        <input type="number" value={lamports} onChange={handleChange} className="input-text" /><br />
        <input type="submit" value="Request airdrop" className="input-submit" />
      </form>
      <p className="p15">&nbsp;</p>
      <hr />
      {txid && <p>Transaction: {txid}</p>}
      {slot && <p>Confirmation slot: {slot}</p>}
      <hr />
      <p>Your current balance is: {balance}</p>
    </div>
  );
};

export default Airdrop;
