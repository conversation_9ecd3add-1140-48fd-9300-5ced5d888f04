import React from 'react';
import { Heart } from 'lucide-react';
import Image from 'next/image';

const Reply = () => {
  return (
    <div id="p116072199" className="grid gap-1 mt-2 overflow-auto bg-[#2e303a] p-2 text-sm text-slate-200 rounded-md">
      <div className="flex w-full flex-wrap items-start gap-2 text-xs text-slate-400">
        {/* <a href="/profile/4JR8hvQWyAKeL514NG5bxcK6EgHmBHfQ2EBcF85CtF6L" className="flex items-center gap-1"> */}
          <Image
            alt="user avatar"
            loading="lazy"
            width={16}
            height={16}
            className="rounded h-4 w-4"
            src="/token.png"
          />
          <span className="flex gap-1 rounded px-1 hover:underline text-black" style={{ backgroundColor: 'rgb(201, 240, 243)' }}>
            4JR8hv
          </span>
        {/* </a> */}
        <div>02:01:50</div>
        <div className="flex w-fit cursor-pointer items-center gap-2 hover:stroke-red-500 hover:text-red-500">
          <Heart className="w-4 h-4" />
          <div>1</div>993
        </div>
        <div className="cursor-pointer justify-self-end hover:underline">[reply]</div>
      </div>
      <div className="flex items-start gap-2">
        <div>
          STOP BUYING THIS BUNDLE ARE U SLOW GUYS? ALL THE KOLS HAVE BUNDLED THIS AND THEY SLOWLY SELL ON TOP OF YOU AND THEY USE CUPSEY MAIN TO MAK YOU BUY
        </div>
      </div>
    </div>
  );
};

export default Reply;

