const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'DexNode API Documentation',
      version: '1.0.0',
      description: 'Complete API documentation for DexNode trading platform with authentication and all endpoints',
      contact: {
        name: 'DexNode Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token obtained from login endpoints'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            _id: { type: 'string', description: 'User ID' },
            email: { type: 'string', format: 'email' },
            username: { type: 'string' },
            profileImage: { type: 'string' },
            walletAddress: { type: 'string' },
            authProvider: { type: 'string', enum: ['google', 'apple', 'wallet', 'email'] },
            emailVerified: { type: 'boolean' },
            isWalletConnected: { type: 'boolean' },
            isActive: { type: 'boolean' },
            status: { type: 'string', enum: ['active', 'inactive', 'suspended'] },
            createdAt: { type: 'string', format: 'date-time' },
            lastLoginAt: { type: 'string', format: 'date-time' }
          }
        },
        Token: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            name: { type: 'string' },
            symbol: { type: 'string' },
            description: { type: 'string' },
            media: { type: 'string' },
            links: {
              type: 'object',
              properties: {
                telegram: { type: 'string' },
                website: { type: 'string' },
                twitter: { type: 'string' }
              }
            },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        Trade: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            userId: { type: 'string' },
            tokenId: { type: 'string' },
            type: { type: 'string', enum: ['buy', 'sell'] },
            amount: { type: 'number' },
            price: { type: 'number' },
            status: { type: 'string', enum: ['pending', 'completed', 'cancelled'] },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        Comment: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            tokenId: { type: 'string' },
            userId: { type: 'string' },
            username: { type: 'string' },
            profileImage: { type: 'string' },
            content: { type: 'string' },
            imageUrl: { type: 'string' },
            parentId: { type: 'string' },
            replies: { type: 'array', items: { type: 'string' } },
            likes: { type: 'number' },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        DevnetTransaction: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            userId: { type: 'string' },
            tokenId: { type: 'string' },
            type: { type: 'string', enum: ['buy', 'sell'] },
            amount: { type: 'number' },
            price: { type: 'number' },
            status: { type: 'string', enum: ['pending', 'completed', 'cancelled'] },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        Reward: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            userId: { type: 'string' },
            type: { type: 'string', enum: ['referral', 'trade_bonus', 'loyalty_bonus', 'daily_bonus', 'milestone_bonus', 'withdrawal'] },
            amount: { type: 'number' },
            description: { type: 'string' },
            status: { type: 'string', enum: ['pending', 'completed', 'withdrawn'] },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        Error: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            error: { type: 'string' }
          }
        }
      }
    },
    security: [{
      bearerAuth: []
    }],
    servers: [
      {
        url: 'http://localhost:4000',
        description: 'Development server',
      },
      {
        url: 'https://api.dexnode.com',
        description: 'Production server',
      }
    ],
  },
  apis: [ 
    './src/routes/*.js',
    './src/models/*.js'
  ],
};

const specs = swaggerJsdoc(options);
module.exports = specs;
