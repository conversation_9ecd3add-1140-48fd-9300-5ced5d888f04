'use client';

import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import LoginModal from './LoginModel.jsx';

const CreateTokenButton = () => {
  const [showLoginModal, setShowLoginModal] = useState(false);
  const { user, isWalletConnected } = useAuth();

  const handleCreateToken = () => {
    if (!user) {
      setShowLoginModal(true);
      return;
    }

    if (!isWalletConnected) {
      setShowLoginModal(true);
      return;
    }

    // Proceed with token creation
    // Your token creation logic here
  };

  return (
    <>
      <button
        onClick={handleCreateToken}
        className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded"
      >
        Create Token
      </button>

      {showLoginModal && (
        <LoginModal 
          onClose={() => setShowLoginModal(false)}
          initialView={!user ? 'login' : 'wallet'}
        />
      )}
    </>
  );
};

export default CreateTokenButton;
