// pages/api/token-chart.js (if you choose Pages Router API)
import { NextResponse } from "next/server"; // Still use NextResponse for consistency, though 'res.json' is typical for Pages Router

// Helper function (can be defined outside or inside)
function aggregateTradesByInterval(trades, intervalMinutes) {
    if (!trades || trades.length === 0) return [];
    const intervalMs = intervalMinutes * 60 * 1000;
    const aggregated = {};
    trades.forEach((trade) => {
        const timestamp = new Date(trade.Block.Time).getTime();
        const intervalKey = Math.floor(timestamp / intervalMs) * intervalMs;
        const price = trade.Trade.Buy.PriceInUSD;
        if (!aggregated[intervalKey]) {
            aggregated[intervalKey] = { prices: [], timestamp: intervalKey };
        }
        if (price && price > 0) {
            aggregated[intervalKey].prices.push(price);
        }
    });
    return Object.values(aggregated)
        .map((interval) => ({
            time: Math.floor(interval.timestamp / 1000),
            value: interval.prices.reduce((sum, price) => sum + price, 0) / interval.prices.length,
            timestamp: interval.timestamp,
        }))
        .sort((a, b) => a.time - b.time);
}

// THIS IS THE DEFAULT EXPORT FOR PAGES ROUTER
export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ success: false, error: 'Method Not Allowed' });
    }

    try {
        const { address: mintAddress, hours = "24" } = req.query; // Access query params from req.query

        if (!mintAddress) {
            return res.status(400).json({ success: false, error: "Mint address parameter is required" });
        }

        const BITQUERY_API_KEY = process.env.BITQUERY_API_KEY;
        const BITQUERY_API_URL = "https://streaming.bitquery.io/eap";

        if (!BITQUERY_API_KEY) {
            console.error("BITQUERY_API_KEY is not set in environment variables.");
            return res.status(500).json(
                { success: false, error: "Server configuration error: API key missing." },
            );
        }

        const till = new Date();
        const since = new Date(till.getTime() - Number.parseInt(hours) * 60 * 60 * 1000);

        const query = `
        {
          Solana {
            DEXTrades(
              where: {
                Trade: {
                  Buy: {Currency: {MintAddress: {is: "${mintAddress}"}}},
                  Sell: {Currency: {MintAddress: {is: "So11111111111111111111111111111111111111112"}}}
                },
                Transaction: {Result: {Success: true}},
                Block: {Time: {since: "${since.toISOString()}", till: "${till.toISOString()}"}}
              }
              orderBy: {ascending: Block_Time}
            ) {
              Block {
                Time
              }
              Trade {
                Buy {
                  Price
                  PriceInUSD
                  Currency {
                    Name
                    Symbol
                    MintAddress
                  }
                }
              }
            }
          }
        }
        `;

        const bitqueryResponse = await fetch(BITQUERY_API_URL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-API-KEY": BITQUERY_API_KEY,
            },
            body: JSON.stringify({ query }),
        });

        if (!bitqueryResponse.ok) {
            const errorData = await bitqueryResponse.json();
            console.error("Bitquery API error response:", JSON.stringify(errorData, null, 2));
            throw new Error(`Bitquery API request failed with status ${bitqueryResponse.status}`);
        }

        const data = await bitqueryResponse.json();

        if (!data.data || !data.data.Solana || !data.data.Solana.DEXTrades) {
            console.warn("Bitquery response did not contain expected DEXTrades data");
            return res.status(200).json({ success: true, data: [] });
        }

        const trades = data.data.Solana.DEXTrades;
        const aggregatedData = aggregateTradesByInterval(trades, 5);

        return res.status(200).json({
            success: true,
            data: aggregatedData,
        });
    } catch (error) {
        console.error("Token chart API error:", error);
        return res.status(500).json(
            {
                success: false,
                error: error instanceof Error ? error.message : "Failed to fetch chart data",
            },
        );
    }
}