import { useState, useEffect } from 'react';

const Animation = () => {
  const [activeButton, setActiveButton] = useState(null);
  const buttons = [
    { text: "cnkjdsncdcnkdnckdsacdsacdscds" },
    { text: "ccdsancjdcdcdc" }
  ];

  const colorClass = "bg-purple-500";
  const animatedClass = "animate-vibrate";

  // Handle button animation sequence
  useEffect(() => {
    let timeout1;
    let timeout2;
    let interval;

    const startAnimationCycle = () => {
      // First button vibrates immediately
      setActiveButton(0);
      
      // Second button vibrates after a delay (e.g., 1.5 seconds)
      timeout1 = setTimeout(() => {
        setActiveButton(1);
      }, 1500);
      
      // Repeat the cycle after both animations complete (e.g., every 3 seconds)
      timeout2 = setTimeout(() => {
        interval = setInterval(() => {
          setActiveButton(0);
          setTimeout(() => {
            setActiveButton(1);
          }, 1500);
        }, 3000);
      }, 3000);
    };

    startAnimationCycle();

    return () => {
      clearTimeout(timeout1);
      clearTimeout(timeout2);
      clearInterval(interval);
    };
  }, []);

  return (
    <div className="flex items-center gap-4 mt-6  flex-wrap justify-center">
      {buttons.map((button, index) => (
        <button
          key={index}
          className={`${colorClass} text-white py-2 px-4  rounded-sm cursor-pointer hover:opacity-90 ${
            activeButton === index ? animatedClass : ''
          }`}
        >
          {button.text}
        </button>
      ))}

      {/* Animation styles */}
      <style jsx>{`
        @keyframes vibrate {
          0% { transform: translate(0, 0) rotate(0deg); }
          10% { transform: translate(-1px, -1px) rotate(-1deg); }
          20% { transform: translate(-2px, 0) rotate(1deg); }
          30% { transform: translate(2px, 1px) rotate(0deg); }
          40% { transform: translate(1px, -1px) rotate(1deg); }
          50% { transform: translate(-1px, 2px) rotate(-1deg); }
          60% { transform: translate(-2px, 1px) rotate(0deg); }
          70% { transform: translate(2px, 1px) rotate(-1deg); }
          80% { transform: translate(-1px, -1px) rotate(1deg); }
          90% { transform: translate(1px, 2px) rotate(0deg); }
          100% { transform: translate(0, 0) rotate(0deg); }
        }
        .animate-vibrate {
          animation: vibrate 0.5s linear;
        }
      `}</style>
    </div>
  );
};

export default Animation;