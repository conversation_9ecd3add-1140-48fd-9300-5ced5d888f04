const DevnetTransaction = require('../models/DevnetTransaction');

// Get all devnet transactions (with pagination)
exports.getAllDevnetTransactions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Fetch raw transactions
    const transactionsRaw = await DevnetTransaction.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Fetch token details for all unique token IDs
    const Token = require('../models/token.model');
    const tokenIds = [...new Set(transactionsRaw.map(t => t.token))];
    const tokens = await Token.find({ _id: { $in: tokenIds } }).lean();
    const tokenMap = Object.fromEntries(tokens.map(t => [t._id.toString(), t]));

    // Attach token object to each transaction
    const transactions = transactionsRaw.map(t => ({
      ...t,
      token: tokenMap[t.token] || { _id: t.token, name: t.token, symbol: '', address: '' }
    }));

    const total = await DevnetTransaction.countDocuments();

    res.json({
      transactions,
      page,
      totalPages: Math.ceil(total / limit),
      total
    });
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch devnet transactions', error: err.message });
  }
};

// Get a single devnet transaction by ID
exports.getDevnetTransactionById = async (req, res) => {
  try {
    const transaction = await DevnetTransaction.findById(req.params.id);
    if (!transaction) {
      return res.status(404).json({ message: 'Devnet transaction not found' });
    }
    res.json(transaction);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch devnet transaction', error: err.message });
  }
};

// Create a new devnet transaction
exports.createDevnetTransaction = async (req, res) => {
  try {
    const newTransaction = new DevnetTransaction(req.body);
    await newTransaction.save();
    res.status(201).json(newTransaction);
  } catch (err) {
    res.status(400).json({ message: 'Failed to create devnet transaction', error: err.message });
  }
};

// Update a devnet transaction
exports.updateDevnetTransaction = async (req, res) => {
  try {
    const updated = await DevnetTransaction.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    );
    if (!updated) {
      return res.status(404).json({ message: 'Devnet transaction not found' });
    }
    res.json(updated);
  } catch (err) {
    res.status(400).json({ message: 'Failed to update devnet transaction', error: err.message });
  }
};

// Delete a devnet transaction
exports.deleteDevnetTransaction = async (req, res) => {
  try {
    const deleted = await DevnetTransaction.findByIdAndDelete(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: 'Devnet transaction not found' });
    }
    res.json({ message: 'Devnet transaction deleted' });
  } catch (err) {
    res.status(500).json({ message: 'Failed to delete devnet transaction', error: err.message });
  }
};

// (Optional) Get devnet stats
exports.getDevnetStats = async (req, res) => {
  try {
    const total = await DevnetTransaction.countDocuments();
    const completed = await DevnetTransaction.countDocuments({ status: 'completed' });
    const failed = await DevnetTransaction.countDocuments({ status: 'failed' });
    res.json({ total, completed, failed });
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch devnet stats', error: err.message });
  }
}; 