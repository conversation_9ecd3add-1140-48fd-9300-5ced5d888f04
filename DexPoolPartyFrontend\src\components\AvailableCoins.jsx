'use client';
import { useTokenContext } from '@/contexts/TokenContext.js';
import { useTrending } from '@/contexts/trendingData.js';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function AvailableCoins() {
  const { trendingData, loading, error, refreshData } = useTrending();
  // const { tokenIds, addTokenIdIfNotExists } = useTokenContext();
  const [resolvedImages, setResolvedImages] = useState({});

  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 300000); // 5 mins
    return () => clearInterval(interval);
  }, [refreshData]);

  useEffect(() => {
    if (!trendingData || trendingData.length === 0) {
      setResolvedImages({});
      return;
    }

    // Helper: fetch metadata from logo and get image URL
    async function fetchMetadata(uri) {
      try {
        const res = await fetch(uri);
        if (!res.ok) throw new Error('Network response not ok');
        const json = await res.json();
        return json.image || null;
      } catch {
        return null;
      }
    }

    async function fetchAllMetadata() {
      const newImages = {};
      await Promise.all(
        trendingData.map(async (coin) => {
          if (coin.logo && coin.logo.startsWith('http')) {
            const imgUrl = await fetchMetadata(coin.logo);
            newImages[coin.id] = imgUrl;
          } else {
            newImages[coin.id] = null;
          }
        })
      );
      setResolvedImages(newImages);
    }

    fetchAllMetadata();
  }, [trendingData]);

  const formatNumber = (num) => {
    const parsedNum = Number(num);
    if (!num) return '0';
    if (num < 1000) return parsedNum.toFixed(2);
    return new Intl.NumberFormat('en-US', {
      notation: 'compact',
      maximumFractionDigits: 1
    }).format(num);
  };

  // Component to handle Image fallback
  const CoinImage = ({ src, alt }) => {
    const [imgSrc, setImgSrc] = useState(src || '/token.png');

    return (
      <Image
        alt={alt}
        width={68}
        height={68}
        className="rounded-md object-cover"
        src={imgSrc}
        onError={() => setImgSrc('/token.png')}
      />
    );
  };

  const SkeletonCard = () => (
    <div className="flex flex-row w-full gap-[12px] pl-[12px] pr-[12px] sm:pr-[16px] pt-[12px] pb-[2px] justify-start items-center bg-[#1e1e1e] rounded-xl animate-pulse">
      {/* Image Placeholder */}
      <div className="w-[74px] h-[74px] bg-gray-700 rounded-[4px]"></div>

      {/* Info Placeholder */}
      <div className="flex flex-col flex-1 h-full gap-[12px] pt-[4px] pb-[12px]">
        <div className="flex flex-col gap-[4px]">
          <div className="h-[18px] bg-gray-700 rounded w-1/4"></div>
          <div className="h-[18px] bg-gray-700 rounded w-1/2"></div>
          <div className="flex gap-2 items-end">
            <div className="h-[16px] w-[30px] bg-gray-600 rounded"></div>
            <div className="h-[16px] w-[60px] bg-gray-600 rounded"></div>
          </div>
        </div>

        <div className="flex gap-3 items-center">
          <div className="h-[14px] bg-gray-700 rounded w-[70px]"></div>
          <div className="flex gap-2">
            <div className="h-[14px] w-[16px] bg-gray-600 rounded-full"></div>
            <div className="h-[14px] w-[16px] bg-gray-600 rounded-full"></div>
            <div className="h-[14px] w-[16px] bg-gray-600 rounded-full"></div>
          </div>
          <div className="flex-1 flex justify-end gap-1">
            <div className="h-[14px] bg-gray-600 rounded w-[15px]"></div>
            <div className="h-[16px] bg-gray-700 rounded w-[60px]"></div>
          </div>
        </div>
      </div>
    </div>
  );


  if (loading || error) return (
    <div className='flex justify-center items-center w-full'>
      <div className="px-4 py-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4">
        {[...Array(10)].map((_, i) => <SkeletonCard key={i} />)}
      </div>
    </div>
  );



  if (!trendingData || trendingData.length === 0) return (
    <div className="px-4 py-8 text-center text-gray-400">
      No coins found
      <button
        onClick={refreshData}
        className="ml-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Refresh
      </button>
    </div>
  );

  return (
    <div className="px-4 w-full">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">Available Coins</h2>
        <button
          onClick={refreshData}
          className="px-3 py-1 text-sm bg-[#1C2530] text-gray-300 rounded hover:bg-[#2F3741] transition"
        >
          Refresh
        </button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-5">
        {trendingData.filter(coin => !!coin.id).map((coin) => {
          const imageUrl = resolvedImages[coin.id] || '/token.png';
          return (
            <Link
              key={coin.id}
              href={`/${coin.id}`}
              className="flex flex-row w-full gap-4 p-4 justify-start items-center bg-[#1e1e1e] rounded-xl hover:bg-[#2a2a2a] transition-colors shadow-md cursor-pointer"
            >
                <div className="w-[74px] h-[74px] relative">
                  <div className="w-full h-full relative group">
                    <CoinImage
                      alt={coin.name}
                      src={imageUrl}
                    />
                    <button className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center pointer-events-none">
                      <i className="ri-camera-line text-white text-[24px]"></i>
                    </button>
                  </div>
                </div>

                <div className="flex flex-col flex-1 gap-2">
                  <div className="flex justify-between items-center">
                    <h3 className="text-white text-lg font-semibold truncate">{coin.name.length > 20 ? `${coin.name.slice(0, 15)}...` : coin.name}
</h3>
                    <span className="text-xs text-gray-400">MC</span>
                  </div>
                  <div className="text-yellow-400 font-medium">${formatNumber(coin.quote?.USD?.market_cap)}</div>
                  <div className="flex items-center justify-between">
                    <span className={`${coin.quote?.USD?.percent_change_24h >= 0 ? 'text-green-400' : 'text-red-500'} font-medium`}>
                      ${coin.quote?.USD?.price?.toFixed(6) || 'N/A'}
                    </span>
                    <span className="text-sm text-white font-medium">Vol: ${formatNumber(coin.quote?.USD?.volume_24h)}</span>
                  </div>
                </div>
            </Link>
          )
        })}
      </div>
    </div>
  );
}
