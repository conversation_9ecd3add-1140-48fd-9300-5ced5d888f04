'use client';
import React from 'react';
import { usePathname } from 'next/navigation';

const Footer = ({ sidebarCollapsed }) => {
  const pathname = usePathname();
  const isAdvancePage = pathname === '/advancePage';
  // Determine margin-left based on sidebar state
  let marginLeft = '';
  if (!isAdvancePage) {
    marginLeft = sidebarCollapsed ? 'md:ml-[70px]' : 'md:ml-[250px] lg:ml-[300px]';
  }
  // Hide footer on mobile for advance page
  const hideOnMobile = isAdvancePage ? 'hidden sm:block' : '';
  return (
    <footer className={`
      bg-black/90 backdrop-blur-sm border-t border-white/10
      fixed bottom-0 left-0 right-0 z-40
      px-4 py-3 sm:py-4 text-gray-400
      ${marginLeft} ${hideOnMobile}
    `}>
      <div className="container mx-auto max-w-7xl">
        <div className="flex flex-col items-center gap-3 sm:flex-row sm:justify-between sm:gap-0">
          {/* Copyright - moves to left on larger screens */}
          <p className="text-xs sm:text-sm text-gray-400 order-1 sm:order-none">
            © {new Date().getFullYear()} Dexapoolparty. All rights reserved.
          </p>
          
          {/* Links - centered on mobile, middle on larger screens */}
          <div className="flex flex-wrap justify-center gap-x-4 gap-y-2 order-3 w-full sm:w-auto sm:order-none sm:absolute sm:left-1/2 sm:transform sm:-translate-x-1/2">
            <a href="/privacy" className="text-xs sm:text-sm hover:text-gray-300 transition-colors">
              Privacy Policy
            </a>
            <a href="/terms" className="text-xs sm:text-sm hover:text-gray-300 transition-colors">
              Terms of Service
            </a>
            <a href="/fees" className="text-xs sm:text-sm hover:text-gray-300 transition-colors">
              Fees
            </a>
          </div>
          
          {/* Report button - moves to right on larger screens */}
          <button 
            className="text-xs sm:text-sm hover:text-gray-300 transition-colors order-2 sm:order-none"
            onClick={() => window.open('/report', '_blank')}
          >
            Report an Issue
          </button>
        </div>
      </div>
    </footer>
  );
}

export default Footer;