"use client"

import { useState, useEffect } from "react"
import { X, Filter } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

const FilterModal = ({ isOpen, onClose, onApplyFilters, currentFilters }) => {
  const [filters, setFilters] = useState({
    minMarketCap: "",
    maxMarketCap: "",
    minVolume: "",
    change24hType: "any",
    categories: [],
  })

  useEffect(() => {
    if (isOpen) {
      setFilters(currentFilters)
    }
  }, [isOpen, currentFilters])

  const handleInputChange = (field, value) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleCategoryToggle = (category) => {
    setFilters((prev) => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter((c) => c !== category)
        : [...prev.categories, category],
    }))
  }

  const handleApply = () => {
    onApplyFilters(filters)
    onClose()
  }

  const handleReset = () => {
    const resetFilters = {
      minMarketCap: "",
      maxMarketCap: "",
      minVolume: "",
      change24hType: "any",
      categories: [],
    }
    setFilters(resetFilters)
    onApplyFilters(resetFilters)
  }

  const categories = [
    { id: "newly-created", label: "Newly Created", color: "bg-green-500" },
    { id: "about-to-graduate", label: "About to Graduate", color: "bg-yellow-500" },
    { id: "graduated", label: "Graduated", color: "bg-blue-500" },
    { id: "featured", label: "Featured", color: "bg-purple-500" },
  ]

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />

      {/* Modal */}
      <div className="relative bg-[#111115] border border-zinc-700/50 rounded-2xl shadow-2xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-zinc-700/50">
          <div className="flex items-center gap-3">
            <Filter className="w-5 h-5 text-purple-400" />
            <h2 className="text-xl font-bold text-white">Filter Tokens</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0 hover:bg-zinc-700/50">
            <X className="w-4 h-4 text-zinc-400" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Market Cap Range */}
          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-zinc-200">Market Cap Range</h3>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="text-xs text-zinc-400 mb-1 block">Min ($)</label>
                <input
                  type="number"
                  placeholder="0"
                  value={filters.minMarketCap}
                  onChange={(e) => handleInputChange("minMarketCap", e.target.value)}
                  className="w-full bg-zinc-800/50 text-white px-3 py-2 rounded-lg border border-zinc-700/50 focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20 text-sm"
                />
              </div>
              <div>
                <label className="text-xs text-zinc-400 mb-1 block">Max ($)</label>
                <input
                  type="number"
                  placeholder="∞"
                  value={filters.maxMarketCap}
                  onChange={(e) => handleInputChange("maxMarketCap", e.target.value)}
                  className="w-full bg-zinc-800/50 text-white px-3 py-2 rounded-lg border border-zinc-700/50 focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20 text-sm"
                />
              </div>
            </div>
          </div>

          {/* Volume */}
          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-zinc-200">Minimum Volume</h3>
            <input
              type="number"
              placeholder="0"
              value={filters.minVolume}
              onChange={(e) => handleInputChange("minVolume", e.target.value)}
              className="w-full bg-zinc-800/50 text-white px-3 py-2 rounded-lg border border-zinc-700/50 focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20 text-sm"
            />
          </div>

          {/* Price Change */}
          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-zinc-200">24h Price Change</h3>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: "any", label: "Any" },
                { value: "positive", label: "Positive" },
                { value: "negative", label: "Negative" },
              ].map((option) => (
                <Button
                  key={option.value}
                  variant={filters.change24hType === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleInputChange("change24hType", option.value)}
                  className={`text-xs ${
                    filters.change24hType === option.value
                      ? "bg-purple-600 text-white border-purple-600"
                      : "bg-zinc-800/50 text-zinc-300 border-zinc-700/50 hover:text-white hover:bg-zinc-700/50"
                  }`}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Categories */}
          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-zinc-200">Categories</h3>
            <div className="space-y-2">
              {categories.map((category) => (
                <div
                  key={category.id}
                  onClick={() => handleCategoryToggle(category.id)}
                  className="flex items-center gap-3 p-3 rounded-lg bg-zinc-800/30 hover:bg-zinc-700/30 cursor-pointer transition-colors"
                >
                  <div className="flex items-center gap-2 flex-1">
                    <span className={`w-3 h-3 ${category.color} rounded-full`}></span>
                    <span className="text-sm text-zinc-200">{category.label}</span>
                  </div>
                  {filters.categories.includes(category.id) && (
                    <Badge variant="secondary" className="text-xs bg-purple-600/20 text-purple-400">
                      ✓
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Active Filters Summary */}
          {(filters.minMarketCap ||
            filters.maxMarketCap ||
            filters.minVolume ||
            filters.change24hType !== "any" ||
            filters.categories.length > 0) && (
            <div className="space-y-3">
              <h3 className="text-sm font-semibold text-zinc-200">Active Filters</h3>
              <div className="flex flex-wrap gap-2">
                {filters.minMarketCap && (
                  <Badge variant="outline" className="text-xs border-zinc-600/50 text-zinc-300">
                    Min MC: ${Number(filters.minMarketCap).toLocaleString()}
                  </Badge>
                )}
                {filters.maxMarketCap && (
                  <Badge variant="outline" className="text-xs border-zinc-600/50 text-zinc-300">
                    Max MC: ${Number(filters.maxMarketCap).toLocaleString()}
                  </Badge>
                )}
                {filters.minVolume && (
                  <Badge variant="outline" className="text-xs border-zinc-600/50 text-zinc-300">
                    Min Vol: ${Number(filters.minVolume).toLocaleString()}
                  </Badge>
                )}
                {filters.change24hType !== "any" && (
                  <Badge variant="outline" className="text-xs border-zinc-600/50 text-zinc-300">
                    {filters.change24hType === "positive" ? "Positive" : "Negative"} Change
                  </Badge>
                )}
                {filters.categories.map((categoryId) => {
                  const category = categories.find((c) => c.id === categoryId)
                  return (
                    <Badge key={categoryId} variant="outline" className="text-xs border-zinc-600/50 text-zinc-300">
                      {category?.label}
                    </Badge>
                  )
                })}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between gap-3 p-6 border-t border-zinc-700/50">
          <Button
            variant="outline"
            onClick={handleReset}
            className="bg-zinc-800/50 border-zinc-700/50 text-zinc-300 hover:bg-zinc-700/50 hover:border-zinc-600/50"
          >
            Reset
          </Button>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="bg-zinc-800/50 border-zinc-700/50 text-zinc-300 hover:bg-zinc-700/50 hover:border-zinc-600/50"
            >
              Cancel
            </Button>
            <Button onClick={handleApply} className="bg-purple-600 text-white hover:bg-purple-700 border-purple-600">
              Apply Filters
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FilterModal
