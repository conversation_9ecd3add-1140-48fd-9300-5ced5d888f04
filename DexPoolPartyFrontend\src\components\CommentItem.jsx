import React from 'react'

const CommentItem = ({ comment, openReplies, toggleReplies, handleOpenReplyModal, handleLike }) => {
  return (
    <div
      key={comment._id}
      className={`${comment.parentId ? 'bg-[#3a3c4a] p-3 rounded text-slate-300 text-sm' : 'grid gap-1 mt-2 overflow-auto bg-[#2e303a] p-4 rounded-md text-sm text-slate-200'
        }`}
    >
      {/* Comment Header */}
      <div className="flex flex-wrap items-center gap-2 text-xs text-slate-400">
        <img
          alt="user avatar"
          loading="lazy"
          width={comment.parentId ? 16 : 20}
          height={comment.parentId ? 16 : 20}
          decoding="async"
          className="rounded h-4 w-4"
          src={comment.profileImage || "/token.png"}
          style={{ color: "transparent" }}
        />
        <span
          className="flex gap-1 rounded px-1 hover:underline text-black text-xs font-medium"
          style={{ backgroundColor: "rgb(201, 240, 243)" }}
        >
          {comment.userId || "Anonymous"}
        </span>
        <span className="text-xs text-gray-500 ml-1">{new Date(comment.createdAt).toLocaleString()}</span>
        <button
          className="flex items-center gap-1 ml-2 focus:outline-none bg-transparent border-none p-0 hover:opacity-80"
          style={{ boxShadow: 'none' }}
          onClick={() => handleLike(comment._id, comment.liked)}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={comment.parentId ? 16 : 20}
            height={comment.parentId ? 16 : 20}
            viewBox="0 0 24 24"
            fill={comment.liked ? 'red' : 'none'}
            stroke={comment.liked ? 'red' : '#e5e7eb'}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="lucide lucide-heart"
            aria-hidden="true"
          >
            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path>
          </svg>
          <span className={`ml-1 font-semibold ${comment.liked ? 'text-red-400' : 'text-gray-400'}`}>{Array.isArray(comment.likedBy) ? comment.likedBy.length : 0}</span>
        </button>
        {/* Reply button - only show on top-level comments */}
        {!comment.parentId && (
          <button
            onClick={() => handleOpenReplyModal(comment._id)}
            className="ml-auto text-xs text-blue-400 hover:underline font-medium"
          >
            Reply
          </button>
        )}
      </div>

      {/* Comment Content */}
      <div className="mt-1 text-slate-200 text-sm leading-relaxed">{comment.content}</div>

      {/* Optional image */}
      {comment.imageUrl && (
        <img
          src={comment.imageUrl}
          alt="uploaded"
          className={`mt-2 max-h-${comment.parentId ? '32' : '40'} object-contain rounded`}
        />
      )}

      {/* Replies toggle */}
      {comment.replies?.length > 0 && (
        <button
          onClick={() => toggleReplies(comment._id)}
          className="mt-2 text-xs text-indigo-400 hover:underline"
        >
          {openReplies[comment._id]
            ? `Hide ${comment.replies.length} ${comment.replies.length === 1 ? 'Reply' : 'Replies'}`
            : `View ${comment.replies.length} ${comment.replies.length === 1 ? 'Reply' : 'Replies'}`}
        </button>
      )}

      {/* Replies Section - recursively render replies */}
      {openReplies[comment._id] && comment.replies?.length > 0 && (
        <div className="ml-6 mt-3 space-y-3 border-l-2 border-l-slate-700 pl-4 bg-[#23242c] rounded-lg py-2">
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply._id}
              comment={reply}
              openReplies={openReplies}
              toggleReplies={toggleReplies}
              handleOpenReplyModal={handleOpenReplyModal}
              handleLike={handleLike}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default CommentItem