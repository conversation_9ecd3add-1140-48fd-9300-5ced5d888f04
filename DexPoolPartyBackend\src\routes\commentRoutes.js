const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addComment, getCommentsByItem, addReply, likeComment, unlikeComment } = require('../controllers/commentController');

/**
 * @swagger
 * tags:
 *   name: Comments
 *   description: Comment management API
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Comment:
 *       type: object
 *       required:
 *         - tokenId
 *         - userId
 *         - content
 *       properties:
 *         _id:
 *           type: string
 *           description: Auto-generated comment ID
 *         tokenId:
 *           type: string
 *           description: ID of the token the comment is for
 *         userId:
 *           type: string
 *           description: ID of the user who made the comment
 *         username:
 *           type: string
 *         profileImage:
 *           type: string
 *         content:
 *           type: string
 *         imageUrl:
 *           type: string
 *         parentId:
 *           type: string
 *         replies:
 *           type: array
 *           items:
 *             type: string
 *         likes:
 *           type: number
 *         isActive:
 *           type: boolean
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 */

/**
 * @swagger
 * /api/comments/{tokenId}:
 *   get:
 *     summary: Get all comments for a token
 *     tags: [Comments]
 *     parameters:
 *       - in: path
 *         name: tokenId
 *         required: true
 *         schema:
 *           type: string
 *         description: Token ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of comments per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [newest, oldest, most_liked]
 *           default: newest
 *         description: Sort order for comments
 *     responses:
 *       200:
 *         description: List of comments
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Comment'
 *       404:
 *         description: Token not found
 */

/**
 * @swagger
 * /api/comments/add-comment/{id}:
 *   post:
 *     summary: Add a new comment to a token
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Token ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 description: Comment content
 *                 minLength: 1
 *                 maxLength: 1000
 *               imageUrl:
 *                 type: string
 *                 description: Optional image URL for the comment
 *     responses:
 *       201:
 *         description: Comment added successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Comment'
 *       400:
 *         description: Invalid input
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Token not found
 */

/**
 * @swagger
 * /api/comments/add-reply/{commentId}:
 *   post:
 *     summary: Add a reply to a comment
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: commentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Comment ID to reply to
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 description: Reply content
 *                 minLength: 1
 *                 maxLength: 1000
 *               imageUrl:
 *                 type: string
 *                 description: Optional image URL for the reply
 *     responses:
 *       201:
 *         description: Reply added successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Comment'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Comment not found
 */

/**
 * @swagger
 * /api/comments/like/{commentId}:
 *   post:
 *     summary: Like a comment
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: commentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Comment ID to like
 *     responses:
 *       200:
 *         description: Comment liked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 likes:
 *                   type: number
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Comment not found
 */

/**
 * @swagger
 * /api/comments/unlike/{commentId}:
 *   post:
 *     summary: Unlike a comment
 *     tags: [Comments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: commentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Comment ID to unlike
 *     responses:
 *       200:
 *         description: Comment unliked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 likes:
 *                   type: number
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Comment not found
 */

// POST route to add a new comment
router.post('/add-comment/:id', auth, addComment);

// GET route to fetch comments for a specific item (e.g., token)
router.get('/:tokenId', getCommentsByItem);

// POST route to add a reply to a comment
router.post("/add-reply/:commentId", auth, addReply);

// POST routes for like/unlike functionality
router.post('/like/:commentId', auth, likeComment);
router.post('/unlike/:commentId', auth, unlikeComment);

module.exports = router; 