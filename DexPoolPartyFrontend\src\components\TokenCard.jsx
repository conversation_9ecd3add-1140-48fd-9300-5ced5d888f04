import React from 'react';
import {
    TrendingDown,
    Volume2,
    DollarSign,
    Star,
    TrendingUp
} from "lucide-react";

const TokenCard = ({ token, isLoading }) => {
    if (isLoading) {
        return (
            <div className="bg-[#2A2A2A] p-4 w-11/12 rounded-xl border border-gray-700/50 animate-pulse">
                <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gray-700 rounded-full"></div>
                        <div>
                            <div className="w-20 h-4 bg-gray-700 rounded mb-1"></div>
                            <div className="w-16 h-3 bg-gray-700 rounded"></div>
                        </div>
                    </div>
                    <div className="w-8 h-8 bg-gray-700 rounded-full"></div>
                </div>
                <div className="space-y-2">
                    <div className="w-full h-3 bg-gray-700 rounded"></div>
                    <div className="w-3/4 h-3 bg-gray-700 rounded"></div>
                </div>
            </div>
        );
    }

    const price = token?.quote?.USD?.price || 0;
    const marketCap = token?.quote?.USD?.market_cap || 0;
    const volume = token?.quote?.USD?.volume_24h || 0;
    const priceChange = token?.quote?.USD?.percent_change_24h || 0;
    const isPositive = priceChange >= 0;

    return (
        <div className="group bg-[#2A2A2A] p-4 rounded-xl border border-gray-700/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10 cursor-pointer">
            {/* Header */}
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                    <div className="relative">
                        <img
                            src={token.logo}
                            alt={token.name}
                            className="w-10 h-10 rounded-full border border-gray-600 group-hover:border-purple-500/50 transition-colors"
                            onError={(e) => {
                                e.target.src = `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><rect width="40" height="40" fill="%23374151"/><text x="20" y="25" text-anchor="middle" fill="white" font-size="14">${token.symbol?.charAt(0) || '?'}</text></svg>`;
                            }}
                        />
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        </div>
                    </div>
                    <div>
                        <h3 className="text-white font-semibold text-sm group-hover:text-purple-400 transition-colors">
                            {token.name}
                        </h3>
                        <p className="text-gray-400 text-xs uppercase tracking-wide">
                            {token.symbol}
                        </p>
                    </div>
                </div>
                <button className="w-8 h-8 bg-gray-700/50 hover:bg-purple-600 rounded-full flex items-center justify-center transition-all duration-200 group-hover:scale-110">
                    <Star className="w-4 h-4 text-gray-400 group-hover:text-white" />
                </button>
            </div>

            {/* Price & Change */}
            <div className="flex items-center justify-between mb-3">
                <div>
                    <p className="text-white font-bold text-lg">
                        ${price.toLocaleString('en-US', {
                            minimumFractionDigits: price < 1 ? 6 : 2,
                            maximumFractionDigits: price < 1 ? 6 : 2
                        })}
                    </p>
                    <p className="text-gray-400 text-xs">
                        MCap: ${(marketCap / 1e6).toFixed(2)}M
                    </p>
                </div>
                <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${isPositive
                    ? 'bg-purple-500/20 text-purple-400'
                    : 'bg-red-500/20 text-red-400'
                    }`}>
                    {isPositive ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                    {Math.abs(priceChange).toFixed(2)}%
                </div>
            </div>

            {/* Volume & Rank */}
            <div className="flex items-center justify-between text-xs text-gray-400 mb-3">
                <div className="flex items-center gap-1">
                    <Volume2 className="w-3 h-3" />
                    <span>Vol: ${(volume / 1e6).toFixed(2)}M</span>
                </div>
                <div className="flex items-center gap-1">
                    <TrendingUp className="w-3 h-3" />
                    <span>Rank #{token.cmc_rank || 'N/A'}</span>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
                <button className="flex-1 bg-purple-600 hover:bg-purple-500 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors duration-200 flex items-center justify-center gap-1">
                    <DollarSign className="w-3 h-3" />
                    Buy
                </button>
                <button className="flex-1 bg-gray-700 hover:bg-gray-600 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors duration-200">
                    Trade
                </button>
            </div>
        </div>
    );
};

export default TokenCard;
