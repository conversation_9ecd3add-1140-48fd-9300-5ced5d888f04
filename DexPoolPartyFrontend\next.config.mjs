/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    // Remove the 'domains' array if you're using 'remotePatterns' to allow all.
    // If you still want to explicitly list some *and* allow all others,
    // you would combine both, but for "select all" remotePatterns is sufficient.
    // domains: [
    //   'cdn.pixabay.com',
    //   's2.coinmarketcap.com',
    //   'lh3.googleusercontent.com',
    //   'localhost',
    //   'coin-images.coingecko.com',
    //   'ipfs.io',
    //   "pbs.twimg.com",
    //   "metadata.pumplify.eu",
    //   'cf-ipfs.com',
    //   "encrypted-tbn0.gstatic.com",
    //   "api.dexpoolparty.com",
    //   'www.gstatic.com'
    // ],
    remotePatterns: [
      {
        protocol: 'http', // Allow HTTP
        hostname: '**',   // Match any hostname
      },
      {
        protocol: 'https', // Allow HTTPS
        hostname: '**',   // Match any hostname
      },
      {
        protocol: 'https',
        hostname: 'img.bitquery.io',
        port: '',
        pathname: '/**', // Allows any path on this domain
      },
    ],
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'unsafe-none', // ⚠️ use 'unsafe-none' to prevent Firebase popup issue
          },
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'unsafe-none', // optional: remove if not using SharedArrayBuffer
          },
        ],
      },
    ];
  },
};

export default nextConfig;