"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { RefreshCw, TrendingUp, TrendingDown, Search } from "lucide-react"
import { Badge } from "@/components/ui/badge"

const formatVolume = (volume) => {
    if (!volume) return "0.00"
    const numVolume = typeof volume === "number" ? volume : Number.parseFloat(volume.toString())
    return isNaN(numVolume) ? "0.00" : numVolume.toFixed(2)
}

export default function TokenChart({ initialSelectedToken }) {
    const [trendingTokens, setTrendingTokens] = useState([])
    const [selectedToken, setSelectedToken] = useState(null) // Initialize as null
    const [chartData, setChartData] = useState([])
    const [loading, setLoading] = useState(false)
    const [loadingTrending, setLoadingTrending] = useState(true) // Keep true initially
    const [error, setError] = useState(null)
    const [lastPrice, setLastPrice] = useState(null)
    const [priceChange, setPriceChange] = useState(null)
    const [searchTerm, setSearchTerm] = useState("")

    const fetchTokenLogo = async (token) => {
        try {
            const res = await fetch(token.logo);
            const data = await res.json();
            console.log("Fetched token logo data:", data);

            console.log(data);

            const tokenWithLogo = {
                ...token,
                logo: data.image
            };

            setSelectedToken(tokenWithLogo);
        } catch (error) {
            console.error("Failed to fetch token logo:", error);
            setSelectedToken(token);
        }
    };


    useEffect(() => {
        const fetchTrendingTokens = async () => {
            setLoadingTrending(true)
            try {
                const response = await fetch("/api/trending")
                const result = await response.json()
                const preselected = result.data.find(t => t.id === initialSelectedToken);

                if (result.success && result.data.length > 0) {
                    setTrendingTokens(result.data)
                    if (initialSelectedToken) {
                        console.log("Preselected token:", preselected);

                        if (preselected) {
                            fetchTokenLogo(preselected);
                        } else {
                            console.warn(`Initial token with address ${initialSelectedToken} not found in trending tokens.`);
                            fetchTokenLogo(preselected);
                        }
                    } else {
                        setSelectedToken(preselected)
                    }
                } else {
                    setError("No trending tokens found or API error for trending.")
                }
            } catch (err) {
                console.error("Error fetching trending tokens:", err)
                setError("Failed to fetch trending tokens. Please try again later.")
            } finally {
                setLoadingTrending(false)
            }
        }

        fetchTrendingTokens()
    }, [initialSelectedToken])

    useEffect(() => {
        const fetchChartData = async () => {
            if (!selectedToken) {
                setChartData([]);
                setLastPrice(null);
                setPriceChange(null);
                return;
            }

            setLoading(true)
            setError(null)

            try {
                const response = await fetch(`/api/token-chart?address=${initialSelectedToken}&hours=24`)
                const result = await response.json()

                if (!result.success) {
                    throw new Error(result.error || "Unknown error fetching chart data.")
                }

                const formattedData = result.data.map((point) => ({
                    ...point,
                    formattedTime: new Date(point.timestamp * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                }));

                setChartData(formattedData)

                if (formattedData.length > 1) {
                    const currentPrice = formattedData[formattedData.length - 1].value
                    const previousPrice = formattedData[0].value
                    const change = ((currentPrice - previousPrice) / previousPrice) * 100

                    setLastPrice(currentPrice)
                    setPriceChange(change)
                } else if (selectedToken.quote && selectedToken.quote.USD && selectedToken.quote.USD.price) {
                    setLastPrice(selectedToken.quote.USD.price)
                    setPriceChange(0)
                } else {
                    setLastPrice(null);
                    setPriceChange(null);
                }

            } catch (err) {
                setError(err instanceof Error ? err.message : "Failed to fetch chart data")
                console.error("Error fetching chart data:", err)

                if (selectedToken.quote && selectedToken.quote.USD && selectedToken.quote.USD.price) {
                    setLastPrice(selectedToken.quote.USD.price)
                    setPriceChange(0)
                } else {
                    setLastPrice(null);
                    setPriceChange(null);
                }
                setChartData([])
            } finally {
                setLoading(false)
            }
        }

        fetchChartData()
    }, [selectedToken])

    const handleTokenChange = (mintAddress) => {
        const token = trendingTokens.find((t) => t.mintAddress === mintAddress)
        if (token) {
            setSelectedToken(token)
        }
    }

    const handleRefresh = () => {
        if (selectedToken) {
            setSelectedToken({ ...selectedToken });
        }
    }

    const formatPrice = (price) => {
        if (price === null || price === undefined) return "N/A";
        if (price === 0) return "$0.00";
        if (Math.abs(price) < 0.000001) {
            return `$${price.toExponential(2)}`
        } else if (Math.abs(price) < 0.01) {
            return `$${price.toFixed(8)}`
        } else if (Math.abs(price) < 1) {
            return `$${price.toFixed(6)}`
        } else if (Math.abs(price) < 100) {
            return `$${price.toFixed(4)}`
        } else {
            return `$${price.toFixed(2)}`
        }
    }

    const filteredTokens = trendingTokens.filter(
        (token) =>
            token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            token.symbol.toLowerCase().includes(searchTerm.toLowerCase()),
    )

    if (loadingTrending && trendingTokens.length === 0) {
        return (
            <div className="w-full max-w-6xl mx-auto p-6">
                <div className="flex items-center justify-center h-64">
                    <div className="flex items-center gap-2">
                        <RefreshCw className="h-5 w-5 animate-spin" />
                        <span>Loading trending tokens...</span>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="w-full max-w-6xl mx-auto p-3 md:p-6 space-y-6">
            {selectedToken && (
                <Card className="bg-[#111115] border border-gray-800 text-white">
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    {selectedToken.logo && (
                                        <img
                                            src={selectedToken.logo || "/placeholder.svg"}
                                            alt={selectedToken.symbol}
                                            className="w-8 h-8 rounded-full"
                                        />
                                    )}
                                    {selectedToken.name} ({selectedToken.symbol})
                                </CardTitle>
                                <CardDescription className="text-gray-400">Live data from Bitquery • Solana DEX trades</CardDescription>
                            </div>

                            {lastPrice !== null && (
                                <div className="text-right">
                                    <div className="text-3xl font-bold">{formatPrice(lastPrice)}</div>
                                    {priceChange !== null && (
                                        <Badge variant={priceChange >= 0 ? "default" : "destructive"} className={`flex items-center justify-center gap-1 mt-1 ${priceChange >= 0 ? "bg-green-600" : "bg-red-600"} text-white`}>
                                            {priceChange >= 0 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                                            {priceChange >= 0 ? "+" : ""}
                                            {priceChange.toFixed(2)}%
                                        </Badge>
                                    )}
                                </div>
                            )}
                        </div>
                    </CardHeader>

                    <CardContent>
                        {error && (
                            <div className="mb-4 p-3 bg-yellow-900/30 text-yellow-300 rounded-md">
                                <p className="text-sm">⚠️ Chart Error: {error}</p>
                            </div>
                        )}

                        {loading ? ( // Use the 'loading' state for chart data
                            <div className="h-[400px] flex items-center justify-center">
                                <div className="flex items-center gap-2 text-gray-300">
                                    <RefreshCw className="h-5 w-5 animate-spin" />
                                    <span>Loading chart data...</span>
                                </div>
                            </div>
                        ) : chartData.length > 0 ? (
                            <div className="h-[400px] w-full">
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart data={chartData}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#444444" />
                                        <XAxis dataKey="formattedTime" tick={{ fill: '#ffffff', fontSize: 12 }} interval="preserveStartEnd" />
                                        <YAxis tick={{ fill: '#ffffff', fontSize: 9 }} tickFormatter={(value) => formatPrice(value)} />
                                        <Tooltip
                                            formatter={(value) => [formatPrice(value), "Price"]}
                                            labelFormatter={(label) => `Time: ${label}`}
                                            contentStyle={{ backgroundColor: '#333333', border: 'none', borderRadius: '4px' }}
                                            labelStyle={{ color: '#cccccc' }}
                                            itemStyle={{ color: '#ffffff' }}
                                        />
                                        <Line
                                            type="monotone"
                                            dataKey="value"
                                            stroke="#3b82f6"
                                            strokeWidth={2}
                                            dot={false}
                                            activeDot={{ r: 6, fill: "#3b82f6", stroke: "#3b82f6", strokeWidth: 2 }}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>
                        ) : (
                            <div className="h-[400px] flex items-center justify-center">
                                <div className="text-center text-gray-400">
                                    <p>No chart data available for this token.</p>
                                    <p className="text-sm mt-1">
                                        Current price: {lastPrice ? formatPrice(lastPrice) : "N/A"}
                                    </p>
                                </div>
                            </div>
                        )}

                        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-300">
                            <div>
                                <span className="text-gray-500">Current Price:</span>
                                <div className="font-semibold text-white">{formatPrice(lastPrice || (selectedToken?.quote?.USD?.price ?? null))}</div>
                            </div>
                            <div>
                                <span className="text-gray-500">24h Volume:</span>
                                <div className="font-semibold text-white">${formatVolume(selectedToken?.quote?.USD?.volume_24h)}</div>
                            </div>
                            <div>
                                <span className="text-gray-500">Data Points:</span>
                                <div className="font-semibold text-white">{chartData.length}</div>
                            </div>
                        </div>

                        <div className="mt-2 text-xs text-gray-500">
                            Last updated: {new Date().toLocaleTimeString()} • Mint Address: {initialSelectedToken}
                        </div>

                        <div className="mt-6 flex justify-end">
                            <Button onClick={handleRefresh} disabled={loading} className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
                                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                                Refresh Data
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    )
}