const { default: mongoose } = require("mongoose");
const Token = require("../models/token.model");
const User = require("../models/user.model");
const Comment = require("../models/comment.model");

// Get all tokens
const getAllTokens = async (req, res) => {
  try {
    const tokens = (await Token.find({ isActive: true })) || [];
    console.log("fetched tok", tokens);
    res.status(200).json(Array.isArray(tokens) ? tokens : []);
  } catch (error) {
    res.status(500).json({ message: error.message });
    return [];
  }
};

// Create new token
const createToken = async (req, res) => {
  console.log("creatingtoken", req.body);
  try {
    // Get user ID from authenticated request
    const userId = req.user.id; // assuming req.user is set after authentication

    // Check if user exists
    const user = await User.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Create token with request body and creator
    const tokenData = {
      _id: req.body._id || new mongoose.Types.ObjectId().toString(), // Use provided _id or generate a unique string
      ...req.body,
      media: req.file ? `uploads/${req.file.filename}` : null, // consistent with profile image format
      creator: userId,
      status: "pending", // default status
    };

    // Create new token
    const token = new Token(tokenData);
    await token.save();

    user.createdTokens = user.createdTokens || [];
    user.createdTokens.push(token._id);

    // Save the updated user document
    await user.save();

    res.status(201).json(token);
  } catch (error) {
    if (error.name === "ValidationError") {
      console.error("Validation error:", error);
      return res.status(400).json({ message: error.message });
    }
    console.error("Error creating token:", error);
    res.status(500).json({ message: "Error creating token", error });
  }
};

const createOtherPlatformToken = async (req, res) => {
  try {
    const { tokenId, coin } = req.body;

    if (!tokenId || !coin) {
      return res.status(400).json({
        success: false,
        message: "tokenId and coin data are required",
      });
    }

    // Check if token with this tokenId already exists
    const existingToken = await Token.findOne({ _id: tokenId });
    if (existingToken) {
      return res.status(409).json({
        success: false,
        message: "Token with this tokenId already exists",
      });
    }

    // Map the coin fields to your Token schema fields:
    const tokenData = {
      _id: tokenId, // Use tokenId as the _id
      name: coin.name || "No name",
      symbol: coin.symbol || "UNK",
      description: `Ranked #${coin.cmc_rank || "N/A"}, added on ${
        coin.date_added || "N/A"
      }`,
      media: coin.media || "",
      links: {
        telegram: coin.telegram || "",
        website: coin.website || "",
        twitter: coin.twitter || "",
      },
      status: "pending",
      creator: req.user?.id || "system",
      price: coin.quote?.USD?.price || 0,
      isActive: true,
      ourPlatformToken: true,
      otherTokenId: tokenId,
    };

    // Save token document
    const newToken = new Token(tokenData);
    await newToken.save();

    res.status(201).json({ success: true, token: newToken });
  } catch (error) {
    console.error("Error creating token:", error);
    res.status(500).json({ success: false, message: "Server error", error });
  }
};

// Get token by ID
const getTokenById = async (req, res) => {
  try {
    const token = await Token.findById(req.params.id);
    if (!token) {
      return res.status(404).json({ message: "Token not found" });
    }

    // Get comments for this token
    const comments = await Comment.find({
      tokenId: req.params.id,
      parentId: null, // Only get top-level comments
    }).sort({ createdAt: -1 }); // Latest comments first

    res.status(200).json({
      ...token.toJSON(),
      comments,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getUserTokenById = async (req, res) => {
  try {
    console.log("hitted");

    const user = await User.findOne({ _id: req.user.id });
    if (!user) {
      return res.status(404).json({ tokens: [] });
    }

    // user.createdTokens is an array of token IDs (strings)
    if (
      !user.createdTokens ||
      !Array.isArray(user.createdTokens) ||
      user.createdTokens.length === 0
    ) {
      return res.status(200).json({ tokens: [] });
    }

    // Fetch each token manually by ID
    const tokens = await Promise.all(
      user.createdTokens.map((tokenId) => Token.findById(tokenId))
    );

    // Filter out any null (in case some tokens were deleted)
    const validTokens = tokens.filter((token) => token !== null) || [];
    console.log("boomTokens", validTokens);

    res.status(200).json({ tokens: validTokens });
  } catch (error) {
    console.error("Error fetching tokens:", error);
    res.status(500).json({ message: error.message });
  }
};

// Update token
const updateToken = async (req, res) => {
  try {
    const token = await Token.findByIdAndUpdate(
      req.params.id,
      { ...req.body },
      { new: true, runValidators: true }
    );
    if (!token) {
      return res.status(404).json({ message: "Token not found" });
    }
    res.status(200).json(token);
  } catch (error) {
    if (error.name === "ValidationError") {
      return res.status(400).json({ message: error.message });
    }
    res.status(500).json({ message: error.message });
  }
};

// Delete token (soft delete)
const deleteToken = async (req, res) => {
  try {
    const token = await Token.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );
    if (!token) {
      return res.status(404).json({ message: "Token not found" });
    }
    res.status(200).json({ message: "Token deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getAllOtherTokenIds = async (req, res) => {
  try {
    console.log("hfduigsdifhdi");

    // Find tokens with non-null, non-empty otherTokenId
    const tokens = await Token.find(
      { otherTokenId: { $exists: true, $ne: null, $ne: "" } },
      "otherTokenId" // only select otherTokenId field
    ).lean();

    // Extract array of otherTokenId strings
    const ids = tokens.map((token) => token.otherTokenId);

    res.status(200).json({ success: true, ids });
  } catch (error) {
    console.error("Error fetching otherTokenIds:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

module.exports = {
  getAllTokens,
  createToken,
  getTokenById,
  updateToken,
  deleteToken,
  getUserTokenById,
  getAllOtherTokenIds,
  createOtherPlatformToken,
};
