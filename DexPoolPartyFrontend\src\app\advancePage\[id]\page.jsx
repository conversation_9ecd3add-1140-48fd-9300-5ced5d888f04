'use client';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
import Image from 'next/image';
import Reply from '@/components/Reply.js';
import ActivityComments from '@/components/Activity.jsx';
import axios from 'axios';

// // Solana Wallet & Jupiter Imports
// import { useWallet } from '@solana/wallet-adapter-react';
// import { useWalletModal } from '@solana/wallet-adapter-react-ui';
// import { useJupiter } from '@jup-ag/react-hook';
// import { Connection, PublicKey, Transaction } from '@solana/web3.js';
// import { TOKEN_LIST_URL } from '@jup-ag/spl-token-list';

const CoinDetailsPage = ({ params }) => {
    const [coinData, setCoinData] = useState(null);
    const { id } = params;
    const [isLoading, setIsLoading] = useState(true);

    // Jupiter Swap State
    const { connected, publicKey, sendTransaction } = useWallet();
    const { setVisible } = useWalletModal();
    const [fromToken, setFromToken] = useState(null); // e.g., SOL
    const [toToken, setToToken] = useState(null);   // e.g., Your Goldticket token
    const [inputAmount, setInputAmount] = useState('0.00');
    const [slippage, setSlippage] = useState(0.5); // Default slippage

    const [tokenList, setTokenList] = useState([]);
    const [loadingTokens, setLoadingTokens] = useState(true);

    // Fetch Bitquery tokens from /api/trending
    const fetchData = useCallback(async () => {
        setIsLoading(true);
        try {
            const response = await fetch('/api/trending');
            const data = await response.json();
            if (data && data.data) {
                setCoinData(data.data);
            } else {
                setCoinData([]);
            }
        } catch (error) {
            console.error('Error fetching Bitquery tokens:', error);
            setCoinData([]);
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    // Use Bitquery token format
    const currentCoin = useMemo(() => {
        if (!coinData) return null;
        return coinData.find(coin => coin.id === id);
    }, [coinData, id]);

    // Jupiter Hook for Swapping
    const {
        routeMap,
        routes,
        exchange,
        loading: jupiterLoading,
        error: jupiterError,
    } = useJupiter({
        amount: inputAmount ? parseFloat(inputAmount) * (10 ** (fromToken?.decimals || 0)) : 0, // Amount in lamports/smallest unit
        inputMint: fromToken ? new PublicKey(fromToken.address) : undefined,
        outputMint: toToken ? new PublicKey(toToken.address) : undefined,
        slippageBps: slippage * 100, // Slippage in basis points (e.g., 0.5% = 50 bps)
        // debounceTime: 500, // Optional: debounce requests
        // endpoint: 'YOUR_RPC_ENDPOINT', // Optional: specify your RPC endpoint if not using default
    });

    const handleSwap = async () => {
        if (!connected) {
            setVisible(true); // Open wallet modal
            return;
        }

        if (!publicKey || !routes || routes.length === 0) {
            alert('Please select tokens and amount, and ensure routes are available.');
            return;
        }

        const route = routes[0]; // Take the best route (first one)

        try {
            const { execute } = exchange({
                route,
                // Optional:
                // userPublicKey: publicKey,
                // wrapAndUnwrapSol: true, // Auto wrap/unwrap SOL if needed
            });

            const swapResult = await execute();

            if (swapResult.error) {
                console.error('Swap failed:', swapResult.error);
                alert(`Swap failed: ${swapResult.error.message}`);
            } else if (swapResult.txid) {
                alert(`Swap successful! Transaction ID: ${swapResult.txid}`);
                console.log('Swap transaction ID:', swapResult.txid);
                // Optionally, reset amount or update balances
                setInputAmount('0.00');
            }
        } catch (err) {
            console.error('Error during swap execution:', err);
            alert(`Error during swap: ${err.message}`);
        }
    };

    if (isLoading || loadingTokens) {
        return (
            <div className="flex items-center justify-center h-screen w-full">
                <div className="loader"></div>
            </div>
        );
    }

    if (!currentCoin) {
        return (
            <div className="flex items-center justify-center h-screen w-full text-white">
                Coin not found.
            </div>
        );
    }

    return (
        <div className="flex flex-col items-center w-full px-[10px] mt-[50px] md:px-[20px] py-[30px] md:py-[90px] min-h-screen text-white">
            <div className="w-full md:pl-[50px] mt-2">
                {/* Token header */}
                <div className="mb-1 mt-2 flex w-full text-sm md:text-large flex-wrap items-center gap-2 md:gap-3 gap-y-[8px] md:gap-y-[14px] font-bold text-white">
                    <div>
                        Golden Ticket (Goldticket) {/* This should ideally come from currentCoin */}
                    </div>
                    <div className='bg-yellow-300 text-black rounded-sm px-1 md:px-2 py-0.5 md:py-1 text-xs md:text-base'>
                        D3psZ1 {/* This should ideally come from currentCoin or token data */}
                    </div>
                    <div className="text-xs md:text-base">
                        about 1 hour ago
                    </div>
                    {currentCoin && (
                        <div className='text-green-200 text-xs md:text-base'>
                            market cap: ${currentCoin.quote.USD.market_cap.toLocaleString()}
                        </div>
                    )}
                    <div className="text-xs md:text-base">
                        replies: 127
                    </div>
                </div>

                {/* Main content grid */}
                <div className="mt-4 md:mt-8 grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
                    {/* Chart section */}
                    <div className="lg:col-span-2">
                        {/* TradingView Chart */}
                        <div className="flex flex-col overflow-y-auto">
                            <div className="mb-3 md:mb-5 w-full"></div>
                            <div style={{ height: '400px', width: '100%' }} className="md:h-[600px]">
                                <div className="flex h-full w-full flex-col" data-sentry-component="UpdatedChart">
                                    <div className="relative flex-grow">
                                        <div className="mb-3 md:mb-5 w-full">
                                            {/* <CmcTable /> */}
                                        </div>
                                        <iframe
                                            id="tradingview_chart"
                                            src={`https://s.tradingview.com/widgetembed/?symbol=BINANCE:${currentCoin.symbol}USDT&interval=D&theme=dark`}
                                            title="Financial Chart"
                                            frameBorder="0"
                                            allowTransparency="true"
                                            scrolling="no"
                                            allowFullScreen
                                            style={{ display: 'block', width: '100%', height: '500px' }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Rest of your existing code remains the same */}
                        {/* Discussion section */}
                        <div className="mt-4 hidden md:block md:mt-8">
                            {/* Sorting controls */}
                            <div className="mb-3 md:mb-4 mt-1 flex items-center flex-wrap gap-2">
                                <button className="flex items-center gap-1 rounded-md bg-[#303947] px-2 py-1 text-xs md:text-sm text-white shadow-sm transition duration-200 hover:bg-gray-600 focus:outline-none">
                                    sort: time (oldest)
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-up">
                                        <path d="m5 12 7-7 7 7"></path>
                                        <path d="M12 19V5"></path>
                                    </svg>
                                </button>
                                <div className="flex cursor-pointer items-center gap-1 rounded-md bg-green-300 px-2 py-1 text-xs md:text-sm text-black shadow-sm transition duration-200 hover:bg-green-400 focus:outline-none">
                                    post a reply
                                </div>
                            </div>

                            {/* Original post */}
                            <div className="grid h-fit gap-1 bg-[#2e303a] p-1 text-xs md:text-sm">
                                <div className="flex gap-1 text-xs">
                                    <span className="flex items-center gap-1">
                                        <span
                                            className="flex gap-1 rounded px-1 hover:underline text-black"
                                            style={{ backgroundColor: 'rgb(200, 220, 139)' }}
                                        >
                                            D3psZ1 (dev)
                                        </span>
                                    </span>
                                    <div className="text-slate-400">28/04/2025, 08:47:00</div>
                                </div>
                                <div className="relative items-start gap-3 overflow-auto text-xs text-slate-300 flex flex-col md:flex-row">
                                    <div className="grid">
                                        <div className="text-sm font-bold">Golden Ticket (Goldticket)</div>
                                        <div className="break-anywhere">
                                            You found your Golden Ticket. Out of a million rugs, a million scams, a million charts you could've clicked, you somehow stumbled into this one. You got lucky when you were trenching late at night, half awake, chasing the next pump. But just like Charlie, finding the ticket was only step one. Now comes the real test. You have to hold through the noise, the fear, the dips that are designed to make you quit. You already beat the odds once. Now you have to out-hold, out-believe, and outlast everyone else if you want your small buy to turn into your golden ticket to generational wealth.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Replies */}
                            <Reply />
                        </div>
                        {/* <ActivityComments /> */}
                    </div>

                    {/* Trading panel */}
                    <div className="lg:col-span-1">
                        <div className="mx-auto grid h-fit w-full gap-3 md:gap-4 md:w-[350px]">
                            {/* Trading card */}
                            <div className="grid gap-3 md:gap-4 rounded-t-lg border border-none bg-[#2e303a] p-3 md:p-4 text-gray-400 transition-all duration-300 ease-in-out md:rounded-lg">
                                <div className="mb-1 flex items-center justify-between">
                                    <div className="flex flex-1 gap-2">
                                        <button className="flex-1 rounded px-2 md:px-3 py-1 md:py-2 text-center text-sm md:text-base font-normal bg-green-400 text-black">buy</button>
                                        <button className="flex-1 rounded px-2 md:px-3 py-1 md:py-2 text-center text-sm md:text-base font-normal bg-gray-800 text-white hover:bg-gray-700 hover:text-gray-500">sell</button>
                                    </div>
                                </div>
                                <div className="flex w-full justify-between">
                                    {/* These buttons need to trigger token selection or set the "toToken" */}
                                    <button
                                        className="rounded px-2 py-1 bg-gray-300 text-black font-bold hover:bg-gray-800 hover:text-gray-300"
                                        onClick={() => {
                                            // You'll need to define how to select your Goldticket token from tokenList
                                            const goldTicket = tokenList.find(token => token.symbol === 'GOLDT' /* Or actual symbol */);
                                            if (goldTicket) {
                                                setToToken(goldTicket);
                                            } else {
                                                alert("Golden Ticket token not found in list!");
                                            }
                                        }}
                                    >
                                        switch to Goldticket
                                    </button>
                                    <button
                                        className="rounded bg-background px-2 py-1 text-black font-bold hover:bg-gray-800 hover:text-gray-300"
                                        type="button"
                                        onClick={() => {
                                            const newSlippage = prompt("Enter max slippage (e.g., 0.5 for 0.5%):", slippage);
                                            if (newSlippage !== null && !isNaN(parseFloat(newSlippage))) {
                                                setSlippage(parseFloat(newSlippage));
                                            } else if (newSlippage !== null) {
                                                alert("Invalid slippage value.");
                                            }
                                        }}
                                    >
                                        set max slippage ({slippage}%)
                                    </button>
                                </div>
                                <div className="flex flex-col">
                                    <div className="relative flex items-center rounded-md bg-[#2e303a]">
                                        <input
                                            id="amount"
                                            value={inputAmount}
                                            onChange={(e) => setInputAmount(e.target.value)}
                                            placeholder="0.00"
                                            type="text"
                                            inputMode="decimal"
                                            className="w-full bg-transparent border-none outline-none p-2 text-lg text-white" // Add basic styling
                                        />
                                        <div className="absolute right-2 ml-2 flex items-center">
                                            {/* Token selection for FROM token */}
                                            {fromToken && (
                                                <>
                                                    {fromToken.logoURI && (
                                                        <Image
                                                            alt={fromToken.symbol}
                                                            loading="lazy"
                                                            width="24"
                                                            height="24"
                                                            decoding="async"
                                                            className="h-6 w-6 md:h-8 md:w-8 rounded-full mr-2"
                                                            src={fromToken.logoURI}
                                                        />
                                                    )}
                                                    <span className="mr-2 text-white text-sm md:text-base">{fromToken.symbol}</span>
                                                </>
                                            )}
                                            {/* You would typically have a button here to open a token selection modal */}
                                            <button
                                                onClick={() => {
                                                    // Implement a token selection modal or component here
                                                    // For now, a simple prompt:
                                                    const newFromSymbol = prompt("Enter FROM token symbol (e.g., SOL, USDC):");
                                                    if (newFromSymbol) {
                                                        const selectedToken = tokenList.find(t => t.symbol.toUpperCase() === newFromSymbol.toUpperCase());
                                                        if (selectedToken) {
                                                            setFromToken(selectedToken);
                                                        } else {
                                                            alert("Token not found in list.");
                                                        }
                                                    }
                                                }}
                                                className="bg-gray-700 px-2 py-1 rounded text-xs"
                                            >
                                                Change
                                            </button>
                                        </div>
                                    </div>
                                    <div className="text-gray-400 text-sm mt-1">
                                        {/* Display estimated output and price impact from Jupiter */}
                                        {routes && routes.length > 0 && fromToken && toToken && inputAmount && (
                                            <div>
                                                <p>
                                                    Output: ~{(routes[0].outAmount / (10 ** toToken.decimals)).toFixed(toToken.decimals || 6)} {toToken.symbol}
                                                </p>
                                                <p>
                                                    Price Impact: {routes[0].priceImpactPct.toFixed(2)}%
                                                </p>
                                                {/* You can display more details from routes[0] */}
                                            </div>
                                        )}
                                        {jupiterLoading && <p>Fetching routes...</p>}
                                        {jupiterError && <p className="text-red-500">Error fetching routes: {jupiterError.message}</p>}
                                    </div>
                                    <div className="mt-2 flex gap-1 rounded-lg bg-[#2e303a] flex-wrap">
                                        <button className="rounded bg-background px-2 py-1 font-bold text-black hover:bg-gray-800 hover:text-gray-300" onClick={() => setInputAmount('0.00')}>reset</button>
                                        <button className="rounded bg-background px-2 py-1 font-bold text-black hover:bg-gray-800 hover:text-gray-300" onClick={() => setInputAmount('0.1')}>0.1 SOL</button>
                                        <button className="rounded bg-background px-2 py-1 font-bold text-black hover:bg-gray-800 hover:text-gray-300" onClick={() => setInputAmount('0.5')}>0.5 SOL</button>
                                        <button className="rounded bg-background px-2 py-1 font-bold text-black hover:bg-gray-800 hover:text-gray-300" onClick={() => setInputAmount('1')}>1 SOL</button>
                                        {/* Max button logic would involve fetching user's SOL balance */}
                                        <button className="rounded bg-background px-2 py-1 font-bold text-black hover:bg-gray-800 hover:text-gray-300">max</button>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-2">
                                    <button
                                        onClick={connected ? handleSwap : () => setVisible(true)}
                                        disabled={jupiterLoading || !fromToken || !toToken || !inputAmount || !routes || routes.length === 0}
                                        className="inline-flex items-center justify-center gap-2 transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 shadow h-8 md:h-10 px-3 md:px-4 w-full rounded-md py-2 md:py-3 text-sm md:text-base font-normal bg-green-700 text-primary-foreground hover:bg-green-500"
                                    >
                                        {connected ? (jupiterLoading ? 'Swapping...' : 'Swap Tokens') : 'Connect Wallet to Trade'}
                                    </button>
                                </div>
                            </div>

                            {/* Token info section - Keep as is, it's mostly display */}
                            <div className="grid w-full gap-3 md:gap-4 rounded-lg border border-none bg-transparent text-gray-400 md:w-[350px]">
                                <div className="h-fit items-start gap-2 flex flex-col md:flex-row">
                                    {/* <Image
                                        alt="Golden Ticket logo"
                                        loading="lazy"
                                        width="256"
                                        height="256"
                                        decoding="async"
                                        className="w-full md:w-32 cursor-pointer object-contain"
                                        src=""
                                        style={{ color: 'transparent' }}
                                    /> */}
                                    <div>
                                        <div className="text-sm font-bold">Golden Ticket (Goldticket)</div>
                                        <div className="break-anywhere break-words text-xs text-gray-400">
                                            You found your Golden Ticket. Out of a million rugs, a million scams, a million charts you could've clicked, you somehow stumbled into this one. You got lucky when you were trenching late at night, half awake, chasing the next pump. But just like Charlie, finding the ticket was only step one. Now comes the real test. You have to hold through the noise, the fear, the dips that are designed to make you quit. You already beat the odds once. Now you have to out-hold, out-believe, and outlast everyone else if you want your small buy to turn into your golden ticket to generational wealth.
                                        </div>
                                    </div>
                                </div>

                                <div className="grid gap-3 md:gap-4">
                                    {/* Bonding curve progress */}
                                    <div>
                                        <div className="flex items-center text-xs md:text-sm text-gray-400">
                                            <span>bonding curve progress: 100%</span>
                                            <button data-state="closed" className="ml-auto">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-info" aria-label="information">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M12 16v-4"></path>
                                                    <path d="M12 8h.01"></path>
                                                </svg>
                                            </button>
                                        </div>
                                        <div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100" className="relative h-3 md:h-4 overflow-hidden rounded-full dark:bg-slate-800 mt-1 md:mt-2 w-full bg-gray-700">
                                            <div data-state="indeterminate" data-max="100" className="h-full w-full flex-1 bg-green-300 transition-all dark:bg-slate-50" style={{ transform: 'translateX(0%)' }}></div>
                                        </div>
                                        <div className="mt-1 md:mt-2">
                                            <div className="flex items-center gap-1 text-xs font-semibold text-gray-400">
                                                bonding curve complete!
                                            </div>
                                        </div>
                                    </div>

                                    {/* King of the hill progress */}
                                    <div className="grid">
                                        <div className="flex items-center text-xs md:text-sm text-gray-400">
                                            <span>king of the hill progress: 100%</span>
                                            <button data-state="closed" className="ml-auto">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-info" aria-label="information">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <path d="M12 16v-4"></path>
                                                    <path d="M12 8h.01"></path>
                                                </svg>
                                            </button>
                                        </div>
                                        <div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100" className="relative h-3 md:h-4 overflow-hidden rounded-full dark:bg-slate-800 mt-1 md:mt-2 w-full bg-gray-700">
                                            <div data-state="indeterminate" data-max="100" className="h-full w-full flex-1 bg-yellow-500 transition-all dark:bg-slate-50" style={{ transform: 'translateX(0%)' }}></div>
                                        </div>
                                        <div className="mt-1 md:mt-2 flex items-center gap-1 text-xs text-yellow-500">
                                            crowned king of the hill on 4/28/2025, 9:37:32 AM
                                        </div>
                                    </div>

                                    {/* Contract address and other buttons */}
                                    <div className="flex flex-col gap-2 pt-1 text-xs font-semibold">
                                        <div className="w-full">
                                            <button className="duration-400 relative flex h-6 w-full cursor-pointer items-center rounded-md bg-gray-700 px-2 py-1 text-gray-400 transition-all hover:bg-gray-600">
                                                <div className="flex flex-grow justify-center">
                                                    <span className="font-semibold text-gray-400">contract address:</span>&nbsp;
                                                    <span className="max-w-[100px] md:max-w-[120px] truncate text-xs font-light">WDa4w...pump</span>
                                                </div>
                                                <div className="absolute right-2">
                                                    <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" className="inline-block" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                                                        <rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect>
                                                        <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path>
                                                    </svg>
                                                </div>
                                            </button>
                                        </div>
                                        <div className="w-full">
                                            <button className="duration-400 relative flex h-6 w-full cursor-pointer items-center rounded-md bg-gray-700 px-2 py-1 text-gray-400 transition-all hover:bg-gray-600">
                                                <div className="flex flex-grow items-center justify-center gap-2">
                                                    <span className="font-normal hover:underline">trade on MEXC</span>
                                                    <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true" height="12" width="12" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                                        <polyline points="15 3 21 3 21 9"></polyline>
                                                        <line x1="10" x2="21" y1="14" y2="3"></line>
                                                    </svg>
                                                </div>
                                            </button>
                                        </div>
                                    </div>

                                    {/* Top holders section */}
                                    <div className="grid gap-2">
                                        <div className="mt-1 md:mt-2 flex items-center justify-between font-bold text-sm">
                                            top holders
                                            <button className="whitespace-nowrap rounded bg-gray-700 px-2 py-1 text-xs font-medium text-gray-400 ring-offset-white transition-colors duration-200 hover:bg-gray-600">
                                                generate bubble map
                                            </button>
                                        </div>
                                        <div className="text-xs md:text-sm">
                                            <div className="grid gap-1">
                                                {[
                                                    '2qf4Xj', 'jhg17399', 'p9x3m2', 'k8l1n5',
                                                    'q7w2e9', 'z1x3c5', 'v2b4n6', 'm9k8j7',
                                                    'gx527grtak', 't5y7u8', 'i9o0p7', 'h3j4k5',
                                                    's6d7f8', 'e4r5t6', 'w3e4r5', 'a1s2d3',
                                                    'f4g5h6', 'j7k8l9', 'l3m4n5', 'q6w7e8'
                                                ].map((name, index) => (
                                                    <div key={index} className="flex justify-between">
                                                        <div className="flex items-center">
                                                            <a className="ml-5 md:ml-6 hover:underline text-xs md:text-sm" href={`/profile/holder${index}`} rel="noopener noreferrer">
                                                                {index + 1}. {name}
                                                            </a>
                                                        </div>
                                                        <div className="text-xs md:text-sm">
                                                            {index === 0 ? '19.31%' :
                                                                index === 1 ? '3.89%' :
                                                                    index === 2 ? '3.73%' :
                                                                        index === 3 ? '3.18%' :
                                                                            index === 4 ? '2.80%' :
                                                                                index === 5 ? '2.54%' :
                                                                                    index === 6 ? '2.52%' :
                                                                                        index === 7 ? '2.33%' :
                                                                                            index === 8 ? '2.26%' :
                                                                                                index === 9 ? '2.18%' :
                                                                                                    index === 10 ? '2.09%' :
                                                                                                        index === 11 ? '1.52%' :
                                                                                                            index === 12 ? '1.51%' :
                                                                                                                index === 13 ? '1.28%' :
                                                                                                                    index === 14 ? '1.26%' :
                                                                                                                        index === 15 ? '1.23%' :
                                                                                                                            index === 16 ? '1.19%' :
                                                                                                                                index === 17 ? '1.12%' :
                                                                                                                                    index === 18 ? '1.08%' : '1.03%'}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CoinDetailsPage;