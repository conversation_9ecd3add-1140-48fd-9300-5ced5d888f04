'use client';
import { createContext,useState, useEffect } from 'react'
export const  CoinMarketContext = createContext();

export const  CoinMarketProvider = ({children}) =>{
    const getCoins = async () => {
        try {
            const response = await fetch('/api/getCoins')
            const data = await response.json()
            console.log("apis response",data);
            return data;
        }
        catch (error) {
            console.error('Error fetching data:', error);
        }
    }

    return (
        <CoinMarketContext.Provider
        value= {{
            getCoins
        }}
        >
            {children}
        </CoinMarketContext.Provider>
    )
}

