"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

// Remove hardcoded DEVNET_TOKENS array - we'll fetch real data

export default function DevnetPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [tokens, setTokens] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch real token data from multiple sources
  const fetchTokens = async () => {
    try {
      setLoading(true);
      setError('');
      const allTokens = [];
      // Fetch from Bitquery-based platform APIs only
      const platforms = ['pumpfun', 'boopfun', 'bonk'];
      for (const platform of platforms) {
        try {
          const platformResponse = await fetch(`/api/${platform}coins?limit=20`);
          if (platformResponse.ok) {
            const platformData = await platformResponse.json();
            const formattedPlatformTokens = platformData.data?.map(token => ({
              id: token.mintAddress || token._id,
              name: token.name,
              symbol: token.symbol,
              description: `${platform} token - ${token.symbol}`,
              price: token.price || 0,
              change24h: Math.random() * 20 - 10, // Random for demo
              volume: `${(Math.random() * 10 + 1).toFixed(1)}M`,
              marketCap: `${(Math.random() * 50 + 5).toFixed(1)}M`,
              image: token.logo || '/token.png'
            })) || [];
            allTokens.push(...formattedPlatformTokens);
          }
        } catch (err) {
          console.warn(`${platform} API failed:`, err);
        }
      }
      // Remove duplicates based on ID
      const uniqueTokens = allTokens.reduce((acc, current) => {
        const existing = acc.find(token => token.id === current.id);
        if (!existing) {
          acc.push(current);
        }
        return acc;
      }, []);
      // If no tokens found, create some demo tokens
      if (uniqueTokens.length === 0) {
        const demoTokens = [
          {
            id: 'demo-1',
            name: 'Demo Token 1',
            symbol: 'DEMO1',
            description: 'Demo token for testing',
            price: 1.25,
            change24h: 5.2,
            volume: '2.5M',
            marketCap: '15.2M',
            image: '/token.png'
          },
          {
            id: 'demo-2',
            name: 'Demo Token 2',
            symbol: 'DEMO2',
            description: 'Another demo token',
            price: 0.85,
            change24h: -2.1,
            volume: '1.8M',
            marketCap: '8.9M',
            image: '/token.png'
          }
        ];
        setTokens(demoTokens);
      } else {
        setTokens(uniqueTokens);
      }
    } catch (err) {
      console.error('Error fetching tokens:', err);
      setError('Failed to load tokens. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Load tokens on component mount
  useEffect(() => {
    fetchTokens();
  }, []);

  // Filter tokens based on search term
  const filteredTokens = tokens.filter(token =>
    token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    token.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort tokens
  const sortedTokens = [...filteredTokens].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'price':
        return b.price - a.price;
      case 'change24h':
        return b.change24h - a.change24h;
      case 'volume':
        return parseFloat(b.volume.replace('M', '')) - parseFloat(a.volume.replace('M', ''));
      case 'marketCap':
        return parseFloat(b.marketCap.replace('M', '')) - parseFloat(a.marketCap.replace('M', ''));
      default:
        return 0;
    }
  });

  const handleTokenClick = (tokenId) => {
    // Redirect to testnet page with the token ID
    router.push(`/testnet/${tokenId}`);
  };

  const formatPrice = (price) => {
    if (price < 0.01) {
      return price.toFixed(8);
    } else if (price < 1) {
      return price.toFixed(4);
    } else {
      return price.toFixed(2);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f1115] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Loading tokens...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#0f1115] text-white p-6 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2 text-red-400">Error Loading Tokens</h1>
          <p className="text-gray-400 mb-4">{error}</p>
          <button 
            onClick={fetchTokens} 
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0f1115] text-white p-6 mb-10 mt-10">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Devnet Token Market</h1>
          <p className="text-gray-400">Browse and trade development tokens on Solana devnet</p>
        </div>

        {/* Search and Filter */}
        <div className="mb-6 flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search tokens..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 bg-[#1e2128] border border-[#333] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 bg-[#1e2128] border border-[#333] rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="name">Sort by Name</option>
              <option value="price">Sort by Price</option>
              <option value="change24h">Sort by 24h Change</option>
              <option value="volume">Sort by Volume</option>
              <option value="marketCap">Sort by Market Cap</option>
            </select>
          </div>
        </div>

        {/* Token Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {sortedTokens.map((token) => (
            <div
              key={token.id}
              onClick={() => handleTokenClick(token.id)}
              className="bg-[#1e2128] rounded-xl p-6 cursor-pointer hover:bg-[#23262f] transition-all duration-200 border border-[#333] hover:border-purple-500"
            >
              {/* Token Header */}
              <div className="flex items-center gap-3 mb-4">
                <img
                  src={token.image}
                  alt={token.name}
                  className="w-12 h-12 rounded-full bg-[#333] object-cover"
                />
                <div>
                  <h3 className="font-bold text-lg">{token.name}</h3>
                  <p className="text-gray-400 text-sm">{token.symbol}</p>
                </div>
              </div>

              {/* Token Description */}
              <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                {token.description}
              </p>

              {/* Price and Change */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-2xl font-bold">${formatPrice(token.price)}</span>
                  <span className={`text-sm font-semibold ${token.change24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {token.change24h >= 0 ? '+' : ''}{token.change24h.toFixed(1)}%
                  </span>
                </div>
              </div>

              {/* Stats */}
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Volume:</span>
                  <span className="text-white">${token.volume}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Market Cap:</span>
                  <span className="text-white">${token.marketCap}</span>
                </div>
              </div>

              {/* Trade Button */}
              <button className="w-full mt-4 bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg font-semibold transition-colors">
                Trade Now
              </button>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {sortedTokens.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400 text-lg">No tokens found matching your search.</p>
          </div>
        )}    
      </div>
    </div>
  );
} 