const rateLimit = require('express-rate-limit');

const emailLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs
  message: { message: 'Too many attempts. Please try again later.' }
});

const otpVerifyLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Limit each IP to 10 verification attempts per hour
  message: { message: 'Too many verification attempts. Please try again later.' }
});

module.exports = { emailLimiter, otpVerifyLimiter };
