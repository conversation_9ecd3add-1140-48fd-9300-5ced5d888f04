const express = require("express");
const router = express.Router();
const {
  getAllTokens,
  createToken,
  getTokenById,
  updateToken,
  deleteToken,
  getUserTokenById,
} = require("../controllers/tokenController");
const {
  getCommentsByItem,
  addComment,
} = require("../controllers/commentController");
const auth = require("../middleware/auth");
const { upload, handleMulterError } = require("../middleware/multer");

/**
 * @swagger
 * tags:
 *   name: Tokens
 *   description: Token management API
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Token:
 *       type: object
 *       required:
 *         - symbol
 *         - name
 *         - price
 *       properties:
 *         _id:
 *           type: string
 *           description: Auto-generated token ID
 *         symbol:
 *           type: string
 *           description: Token symbol (e.g., BTC)
 *         name:
 *           type: string
 *           description: Token name (e.g., Bitcoin)
 *         price:
 *           type: number
 *           description: Current token price
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/tokens:
 *   get:
 *     summary: Get all tokens
 *     tags: [Tokens]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of tokens per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search tokens by name or symbol
 *     responses:
 *       200:
 *         description: List of all tokens
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Token'
 *   post:
 *     summary: Create a new token
 *     tags: [Tokens]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - symbol
 *               - description
 *             properties:
 *               name:
 *                 type: string
 *                 description: Token name
 *               symbol:
 *                 type: string
 *                 description: Token symbol
 *               description:
 *                 type: string
 *                 description: Token description
 *               media:
 *                 type: string
 *                 format: binary
 *                 description: Token media file
 *               links:
 *                 type: string
 *                 description: JSON string of social links
 *     responses:
 *       201:
 *         description: Token created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Token'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid input
 */

/**
 * @swagger
 * /api/tokens/user:
 *   get:
 *     summary: Get tokens for authenticated user
 *     tags: [Tokens]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User's tokens
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Token'
 *       401:
 *         description: Unauthorized
 */

/**
 * @swagger
 * /api/tokens/{id}:
 *   get:
 *     summary: Get token by ID
 *     tags: [Tokens]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Token ID
 *     responses:
 *       200:
 *         description: Token details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Token'
 *       404:
 *         description: Token not found
 *
 *   put:
 *     summary: Update token
 *     tags: [Tokens]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Token ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               symbol:
 *                 type: string
 *               description:
 *                 type: string
 *               media:
 *                 type: string
 *               links:
 *                 type: object
 *                 properties:
 *                   telegram:
 *                     type: string
 *                   website:
 *                     type: string
 *                   twitter:
 *                     type: string
 *     responses:
 *       200:
 *         description: Token updated successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Token not found
 *
 *   delete:
 *     summary: Delete token
 *     tags: [Tokens]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Token ID
 *     responses:
 *       200:
 *         description: Token deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Token not found
 */

/**
 * @swagger
 * /api/tokens/{id}/comments:
 *   get:
 *     summary: Get comments for a token
 *     tags: [Tokens]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Token ID
 *     responses:
 *       200:
 *         description: List of comments
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Comment'
 *       404:
 *         description: Token not found
 *   post:
 *     summary: Add comment to a token
 *     tags: [Tokens]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Token ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 description: Comment content
 *               imageUrl:
 *                 type: string
 *                 description: Optional image URL
 *     responses:
 *       201:
 *         description: Comment added successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Token not found
 */

// Public routes
router.use(auth);
// Public routes
router.get("/", getAllTokens);
router.get("/user", getUserTokenById); // Get tokens for authenticated user
router.get("/:id", getTokenById);

// Protected routes (require authentication)
router.post("/", upload.single("media"), createToken);
router.put("/:id", updateToken);
router.delete("/:id", deleteToken);

// Comment routes
router.get("/:id/comments", getCommentsByItem);
router.post("/:id/comments", auth, addComment);

module.exports = router;
