import React from 'react';
import { XCircle } from 'lucide-react';

const ErrorScreen = ({ error, onRetry }) => {
  return (
    <div className="min-h-screen bg-[#111115] text-white flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-6">
        <XCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <h2 className="text-xl font-bold text-gray-300 mb-2">Error Loading Data</h2>
        <p className="text-gray-400 mb-6">{error}</p>
        <button
          onClick={onRetry}
          className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all duration-200 font-medium"
        >
          Try Again
        </button>
      </div>
    </div>
  );
};

export default ErrorScreen;