const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

const auth = async (req, res, next) => {
  try {
    console.log('=== AUTHENTICATION MIDDLEWARE START ===');
    console.log('Request URL:', req.originalUrl);
    console.log('Request Method:', req.method);
    console.log('Request Headers:', {
      'authorization': req.header('Authorization') ? 'Bearer [TOKEN]' : 'Not provided',
      'content-type': req.header('Content-Type'),
      'user-agent': req.header('User-Agent')
    });

    // Get token from header
    const authHeader = req.header('Authorization');
    console.log("Auth Header:", authHeader);
    
    if (!authHeader) {
      console.log('❌ No Authorization header found');
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    if (!authHeader.startsWith('Bearer ')) {
      console.log('❌ Authorization header does not start with "Bearer "');
      return res.status(401).json({ message: 'Invalid token format. Use "Bearer <token>"' });
    }

    const token = authHeader.split(' ')[1];
    console.log('Token extracted:', token ? `${token.substring(0, 20)}...` : 'No token');

    if (!token) {
      console.log('❌ No token found after "Bearer "');
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    // Verify token
    console.log('🔍 Verifying JWT token...');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log("✅ Token verified successfully");
    console.log("Decoded Token Payload:", {
      id: decoded.id,
      email: decoded.email,
      authProvider: decoded.authProvider,
      isWalletConnected: decoded.isWalletConnected,
      emailVerified: decoded.emailVerified,
      iat: decoded.iat,
      exp: decoded.exp
    });

    // Get full user data
    console.log('🔍 Fetching user from database...');
    const user = await User.findOne({_id: decoded.id});
    if (!user) {
      console.log('❌ User not found in database for ID:', decoded.id);
      return res.status(401).json({ message: 'User not found' });
    }
    console.log('✅ User found:', {
      id: user._id,
      email: user.email,
      username: user.username,
      status: user.status
    });

    // Add user to request
    req.user = req.user || {};
    req.user.id = decoded.id;
    console.log('✅ User ID added to request:', decoded.id);
    console.log('=== AUTHENTICATION MIDDLEWARE END ===');
    
    next();
  } catch (error) {
    console.log("❌ Error in auth middleware:", error.message);
    console.log("Error details:", {
      name: error.name,
      message: error.message,
      stack: error.stack?.split('\n')[0]
    });
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token format' });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token has expired' });
    } else if (error.name === 'NotBeforeError') {
      return res.status(401).json({ message: 'Token not active yet' });
    }
    
    res.status(401).json({ message: 'Token is not valid' });
  }
};

module.exports = auth;