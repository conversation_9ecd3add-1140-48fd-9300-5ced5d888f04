"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { ChevronDown } from "lucide-react";
import LoginModal from "./LoginModel";
import UserDropdown from "./UserDropdown";
import { useAuth } from "@/contexts/AuthContext";
import { showToast } from "@/utils/toast";
import { usePathname, useSearchParams } from "next/navigation";
import BackButton from "./BackButton";

// Helper function to get image URL from backend path
const getImageUrl = (mediaPath) => {
  if (!mediaPath) return "/king.png"; // fallback
  if (mediaPath.startsWith("http") || mediaPath.startsWith("data:"))
    return mediaPath;

  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
  if (!backendUrl) {
    console.warn("NEXT_PUBLIC_BACKEND_URL is not defined.");
    return "/king.png";
  }

  // Normalize Windows-style paths
  const normalizedPath = mediaPath.replace(/\\/g, "/");

  // Remove /api from backend URL if present since uploads are served at root level
  const baseUrl = backendUrl.replace(/\/api$/, "");

  // Combine the base URL and the path properly
  const finalPath = baseUrl.endsWith("/")
    ? normalizedPath.replace(/^\//, "")
    : `/${normalizedPath.replace(/^\//, "")}`;

  return `${baseUrl}${finalPath}`;
};

const Navbar = () => {
  const auth = useAuth();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [loginModalMode, setLoginModalMode] = useState("login"); // 'login' or 'signup'
  const [showDropdown, setShowDropdown] = useState(false);
  const [walletConnected, setWalletConnected] = useState(false);
  const [publicKey, setPublicKey] = useState(null);
  const [isDisconnecting, setIsDisconnecting] = useState(false);

  const isAdvancePage = pathname?.startsWith("/advancePage");

  // Clear any existing toasts when component mounts
  useEffect(() => {
    showToast.dismiss && showToast.dismiss();
  }, []);

  // Handle notification display with delay
  const showNotification = (type, message, description = "") => {
    showToast.dismiss && showToast.dismiss(); // Clear existing toasts
    setTimeout(() => {
      switch (type) {
        case "success":
          showToast.success(message, { description });
          break;
        case "error":
          showToast.error(message, { description });
          break;
        case "info":
          showToast.info(message, { description });
          break;
        case "warning":
          showToast.warning(message, { description });
          break;
        default:
          showToast.info(message, { description });
      }
    }, 100);
  };

  const formatDisplayName = () => {
    if (!auth?.user) {
      // If not logged in via auth but wallet is connected
      if (walletConnected && publicKey) {
        return `${publicKey.slice(0, 4)}...${publicKey.slice(-4)}`;
      }
      return "";
    }
    if (auth.user.username) return auth.user.username;
    if (auth.user.email) return auth.user.email.split("@")[0];
    if (auth.user.walletAddress)
      return `${auth.user.walletAddress.slice(
        0,
        4
      )}...${auth.user.walletAddress.slice(-4)}`;
    return "User";
  };

  useEffect(() => {
    // Check for saved wallet address in localStorage
    const savedWallet = localStorage.getItem("walletAddress");
    if (savedWallet) {
      setPublicKey(savedWallet);
      setWalletConnected(true);
    }
  }, []);

  useEffect(() => {
    // Open login modal if ?isModalOpen=true is in the URL
    if (searchParams.get("isModalOpen") === "true") {
      setShowLoginModal(true);
    }
  }, [searchParams]);

  const handleConnect = (publicKey) => {
    setWalletConnected(true);
    setPublicKey(publicKey);
    localStorage.setItem("walletAddress", publicKey);
    setShowLoginModal(false);
    showNotification(
      "success",
      "Wallet connected",
      "Successfully connected to your wallet"
    );
  };

  const handleDisconnect = async () => {
    if (isDisconnecting) return; // guard clause

    setIsDisconnecting(true);
    try {
      if (window.solana) {
        await window.solana.disconnect();
      }

      setWalletConnected(false);
      setPublicKey(null);
      localStorage.removeItem("walletAddress");
      setShowDropdown(false);

      if (auth?.user) {
        auth.logout();
      }

      showNotification(
        "info",
        "Disconnected",
        "Wallet disconnected successfully"
      );
    } catch (error) {
      console.error("Error disconnecting:", error);
      showNotification("error", "Failed to disconnect wallet");
    } finally {
      setIsDisconnecting(false);
    }
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-[#111115] text-white">
      <div
        className={`flex gap-4 absolute top-6 right-2 items-end ${
          isAdvancePage ? "justify-between" : "justify-end"
        }  ${isAdvancePage ? "w-[96vw] md:w-[95%]" : "w-[96vw] md:w-[76%]"} `}
      >
        {/* BackButton only on Marketplace (advancePage) */}
        {isAdvancePage && <BackButton />}

        <div className="flex items-center gap-2">
          {auth?.user || walletConnected ? (
            <div className="relative">
              <button
                onClick={() => setShowDropdown(!showDropdown)}
                className="flex  items-end justify-end px-2 py-2   md:items-center  md:space-x-2 md:px-4 md:py-2 rounded-lg bg-gray-800 hover:bg-gray-700"
              >
                {auth?.user?.profileImage ? (
                  <Image
                    src={getImageUrl(auth.user.profileImage)}
                    alt="Profile"
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                ) : (
                  <div className="w-6 h-6  rounded-full bg-purple-600  items-center justify-center">
                    {formatDisplayName().charAt(0).toUpperCase()}
                  </div>
                )}
                <span className="hidden md:flex">{formatDisplayName()}</span>
                <ChevronDown
                  className={`w-4 hidden md:flex h-4 transition-transform ${
                    showDropdown ? "rotate-180" : ""
                  }`}
                />
              </button>

              {showDropdown && (
                <UserDropdown
                  username={auth?.user?.username}
                  email={auth?.user?.email}
                  walletAddress={auth?.user?.walletAddress || publicKey}
                  profileImage={auth?.user?.profileImage}
                  onDisconnect={handleDisconnect}
                  onClose={() => setShowDropdown(false)}
                />
              )}
            </div>
          ) : (
            <button
              onClick={() => {
                setShowLoginModal(true);
                setLoginModalMode("login");
              }}
              className="px-4 py-2 rounded-lg bg-purple-800 hover:bg-gray-700 border-3 border-[#111115]"
            >
              Login
            </button>
          )}
        </div>

        {showLoginModal && (
          <LoginModal
            onClose={() => setShowLoginModal(false)}
            onConnect={handleConnect}
            initialMode={"login"}
          />
        )}
      </div>
    </nav>
  );
};

export default Navbar;
