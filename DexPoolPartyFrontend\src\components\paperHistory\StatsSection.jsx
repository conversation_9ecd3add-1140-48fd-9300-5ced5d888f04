import React from 'react';
import { TestTube, Trophy, CheckCircle, Clock, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';

const StatsSection = ({ trades, totalProfit, winRate, isDevnet = false }) => {
  return (
    <div className="mt-12">
      {isDevnet && (
        <div className="mb-6 flex items-center justify-center">
          <div className="flex items-center gap-2 px-4 py-2 bg-green-500/10 backdrop-blur-sm rounded-xl border border-green-500/20">
            <TestTube className="w-4 h-4 text-green-400" />
            <span className="text-sm font-medium text-green-400">Devnet Statistics</span>
          </div>
        </div>
      )}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 rounded-2xl p-8 text-center shadow-lg flex flex-col items-center">
          <Trophy className="w-8 h-8 text-yellow-400 mb-2" />
          <div className="text-3xl font-extrabold text-white mb-1">{trades?.length || 0}</div>
          <div className="text-gray-400 text-base">{isDevnet ? 'Devnet Trades' : 'Total Trades'}</div>
        </div>
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 rounded-2xl p-8 text-center shadow-lg flex flex-col items-center">
          <CheckCircle className="w-8 h-8 text-emerald-400 mb-2" />
          <div className="text-3xl font-extrabold text-emerald-400 mb-1">
            {trades?.filter(t => t?.state === 'completed')?.length || 0}
          </div>
          <div className="text-gray-400 text-base">Completed</div>
        </div>
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 rounded-2xl p-8 text-center shadow-lg flex flex-col items-center">
          <Clock className="w-8 h-8 text-amber-400 mb-2" />
          <div className="text-3xl font-extrabold text-amber-400 mb-1">
            {trades?.filter(t => t?.state === 'pending')?.length || 0}
          </div>
          <div className="text-gray-400 text-base">Pending</div>
        </div>
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 rounded-2xl p-8 text-center shadow-lg flex flex-col items-center">
          {totalProfit >= 0 ? <TrendingUp className="w-8 h-8 text-emerald-400 mb-2" /> : <TrendingDown className="w-8 h-8 text-red-400 mb-2" />}
          <div className={`text-3xl font-extrabold mb-1 ${totalProfit >= 0 ? 'text-emerald-400' : 'text-red-400'}`}>{totalProfit >= 0 ? '+' : ''}${totalProfit.toFixed(0)}</div>
          <div className="text-gray-400 text-base">{isDevnet ? 'Devnet P&L' : 'Total P&L'}</div>
        </div>
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 rounded-2xl p-8 text-center shadow-lg flex flex-col items-center">
          <DollarSign className="w-8 h-8 text-blue-400 mb-2" />
          <div className="text-3xl font-extrabold text-blue-400 mb-1">{winRate.toFixed(0)}%</div>
          <div className="text-gray-400 text-base">{isDevnet ? 'Devnet Win Rate' : 'Win Rate'}</div>
        </div>
      </div>
    </div>
  );
};

export default StatsSection;