export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method Not Allowed' });
  }

  try {
    const { mint, limit = 20 } = req.query;
    if (!mint) {
      return res.status(400).json({ success: false, error: 'Mint address parameter is required' });
    }

    const BITQUERY_API_KEY = process.env.BITQUERY_API_KEY;
    const BITQUERY_API_URL = 'https://streaming.bitquery.io/eap';
    if (!BITQUERY_API_KEY) {
      return res.status(500).json({ success: false, error: 'Server configuration error: API key missing.' });
    }

    const query = `{
      Solana {
        DEXTrades(
          where: {
            Trade: { Buy: { Currency: { MintAddress: { is: \"${mint}\" } } } }
            Transaction: { Result: { Success: true } }
          }
          orderBy: { descending: Block_Time }
          limit: ${limit}
        ) {
          Block { Time }
          Trade {
            Buy {
              Amount
              PriceInUSD
              Currency { Symbol MintAddress }
            }
            Sell {
              Amount
              PriceInUSD
              Currency { Symbol MintAddress }
            }
          }
          Dex { ProtocolName }
          Transaction { Signature Signer }
        }
      }
    }`;

    const response = await fetch(BITQUERY_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': BITQUERY_API_KEY,
      },
      body: JSON.stringify({ query }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return res.status(500).json({ success: false, error: errorData });
    }

    const data = await response.json();
    const trades = data?.data?.Solana?.DEXTrades || [];
    const formatted = trades.map(trade => ({
      time: trade.Block?.Time,
      buyer: trade.Transaction?.Signer,
      signature: trade.Transaction?.Signature,
      priceUSD: trade.Trade?.Buy?.PriceInUSD,
      amount: trade.Trade?.Buy?.Amount,
      protocol: trade.Dex?.ProtocolName,
      buySymbol: trade.Trade?.Buy?.Currency?.Symbol,
      sellSymbol: trade.Trade?.Sell?.Currency?.Symbol,
    }));
    return res.status(200).json({ success: true, data: formatted });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
} 