const nodemailer = require("nodemailer")

// Create transporter (configure with your email service)
const createTransporter = () => {
  return nodemailer.createTransporter({
    service: "gmail", // or your email service
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD, // Use app password for Gmail
    },
  })
}

const sendOTPEmail = async (email, otp) => {
  try {
    const transporter = createTransporter()

    const mailOptions = {
      from: {
        name: "DexPoolParty",
        address: process.env.EMAIL_USER,
      },
      to: email,
      subject: "Your DexPoolParty Verification Code",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 20px;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🎉 DexPoolParty</h1>
            <p style="color: #e0e6ff; margin: 10px 0 0 0; font-size: 16px;">Secure Login Verification</p>
          </div>
          
          <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-top: 0;">Your Verification Code</h2>
            <p style="color: #666; font-size: 16px; line-height: 1.5;">
              Use this code to complete your login to DexPoolParty:
            </p>
            
            <div style="background: #f8f9fa; border: 2px dashed #667eea; border-radius: 8px; padding: 20px; text-align: center; margin: 25px 0;">
              <div style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; font-family: 'Courier New', monospace;">
                ${otp}
              </div>
            </div>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
              <p style="margin: 0; color: #856404; font-size: 14px;">
                ⚠️ <strong>Security Notice:</strong> This code expires in 5 minutes. Never share this code with anyone.
              </p>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-bottom: 0;">
              If you didn't request this code, please ignore this email or contact our support team.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #999; font-size: 12px;">
            <p>© 2024 DexPoolParty. All rights reserved.</p>
            <p>This is an automated message, please do not reply.</p>
          </div>
        </div>
      `,
    }

    const result = await transporter.sendMail(mailOptions)
    console.log("OTP email sent successfully:", result.messageId)
    return result
  } catch (error) {
    console.error("Error sending OTP email:", error)
    throw new Error("Failed to send verification email")
  }
}

module.exports = {
  sendOTPEmail,
}
