'use client';
import { createContext, useState, useEffect, useContext } from 'react';

export const CoinMarketTrending = createContext();

export const CoinMarketProvider = ({ children }) => {
  const [trendingData, setTrendingData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const getCoins = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/trending');
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      const data = await response.json();
      
      return data.data || [];
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.message);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    const data = await getCoins();
    setTrendingData(data);
  };

  useEffect(() => {
    refreshData();
  }, []);

  return (
    <CoinMarketTrending.Provider
      value={{
        trendingData,
        loading,
        error,
        refreshData,
        getCoins
      }}
    >
      {children}
    </CoinMarketTrending.Provider>
  );
};

export const useTrending = () => {
  const context = useContext(CoinMarketTrending);
  if (context === undefined) {
    throw new Error('useTrending must be used within a CoinMarketProvider');
  }
  return context;
};