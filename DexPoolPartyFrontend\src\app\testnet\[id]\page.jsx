"use client";
import { useParams } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { Connection, PublicKey, clusterApiUrl, LAMPORTS_PER_SOL, Transaction, SystemProgram } from '@solana/web3.js';
import { devnetApiService } from '@/lib/devnetApi';
import { handleApiError, handleApiSuccess } from '@/utils/toast';

const TradingViewWidget = dynamic(
  () => import('@/components/TradingViewWidget'),
  { ssr: false }
);

const NETWORKS = [
  { label: 'Devnet', value: 'devnet' },
  { label: 'Testnet', value: 'testnet' },
  { label: 'Mainnet', value: 'mainnet-beta' },
];

const DUMMY_INITIAL_BALANCE = 100;

export default function TestnetTokenPage() {
  const params = useParams();
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);
  const [amount, setAmount] = useState('');
  const [txType, setTxType] = useState('buy');
  const [txLoading, setTxLoading] = useState(false);
  const [txMessage, setTxMessage] = useState('');
  const [phantomAvailable, setPhantomAvailable] = useState(false);
  const [walletAddress, setWalletAddress] = useState('');
  const [solBalance, setSolBalance] = useState(0);
  const [walletError, setWalletError] = useState('');
  const [history, setHistory] = useState([]);
  const [network, setNetwork] = useState('devnet');
  const [airdropLoading, setAirdropLoading] = useState(false);
  const [airdropMsg, setAirdropMsg] = useState('');
  const [error, setError] = useState('');

  // Fetch token data from multiple sources
  const fetchTokenData = async (tokenId) => {
    try {
      // Try backend API first
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:4000';
      const backendResponse = await fetch(`${backendUrl}/tokens/${tokenId}`);
      
      if (backendResponse.ok) {
        const backendToken = await backendResponse.json();
        return {
          ...backendToken,
          media: backendToken.media || '/token.png'
        };
      }

      // Try trending API for real Solana tokens
      const trendingResponse = await fetch('/api/trending');
      if (trendingResponse.ok) {
        const trendingData = await trendingResponse.json();
        const trendingToken = trendingData.data?.find(t => t.id === tokenId);
        if (trendingToken) {
          return {
            _id: trendingToken.id,
            name: trendingToken.name,
            symbol: trendingToken.symbol,
            description: `Real Solana token - ${trendingToken.symbol}`,
            price: trendingToken.quote?.USD?.price || 0,
            media: trendingToken.logo || '/token.png'
          };
        }
      }

      // Try platform APIs
      const platforms = ['pumpfun', 'launchpad', 'boopfun', 'bonk'];
      for (const platform of platforms) {
        try {
          const platformResponse = await fetch(`/api/${platform}coins?limit=50`);
          if (platformResponse.ok) {
            const platformData = await platformResponse.json();
            const platformToken = platformData.data?.find(t => t.mintAddress === tokenId || t._id === tokenId);
            if (platformToken) {
              return {
                _id: platformToken.mintAddress || platformToken._id,
                name: platformToken.name,
                symbol: platformToken.symbol,
                description: `${platform} token - ${platformToken.symbol}`,
                price: platformToken.price || 0,
                media: platformToken.logo || '/token.png'
              };
            }
          }
        } catch (err) {
          console.warn(`Failed to fetch from ${platform}:`, err);
        }
      }

      // Fallback to Jupiter API for Solana token data
      try {
        const jupiterResponse = await fetch(`https://price.jup.ag/v4/price?ids=${tokenId}`);
        if (jupiterResponse.ok) {
          const jupiterData = await jupiterResponse.json();
          const jupiterToken = jupiterData.data?.[tokenId];
          if (jupiterToken) {
            return {
              _id: tokenId,
              name: `Token ${tokenId.slice(0, 8)}...`,
              symbol: 'TOKEN',
              description: 'Solana token from Jupiter API',
              price: jupiterToken.price || 0,
              media: '/token.png'
            };
          }
        }
      } catch (err) {
        console.warn('Failed to fetch from Jupiter API:', err);
      }

      // If all else fails, return a default token
      return {
        _id: tokenId,
        name: 'Unknown Token',
        symbol: 'UNK',
        description: 'Token data not available',
        price: 0,
        media: '/token.png'
      };

    } catch (err) {
      console.error('Error fetching token data:', err);
      throw new Error('Failed to fetch token data');
    }
  };

  // Token fetch logic - now uses real APIs
  useEffect(() => {
    const loadToken = async () => {
      try {
        setLoading(true);
        setError('');
        
        const tokenData = await fetchTokenData(params.id);
        setToken(tokenData);
      } catch (err) {
        const errorMessage = handleApiError(err, 'Failed to load token data');
        setError(errorMessage);
        console.error('Token loading error:', err);
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      loadToken();
    }
  }, [params.id]);

  // Phantom wallet logic
  useEffect(() => {
    if (typeof window !== 'undefined' && window.solana && window.solana.isPhantom) {
      setPhantomAvailable(true);
      window.solana.connect({ onlyIfTrusted: true }).then(({ publicKey }) => {
        if (publicKey) handleWalletConnect(publicKey.toString());
      }).catch(() => {});
    }
  }, []);

  // Fetch real SOL balance
  useEffect(() => {
    if (walletAddress) {
      const connection = new Connection(clusterApiUrl(network));
      connection.getBalance(new PublicKey(walletAddress)).then(lamports => {
        setSolBalance(lamports / LAMPORTS_PER_SOL);
      }).catch(err => {
        console.error('Error fetching balance:', err);
        handleApiError(err, 'Failed to fetch wallet balance');
      });
      // Load dummy history
      const hist = localStorage.getItem(`dummy-history-${walletAddress}`);
      setHistory(hist ? JSON.parse(hist) : []);
    }
  }, [walletAddress, network]);

  useEffect(() => {
    if (walletAddress) {
      localStorage.setItem(`dummy-sol-balance-${walletAddress}`, solBalance);
      localStorage.setItem(`dummy-history-${walletAddress}`, JSON.stringify(history));
    }
  }, [solBalance, walletAddress, history]);

  const handleWalletConnect = (address) => {
    setWalletAddress(address);
    setWalletError('');
  };

  const connectPhantom = async () => {
    setWalletError('');
    if (!phantomAvailable) {
      const errorMsg = 'Phantom wallet not detected.';
      setWalletError(errorMsg);
      handleApiError(errorMsg);
      return;
    }
    try {
      const resp = await window.solana.connect();
      handleWalletConnect(resp.publicKey.toString());
      handleApiSuccess('Phantom wallet connected successfully!');
    } catch (err) {
      const errorMsg = 'Wallet connection failed.';
      setWalletError(errorMsg);
      handleApiError(err, errorMsg);
    }
  };

  // Airdrop SOL (devnet/testnet only)
  const handleAirdrop = async () => {
    setAirdropLoading(true);
    setAirdropMsg('');
    try {
      const connection = new Connection(clusterApiUrl(network));
      const pubkey = new PublicKey(walletAddress);
      const sig = await connection.requestAirdrop(pubkey, LAMPORTS_PER_SOL);
      await connection.confirmTransaction(sig, 'confirmed');
      setAirdropMsg('Airdrop successful!');
      handleApiSuccess('Airdrop successful! 1 SOL added to your wallet.');
      // Refresh balance
      const lamports = await connection.getBalance(pubkey);
      setSolBalance(lamports / LAMPORTS_PER_SOL);
    } catch (e) {
      const errorMsg = 'Airdrop failed: ' + e.message;
      setAirdropMsg(errorMsg);
      handleApiError(e, errorMsg);
    }
    setAirdropLoading(false);
  };

  // Add devnet transaction to database
  const addDevnetTransaction = async (transactionData) => {
    try {
      await devnetApiService.addDevnetTransaction(transactionData);
      handleApiSuccess('Devnet transaction recorded successfully!');
    } catch (err) {
      handleApiError(err, 'Failed to record devnet transaction');
    }
  };

  // Send real transaction (dummy transfer to self for demo)
  const handleTx = async () => {
    setTxLoading(true);
    setTxMessage('');
    setWalletError('');
    if (!walletAddress) {
      const errorMsg = 'Please connect your Phantom wallet.';
      setWalletError(errorMsg);
      handleApiError(errorMsg);
      setTxLoading(false);
      return;
    }
    const totalCost = Number(amount) * Number(token.price);
    if (txType === 'buy' && totalCost > solBalance) {
      const errorMsg = 'Not enough SOL balance.';
      setWalletError(errorMsg);
      handleApiError(errorMsg);
      setTxLoading(false);
      return;
    }
    try {
      const connection = new Connection(clusterApiUrl(network));
      const fromPubkey = new PublicKey(walletAddress);
      // For demo, send a transfer to self (no real SOL lost)
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey,
          toPubkey: fromPubkey,
          lamports: 1 // minimal lamports for demo
        })
      );
      transaction.feePayer = fromPubkey;
      transaction.recentBlockhash = (await connection.getRecentBlockhash()).blockhash;
      // Let Phantom sign and send
      const { signature } = await window.solana.signAndSendTransaction(transaction);
      await connection.confirmTransaction(signature, 'confirmed');
      
      const successMsg = 'Transaction successful! (Real devnet/testnet transaction sent)';
      setTxMessage(successMsg);
      handleApiSuccess(successMsg);
      
      // If this is a buy transaction and we're on devnet, record it
      if (txType === 'buy' && network === 'devnet') {
        const transactionData = {
          token: token._id, // Only the token's _id
          user: 'guest', // No user ID for guest access
          userWallet: walletAddress,
          type: txType,
          amount: Number(amount),
          price: Number(token.price),
          totalValue: totalCost,
          txHash: signature,
          timestamp: new Date(),
          network: network
        };
        
        await addDevnetTransaction(transactionData);
      }
      
      // Refresh balance
      const lamports = await connection.getBalance(fromPubkey);
      setSolBalance(lamports / LAMPORTS_PER_SOL);
    } catch (e) {
      const errorMsg = 'Transaction failed: ' + e.message;
      setTxMessage(errorMsg);
      handleApiError(e, errorMsg);
    }
    setTxLoading(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f1115] text-white flex items-center justify-center">
        <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#0f1115] text-white p-6 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2 text-red-400">Error Loading Token</h1>
          <p className="text-gray-400 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="min-h-screen bg-[#0f1115] text-white p-6 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Token Not Found</h1>
          <p className="text-gray-400">The requested token could not be found in any data source.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0f1115] text-white mt-10 p-6 font-sans w-[90%] mx-auto">
      <div className="max-w-screen-2xl mx-auto px-4">
        <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-4">
            <img src="/phantom.svg" alt="Phantom" className="w-8 h-8" />
            {walletAddress ? (
              <span className="text-green-400 font-mono">{walletAddress.slice(0, 6)}...{walletAddress.slice(-4)}</span>
            ) : (
              <button onClick={connectPhantom} className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-semibold transition">Connect Phantom Wallet</button>
            )}
            <select value={network} onChange={e => setNetwork(e.target.value)} className="ml-4 bg-[#23262f] text-white rounded px-2 py-1">
              {NETWORKS.map(n => <option key={n.value} value={n.value}>{n.label}</option>)}
            </select>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-gray-400">{NETWORKS.find(n => n.value === network)?.label} SOL Balance:</span>
            <span className="text-yellow-400 font-bold">{solBalance.toFixed(4)} SOL</span>
            {walletAddress && (network === 'devnet' || network === 'testnet') && (
              <button onClick={handleAirdrop} disabled={airdropLoading} className="ml-4 bg-green-700 hover:bg-green-800 text-white px-3 py-1 rounded-lg font-semibold transition">
                {airdropLoading ? 'Airdropping...' : 'Airdrop 1 SOL'}
              </button>
            )}
          </div>
        </div>
        {walletError && <p className="text-red-500 mb-4">{walletError}</p>}
        {airdropMsg && <p className="text-green-400 mb-4">{airdropMsg}</p>}
        <div className="w-full grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Left Column - Chart Section */}
          <div className="lg:col-span-8 bg-[#1e2128] rounded-2xl p-4 md:p-6 shadow-lg flex flex-col items-center justify-center min-h-[400px]">
            <TradingViewWidget symbol={`${token.symbol}USD`} />
            <div className="mt-4 text-yellow-400 text-sm font-semibold">Testnet Demo: Real devnet/testnet transactions. No mainnet SOL is used.</div>
          </div>
          {/* Right Column - Buy/Sell Panel */}
          <div className="lg:col-span-4 flex flex-col gap-6">
            <div className="bg-[#23262f] rounded-2xl p-6 shadow-lg flex flex-col gap-4">
              <div className="flex items-center gap-4 mb-2">
                <img src={token.media || '/token.png'} alt={token.name} className="w-10 h-10 rounded-full bg-[#333] object-cover" />
                <div>
                  <h3 className="text-xl font-bold">{token.name} <span className="text-gray-400 font-normal">({token.symbol})</span></h3>
                  <p className="text-gray-400 text-sm">{token.description}</p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-lg font-semibold">Price:</span>
                <span className="text-green-400 text-lg">{token.price}</span>
              </div>
              {/* Buy/Sell Toggle */}
              <div className="flex gap-2 mt-4">
                <button onClick={() => setTxType('buy')} className={`flex-1 py-2 rounded-lg font-semibold transition ${txType === 'buy' ? 'bg-green-600 text-white' : 'bg-gray-700 text-gray-300'}`}>Buy</button>
                <button onClick={() => setTxType('sell')} className={`flex-1 py-2 rounded-lg font-semibold transition ${txType === 'sell' ? 'bg-red-600 text-white' : 'bg-gray-700 text-gray-300'}`}>Sell</button>
              </div>
              {/* Amount Input */}
              <div className="mb-4 mt-2">
                <label className="block text-gray-300 mb-1">Amount</label>
                <input type="number" value={amount} onChange={e => setAmount(e.target.value)} min="0" className="w-full p-2 rounded bg-[#23262f] text-white border border-[#333] focus:outline-none focus:ring-2 focus:ring-purple-500" />
              </div>
              <button onClick={handleTx} disabled={txLoading || !amount} className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg font-semibold transition mb-2">
                {txLoading ? (txType === 'buy' ? 'Buying...' : 'Selling...') : txType === 'buy' ? 'Buy' : 'Sell'}
              </button>
              {txMessage && <p className="mt-3 text-center text-sm text-yellow-400">{txMessage}</p>}
              {walletError && <p className="mt-3 text-center text-sm text-red-500">{walletError}</p>}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 