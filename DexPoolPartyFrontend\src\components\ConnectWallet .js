import { useEffect, useState } from "react";
import { PublicKey, Transaction } from "@solana/web3.js";
import TransferSol from './TransferSol';

const ConnectWallet = () => {
  const [walletAvail, setWalletAvail] = useState(false);
  const [provider, setProvider] = useState(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    if ("solana" in window) {
      const solWindow = window;
      if (solWindow?.solana?.isPhantom) {
        setProvider(solWindow.solana);
        setWalletAvail(true);
        solWindow.solana.connect({ onlyIfTrusted: true });
      }
    }
  }, []);

  useEffect(() => {
    if (!provider) return;

    provider.on("connect", (publicKey) => {
      console.log(`connect event: ${publicKey}`);
      setConnected(true);
    });
    provider.on("disconnect", () => {
      console.log("disconnect event");
      setConnected(false);
    });
  }, [provider]);

  const connectHandler = () => {
    console.log(`connect handler`);
    provider?.connect().catch((err) => {
      console.error("connect ERROR:", err);
    });
  };

  const disconnectHandler = () => {
    console.log("disconnect handler");
    provider?.disconnect().catch((err) => {
      console.error("disconnect ERROR:", err);
    });
  };

  return (
    <div>
      {walletAvail ? (
        <>
          <button disabled={connected} onClick={connectHandler}>
            Connect to Phantom
          </button>
          <button disabled={!connected} onClick={disconnectHandler}>
            Disconnect from Phantom
          </button>
          <hr />
          {connected && provider ? <TransferSol provider={provider} /> : null}
        </>
      ) : (
        <p>
          Oops! Phantom is not available. Get it from{" "}
          <a href="https://phantom.app/">https://phantom.app/</a>.
        </p>
      )}
    </div>
  );
};

export default ConnectWallet;
