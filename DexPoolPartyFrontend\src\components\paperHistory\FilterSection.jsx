import React from 'react';
import { Search, Filter, ChevronDown, X } from 'lucide-react';

const FilterSection = ({
  searchTerm,
  setSearchTerm,
  selectedState,
  setSelectedState,
  selectedProfitFilter,
  setProfitFilter,
  sortBy,
  setSortBy,
  showFilters,
  setShowFilters,
  activeFiltersCount,
  clearAllFilters,
  refreshing,
  handleRefresh,
  totalProfit,
  winRate
}) => {
  return (
    <div className="top-0 z-50 bg-[#111115]/95 backdrop-blur-sm border-b border-gray-800/50">
      <div className="max-w-6xl mx-auto p-4 md:p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
              <Filter className="w-6 h-6" />
            </div>
            <div>
              <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
                Paper Trading History
              </h1>
              <p className="text-gray-400 text-sm md:text-base">Track your simulated trades and performance</p>
            </div>
          </div>
          
          {/* Quick Stats and Refresh */}
          <div className="flex items-center gap-4">            
            <div className="hidden md:flex items-center gap-4">
              <div className="text-center">
                <div className={`text-lg font-bold ${totalProfit >= 0 ? 'text-emerald-400' : 'text-red-400'}`}>
                  {totalProfit >= 0 ? '+' : ''}${totalProfit.toFixed(2)}
                </div>
                <div className="text-xs text-gray-400">Total P&L</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-400">{winRate.toFixed(0)}%</div>
                <div className="text-xs text-gray-400">Win Rate</div>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by token name, symbol, or address..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-white placeholder-gray-400 transition-all duration-200"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-3 rounded-xl border transition-all duration-200 ${
              showFilters || activeFiltersCount > 0
                ? 'bg-blue-500/10 border-blue-500/20 text-blue-400'
                : 'bg-gray-800/60 backdrop-blur-sm border-gray-700/50 text-gray-300 hover:bg-gray-700/60'
            }`}
          >
            <Filter className="w-4 h-4" />
            <span className="hidden sm:inline">Filters</span>
            {activeFiltersCount > 0 && (
              <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                {activeFiltersCount}
              </span>
            )}
            <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-800/30 backdrop-blur-sm rounded-xl border border-gray-700/50">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* State Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Trade State</label>
                <select
                  value={selectedState}
                  onChange={(e) => setSelectedState(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700/60 backdrop-blur-sm border border-gray-600/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white"
                >
                  <option value="all">All States</option>
                  <option value="completed">Completed</option>
                  <option value="pending">Pending</option>
                  <option value="partial">Partial</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              {/* Profit Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Profit/Loss</label>
                <select
                  value={selectedProfitFilter}
                  onChange={(e) => setProfitFilter(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700/60 backdrop-blur-sm border border-gray-600/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white"
                >
                  <option value="all">All Trades</option>
                  <option value="profit">Profitable Only</option>
                  <option value="loss">Loss Only</option>
                </select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Sort By</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700/60 backdrop-blur-sm border border-gray-600/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="highest-value">Highest Value</option>
                  <option value="lowest-value">Lowest Value</option>
                  <option value="most-profitable">Most Profitable</option>
                </select>
              </div>

              {/* Clear Filters */}
              <div className="flex items-end">
                <button
                  onClick={clearAllFilters}
                  className="w-full px-4 py-2 bg-gray-700/60 backdrop-blur-sm hover:bg-gray-600/60 text-gray-300 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
                >
                  <X className="w-4 h-4" />
                  Clear All
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterSection;