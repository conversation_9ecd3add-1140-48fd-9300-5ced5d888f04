const BITQUERY_ENDPOINT = "https://streaming.bitquery.io/eap"
const BITQUERY_API_KEY = process.env.BITQUERY_API_KEY || ""

const PUMP_FUN_PROGRAM = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"

const PUMP_FUN_NEW_CREATIONS_QUERY = `
  query GetPumpFunNewCreations($limit: Int = 100, $since: DateTime, $mint: String) {
    Solana {
      TokenSupplyUpdates(
        where: {
          Instruction: {
            Program: {
              Address: { is: "${PUMP_FUN_PROGRAM}" }
              Method: { is: "create" }
            }
          }
          TokenSupplyUpdate: { Currency: { MintAddress: { is: $mint } } }
          Block: {
            Time: { since: $since }
          }
        }
        limit: { count: $limit }
        orderBy: { descending: Block_Time }
      ) {
        Block {
          Time
        }
        Transaction {
          Signer
          Signature
          Fee
        }
        TokenSupplyUpdate {
          Amount
          Currency {
            Symbol
            ProgramAddress
            PrimarySaleHappened
            Native
            Name
            MintAddress
            MetadataAddress
            Key
            IsMutable
            Fungible
            EditionNonce
            Decimals
            Wrapped
            VerifiedCollection
            Uri
            UpdateAuthority
            TokenStandard
          }
          PostBalance
          PostBalanceInUSD
        }
      }
    }
  }
`

function resolveIpfsUri(uri) {
  if (uri && uri.startsWith("ipfs://")) {
    return `https://ipfs.io/ipfs/${uri.substring(7)}`
  }
  return uri
}

async function transformPumpFunData(data) {
  const items = data?.Solana?.TokenSupplyUpdates || []

  if (!items.length) return []

  const transformedItemsPromises = items.map(async (item, index) => {
    const block = item.Block || {}
    const transaction = item.Transaction || {}
    const supplyUpdate = item.TokenSupplyUpdate || {}
    const currency = supplyUpdate.Currency || {}

    const mintAddress =
      currency.MintAddress || `unknown-${block.Time || index}-${transaction.Signature?.substring(0, 8) || ""}`

    let logo = null
    if (currency.Uri) {
      const metadataUri = resolveIpfsUri(currency.Uri)
      try {
        const metadataResponse = await fetch(metadataUri, {
          cache: "force-cache",
          signal: AbortSignal.timeout(5000),
        })
        if (metadataResponse.ok) {
          const metadata = await metadataResponse.json()
          if (metadata && typeof metadata.image === "string") {
            logo = resolveIpfsUri(metadata.image)
          }
        }
      } catch (metadataError) {
        console.error(`Error fetching metadata for ${mintAddress}:`, metadataError)
      }
    }

    return {
      id: mintAddress,
      symbol: currency.Symbol || `PUMP${index + 1}`,
      name: currency.Name || `Pump Token ${index + 1}`,
      mintAddress: mintAddress,
      creator: transaction.Signer || "Unknown",
      createdAt: block.Time ? new Date(block.Time).toISOString() : new Date().toISOString(),
      createdAtFormatted: block.Time ? new Date(block.Time).toLocaleString() : "Unknown",
      blockNumber: 0,
      blockHash: "",
      transactionSignature: transaction.Signature || "",
      transactionFee: transaction.Fee || 0,
      supply: supplyUpdate.Amount?.toString() || "0",
      postBalance: supplyUpdate.PostBalance?.toString() || "0",
      marketCapUSD: supplyUpdate.PostBalanceInUSD?.toString() || "0",
      decimals: currency.Decimals || 6,
      isMutable: currency.IsMutable || false,
      fungible: currency.Fungible !== false,
      metadataUri: currency.Uri || null,
      logo: logo || `/placeholder.svg?height=48&width=48`,
      updateAuthority: currency.UpdateAuthority || null,
      tokenStandard: currency.TokenStandard || null,
      programAddress: currency.ProgramAddress || PUMP_FUN_PROGRAM,
      primarySaleHappened: currency.PrimarySaleHappened || false,
      isPumpFun: true,
      platform: "pump.fun",
    }
  })

  return Promise.all(transformedItemsPromises)
}

export default async function handler(req, res) {
  try {
    const { searchParams } = new URL(req.url, `http://${req.headers.host}`)
    const limit = Number.parseInt(searchParams.get("limit") || "100")
    const since = searchParams.get("since")
    const mint = searchParams.get("mint") || undefined;

    const defaultSince = new Date(Date.now() - 60 * 60 * 1000).toISOString()
    const sinceParam = since || defaultSince

    const requestBody = {
      query: PUMP_FUN_NEW_CREATIONS_QUERY,
      variables: {
        limit: limit,
        since: sinceParam,
        mint: mint,
      },
    }

    if (req.method === "POST") {
      const body = await req.json()
      const { customVariables = {} } = body
      requestBody.variables = {
        ...requestBody.variables,
        ...customVariables,
      }
    }

    const response = await fetch(BITQUERY_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BITQUERY_API_KEY}`,
        "X-API-KEY": BITQUERY_API_KEY,
      },
      body: JSON.stringify(requestBody),
    })

    // Handle non-JSON responses (like HTML error pages)
    const contentType = response.headers.get("content-type")
    if (!contentType || !contentType.includes("application/json")) {
      const errorText = await response.text()
      console.error(`BitQuery API returned non-JSON response: ${response.status} - ${errorText.substring(0, 200)}`)

      return res.status(response.status).json({
        success: false,
        error: `BitQuery API error: ${response.status} ${response.statusText}`,
        details: "API quota exceeded or service unavailable",
        timestamp: new Date().toISOString(),
      })
    }

    if (!response.ok) {
      const errorData = await response.json()
      console.error(`BitQuery API error: ${response.status} ${response.statusText}`, errorData)

      return res.status(response.status).json({
        success: false,
        error: `BitQuery API error: ${response.statusText}`,
        details: errorData,
        timestamp: new Date().toISOString(),
      })
    }

    const result = await response.json()

    if (result.errors) {
      console.error("BitQuery errors:", result.errors)
      return res.status(400).json({
        success: false,
        error: "BitQuery returned GraphQL errors",
        details: result.errors,
        timestamp: new Date().toISOString(),
      })
    }

    const transformedData = await transformPumpFunData(result.data)

    return res.status(200).json({
      success: true,
      data: transformedData,
      count: transformedData.length,
      queryType: "new_creations",
      since: sinceParam,
      timestamp: new Date().toISOString(),
      message:
        transformedData.length > 0
          ? `Found ${transformedData.length} newly created pump.fun tokens.`
          : "No newly created tokens found in the specified time range.",
    })
  } catch (error) {
    console.error("API Error:", error)

    // Return empty data instead of mock data
    return res.status(500).json({
      success: false,
      error: "Internal Server Error",
      details: error.message,
      data: [],
      count: 0,
      timestamp: new Date().toISOString(),
    })
  }
}
