// pages/api/trending.js

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method Not Allowed' });
    }

    const BITQUERY_API_KEY = process.env.BITQUERY_API_KEY;
    const BITQUERY_API_URL = 'https://streaming.bitquery.io/eap';

    if (!BITQUERY_API_KEY) {
        console.error('BITQUERY_API_KEY is not set in environment variables.');
        return res.status(500).json({ message: 'Server configuration error: API key missing.' });
    }

    // Calculate dynamic time for the last 24 hours
    const till = new Date(); // Current time
    const since = new Date(till.getTime() - (24 * 60 * 60 * 1000)); // 24 hours ago

    // Bitquery GraphQL query for Solana DEXTrades
    // This query gets the top 10 DEX trades for tokens on the Solana network,
    // ordered by buy price, and includes trade volume.
    const query = `
        {
            Solana {
                DEXTrades(
                    limitBy: {by: Trade_Buy_Currency_MintAddress, count: 1}
                    limit: {count: 10}
                    orderBy: {descending: Trade_Buy_Price}
                    where: {
                        Trade: {
                            Dex: {ProtocolName: {is: "pump"}},
                            Buy: {Currency: {MintAddress: {notIn: ["11111111111111111111111111111111"]}}},
                            PriceAsymmetry: {le: 0.1},
                            Sell: {AmountInUSD: {gt: "10"}}
                        },
                        Transaction: {Result: {Success: true}},
                        Block: {Time: {since: "${since.toISOString()}"}} # Dynamic date for last 24 hours
                    }
                ) {
                    Trade {
                        Buy {
                            Price(maximum: Block_Time)
                            PriceInUSD(maximum: Block_Time)
                            Currency {
                                Name
                                Symbol
                                MintAddress
                                Decimals
                                Fungible
                                Uri
                            }
                        }
                        Sell {
                            AmountInUSD # Include this to get volume
                        }
                    }
                }
            }
        }
    `;

    try {
        const bitqueryResponse = await fetch(BITQUERY_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-KEY': BITQUERY_API_KEY,
            },
            body: JSON.stringify({ query }),
        });
        console.log('Bitquery API response status:', bitqueryResponse.status);
        
        const contentType = bitqueryResponse.headers.get('content-type');
        if (!bitqueryResponse.ok) {
            let errorData;
            if (contentType && contentType.includes('application/json')) {
                errorData = await bitqueryResponse.json();
            } else {
                errorData = await bitqueryResponse.text();
            }
            console.error('Bitquery API error response:', errorData);
            throw new Error(`Bitquery API request failed with status ${bitqueryResponse.status}: ${typeof errorData === 'string' ? errorData : JSON.stringify(errorData)}`);
        }

        let data;
        if (contentType && contentType.includes('application/json')) {
            data = await bitqueryResponse.json();
        } else {
            const text = await bitqueryResponse.text();
            throw new Error(`Bitquery API returned non-JSON response: ${text}`);
        }

        // Check for the correct data path: data.data.Solana.DEXTrades
        if (!data.data || !data.data.Solana || !data.data.Solana.DEXTrades) {
            console.warn('Bitquery response did not contain expected DEXTrades data:', JSON.stringify(data, null, 2));
            return res.status(200).json({ success: true, data: [] }); // Return empty if no data
        }

        // Map the data to the expected format for the frontend
        const processedData = data.data.Solana.DEXTrades.map(item => {
            const buyCurrency = item.Trade.Buy.Currency;
            const priceInUSD = item.Trade.Buy.PriceInUSD;
            const volumeInUSD = item.Trade.Sell ? item.Trade.Sell.AmountInUSD : 0; // Use Sell.AmountInUSD for volume

            return {
                id: buyCurrency.MintAddress, // Using MintAddress as ID
                name: buyCurrency.Name,
                symbol: buyCurrency.Symbol,
                logo: buyCurrency.Uri, // Use URI if available, fallback to placeholder
                quote: {
                    USD: {
                        price: priceInUSD,
                        market_cap: 0, // Placeholder: Market cap is not directly available from this query
                        volume_24h: volumeInUSD,
                        percent_change_24h: 0, // Placeholder: 24h percentage change is not directly available
                    },
                },
            };
        });

        res.status(200).json({ success: true, data: processedData });

    } catch (error) {
        console.error('Error in /api/trending:', error.message);
        res.status(500).json({ success: false, message: 'Failed to fetch trending coins', error: error.message });
    }
}
