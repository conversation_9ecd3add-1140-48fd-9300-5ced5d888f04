'use client';

import { useState } from 'react';
import { Filter } from 'lucide-react';

const quickReplies = [
  { emoji: '🎉', text: 'Looks very good!' },
  { emoji: '👏', text: 'Need Any help?' },
  { emoji: '⛔', text: 'This is blocked...' },
  { emoji: '🔍', text: 'Can you clarify...?' },
  { emoji: '✅', text: 'This is complete.' }
];

export default function ActivityComments() {
  const [comment, setComment] = useState('');

  return (
    <div className="w-full max-w-3xl p-4 mx-auto">
      {/* Tabs and Filter */}
      <div className="mb-4 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
        {/* Tabs */}
        <div className="flex overflow-x-auto sm:overflow-visible gap-2 sm:gap-4 border rounded-md p-1">
          {['All', 'Comments', 'History', 'Work log'].map((tab, idx) => (
            <button
              key={idx}
              className={`flex-shrink-0 py-2 px-4 text-sm font-medium text-gray-600 whitespace-nowrap ${
                tab === 'Comments' ? 'border-b-2 border-blue-500' : ''
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Filter icon */}
        <div className="flex justify-end sm:justify-center items-center">
          <Filter className="w-5 h-5 text-gray-600 hover:text-black cursor-pointer" />
        </div>
      </div>

      {/* Comment section */}
      <div className="flex flex-col sm:flex-row gap-3">
        {/* Avatar */}
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-orange-400 flex items-center justify-center font-bold">
          AK
        </div>

        {/* Comment Box */}
        <div className="flex-1 border rounded-md p-3 shadow-sm w-full">
          <textarea
            className="w-full border-none focus:ring-0 resize-none placeholder-gray-500 text-sm"
            rows={2}
            placeholder="Add a comment..."
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />

          {/* Quick replies */}
          <div className="flex flex-wrap gap-2 mt-2">
            {quickReplies.map((item, idx) => (
              <button
                key={idx}
                type="button"
                className="flex items-center bg-gray-100 text-black text-sm px-3 py-1 rounded-full hover:bg-gray-200"
                onClick={() => setComment(`${item.emoji} ${item.text}`)}
              >
                <span className="mr-1">{item.emoji}</span>
                {item.text}
              </button>
            ))}
          </div>

          {/* Pro tip */}
          <p className="text-xs text-gray-500 mt-2">
            <strong>Pro tip:</strong> press{' '}
            <kbd className="bg-gray-200 px-1 rounded text-sm">M</kbd> to comment
          </p>
        </div>
      </div>
    </div>
  );
}
