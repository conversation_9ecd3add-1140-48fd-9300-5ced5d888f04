{"name": "dexnode", "version": "1.0.0", "description": "DexNode Trading Platform", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js"}, "dependencies": {"cors": "^2.8.5", "crypto-js": "^4.1.1", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "firebase-admin": "^13.4.0", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.1", "path": "^0.12.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.2"}, "devDependencies": {"nodemon": "^2.0.22"}}