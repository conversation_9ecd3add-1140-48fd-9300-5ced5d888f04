{"name": "dexpoolparty", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@jup-ag/api": "^6.0.42", "@lottiefiles/dotlottie-react": "^0.14.2", "@phantom/wallet-sdk": "^0.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@solana/wallet-adapter-react": "^0.15.38", "@solana/wallet-adapter-react-ui": "^0.9.38", "@solana/wallet-adapter-wallets": "^0.19.36", "@solana/web3.js": "^1.98.2", "axios": "^1.9.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "firebase": "^11.7.1", "lucide-react": "^0.503.0", "next": "15.3.1", "node-fetch": "^3.3.2", "nprogress": "^0.2.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwind-scrollbar-hide": "^2.0.0", "tw-animate-css": "^1.2.8", "ws": "^8.18.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4"}}