export const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || process.env.NEXT_PUBLIC_LOCALHOST_BACKEND_URL;

// Enhanced logging utility for API calls
const logApiCall = (action, details = {}) => {
  console.log(`🌐 [API] ${action}`, {
    timestamp: new Date().toISOString(),
    baseUrl: API_BASE_URL,
    ...details
  });
};

// Helper to get auth token with logging
const getAuthToken = () => {
  const token = localStorage.getItem('authToken');
  logApiCall('GET_AUTH_TOKEN', {
    hasToken: !!token,
    tokenPreview: token ? `${token.substring(0, 20)}...` : null
  });
  return token;
};

// Enhanced fetch wrapper with logging
const apiFetch = async (url, options = {}) => {
  const token = getAuthToken();
  const hasAuthHeader = options.headers && options.headers.Authorization;
  
  logApiCall('API_REQUEST', {
    url,
    method: options.method || 'GET',
    hasAuthHeader,
    hasToken: !!token,
    contentType: options.headers?.['Content-Type']
  });

  // Add auth header if token exists and not already present
  if (token && !hasAuthHeader) {
    options.headers = {
      ...options.headers,
      'Authorization': `Bearer ${token}`
    };
    logApiCall('AUTH_HEADER_ADDED', {
      tokenPreview: `${token.substring(0, 20)}...`
    });
  }

  try {
    const response = await fetch(url, options);
    
    logApiCall('API_RESPONSE', {
      status: response.status,
      statusText: response.statusText,
      url: response.url,
      hasAuthHeader: !!response.headers.get('authorization')
    });

    if (!response.ok) {
      logApiCall('API_ERROR', {
        status: response.status,
        statusText: response.statusText,
        url: response.url
      });
    }

    return response;
  } catch (error) {
    logApiCall('API_NETWORK_ERROR', {
      error: error.message,
      url
    });
    throw error;
  }
};

export const userService = {
    loginWithWallet: async (walletAddress) => {
        logApiCall('WALLET_LOGIN_ATTEMPT', { walletAddress });
        
        const response = await apiFetch(`${API_BASE_URL}/users/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ walletAddress })
        });
        
        const data = await response.json();
        logApiCall('WALLET_LOGIN_RESPONSE', {
            success: response.ok,
            hasToken: !!data.token,
            tokenPreview: data.token ? `${data.token.substring(0, 20)}...` : null
        });
        
        return data;
    },

    createUser: async (userData) => {
        logApiCall('CREATE_USER_ATTEMPT', {
            userData: { email: userData.email, username: userData.username }
        });
        
        const response = await apiFetch(`${API_BASE_URL}/users`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(userData)
        });
        
        const data = await response.json();
        logApiCall('CREATE_USER_RESPONSE', {
            success: response.ok,
            userId: data.id || data._id
        });
        
        return data;
    }
};

export const tokenService = {
    createToken: async (tokenData) => {
        logApiCall('CREATE_TOKEN_ATTEMPT', {
            tokenName: tokenData.get('name'),
            tokenSymbol: tokenData.get('symbol')
        });

        const response = await apiFetch(`${API_BASE_URL}/tokens`, {
            method: 'POST',
            body: tokenData
        });
        
        const data = await response.json();
        logApiCall('CREATE_TOKEN_RESPONSE', {
            success: response.ok,
            tokenId: data.id || data._id
        });
        
        return data;
    },

    getAllTokens: async () => {
        logApiCall('GET_ALL_TOKENS_ATTEMPT');
        
        const response = await apiFetch(`${API_BASE_URL}/tokens`);
        const data = await response.json();
        
        logApiCall('GET_ALL_TOKENS_RESPONSE', {
            success: response.ok,
            tokenCount: data.length || 0
        });
        
        return data;
    },

    getUserTokens: async (userId) => {
        logApiCall('GET_USER_TOKENS_ATTEMPT', { userId });
        
        const response = await apiFetch(`${API_BASE_URL}/tokens/user`);
        const data = await response.json();
        
        logApiCall('GET_USER_TOKENS_RESPONSE', {
            success: response.ok,
            tokenCount: data.length || 0,
            userId
        });
        
        return data;
    }
};

// Enhanced trade service with logging
export const tradeService = {
    createTrade: async (tradeData) => {
        logApiCall('CREATE_TRADE_ATTEMPT', {
            tokenId: tradeData.tokenId,
            type: tradeData.type,
            amount: tradeData.amount
        });
        
        const response = await apiFetch(`${API_BASE_URL}/trades`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(tradeData)
        });
        
        const data = await response.json();
        logApiCall('CREATE_TRADE_RESPONSE', {
            success: response.ok,
            tradeId: data.id || data._id
        });
        
        return data;
    },

    getUserTrades: async () => {
        logApiCall('GET_USER_TRADES_ATTEMPT');
        
        const response = await apiFetch(`${API_BASE_URL}/trades/user`);
        const data = await response.json();
        
        logApiCall('GET_USER_TRADES_RESPONSE', {
            success: response.ok,
            tradeCount: data.length || 0
        });
        
        return data;
    }
};

// Enhanced comment service with logging
export const commentService = {
    createComment: async (commentData) => {
        logApiCall('CREATE_COMMENT_ATTEMPT', {
            tokenId: commentData.tokenId,
            contentLength: commentData.content?.length
        });
        
        const response = await apiFetch(`${API_BASE_URL}/comments`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(commentData)
        });
        
        const data = await response.json();
        logApiCall('CREATE_COMMENT_RESPONSE', {
            success: response.ok,
            commentId: data.id || data._id
        });
        
        return data;
    },

    getTokenComments: async (tokenId) => {
        logApiCall('GET_TOKEN_COMMENTS_ATTEMPT', { tokenId });
        
        const response = await apiFetch(`${API_BASE_URL}/comments/token/${tokenId}`);
        const data = await response.json();
        
        logApiCall('GET_TOKEN_COMMENTS_RESPONSE', {
            success: response.ok,
            commentCount: data.length || 0,
            tokenId
        });
        
        return data;
    }
};

// Export the enhanced fetch wrapper for custom API calls
export { apiFetch, getAuthToken, logApiCall };
