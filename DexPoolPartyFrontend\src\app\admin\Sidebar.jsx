import React from 'react';
import { useRouter, usePathname } from 'next/navigation';

const navLinks = [
  { label: 'Dashboard', path: '/admin/dashboard', icon: '📊' },
  { label: 'Test Page', path: '/admin/test', icon: '🧪' },
  { label: 'Manage Users', path: '/admin/users', icon: '👥' },
  { label: 'Settings', path: '/admin/settings', icon: '⚙️' },
];

const Sidebar = ({ onLogout }) => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="w-[220px] bg-[#1a1a1f] h-screen fixed left-0 top-0 shadow-2xl flex flex-col z-50 border-r border-[#2a2a2f]">
      <div className="font-bold text-2xl py-8 px-0 text-center tracking-wide text-purple-400">
        Admin Panel
      </div>
      <nav className="flex-1">
        {navLinks.map(link => (
          <div
            key={link.path}
            onClick={() => router.push(link.path)}
            className={`flex items-center gap-3 px-8 py-3 cursor-pointer transition-all duration-200 ${
              pathname === link.path 
                ? 'bg-purple-500/20 text-purple-400 font-semibold border-l-4 border-purple-500' 
                : 'text-gray-300 hover:bg-[#2a2a2f] hover:text-white border-l-4 border-transparent'
            }`}
          >
            <span className="text-xl">{link.icon}</span>
            <span>{link.label}</span>
          </div>
        ))}
      </nav>
      <div className="p-8">
        <button
          onClick={onLogout}
          className="w-full py-3 px-4 rounded-lg bg-gradient-to-r from-red-600 to-red-700 text-white font-semibold text-base border-none transition-all duration-200 hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-[#1a1a1f]"
        >
          Logout
        </button>
      </div>
    </div>
  );
};

export default Sidebar; 