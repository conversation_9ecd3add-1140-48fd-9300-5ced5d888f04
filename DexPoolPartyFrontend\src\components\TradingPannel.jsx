"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DollarSign, Wallet, Settings } from "lucide-react"


export default function TradingPanel({ coin, currentPrice, isConnected }) {
  const [activeTab, setActiveTab] = useState<"buy" | "sell">("buy")
  const [amount, setAmount] = useState("")
  const [slippage, setSlippage] = useState("0.5")
  const [isWalletConnected, setIsWalletConnected] = useState(false)

  const formatPrice = (price) => {
    if (price >= 1) return price.toFixed(2)
    if (price >= 0.01) return price.toFixed(4)
    return price.toFixed(8)
  }

  const calculateTotal = () => {
    const amountNum = Number.parseFloat(amount) || 0
    return (amountNum * currentPrice).toFixed(2)
  }

  const handleConnectWallet = () => {
    // Simulate wallet connection
    setIsWalletConnected(!isWalletConnected)
  }

  const handleTrade = () => {
    if (!isWalletConnected) {
      handleConnectWallet()
      return
    }

    // Simulate trade execution
    alert(`${activeTab === "buy" ? "Buying" : "Selling"} ${amount} ${coin.symbol} at $${formatPrice(currentPrice)}`)
  }

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="w-5 h-5" />
          Trade {coin.symbol}
          {isConnected && (
            <Badge variant="outline" className="text-green-400 border-green-400 text-xs">
              Live
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Wallet Connection Status */}
        <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
          <div className="flex items-center gap-2">
            <Wallet className="w-4 h-4" />
            <span className="text-sm">{isWalletConnected ? "Wallet Connected" : "Wallet Disconnected"}</span>
          </div>
          <div className={`w-2 h-2 rounded-full ${isWalletConnected ? "bg-green-400" : "bg-red-400"}`} />
        </div>

        {/* Buy/Sell Tabs */}
        <div className="flex gap-2">
          <Button
            variant={activeTab === "buy" ? "default" : "outline"}
            className={`flex-1 ${activeTab === "buy" ? "bg-green-600 hover:bg-green-700" : "border-gray-600"}`}
            onClick={() => setActiveTab("buy")}
          >
            Buy
          </Button>
          <Button
            variant={activeTab === "sell" ? "default" : "outline"}
            className={`flex-1 ${activeTab === "sell" ? "bg-red-600 hover:bg-red-700" : "border-gray-600"}`}
            onClick={() => setActiveTab("sell")}
          >
            Sell
          </Button>
        </div>

        {/* Current Price Display */}
        <div className="p-3 bg-gray-700 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-400">Current Price</span>
            <span className="font-semibold text-green-400">${formatPrice(currentPrice)}</span>
          </div>
        </div>

        {/* Amount Input */}
        <div className="space-y-2">
          <label className="text-sm text-gray-400">Amount</label>
          <div className="relative">
            <Input
              type="number"
              placeholder="0.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white pr-16"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-400">
              {coin.symbol}
            </div>
          </div>
        </div>

        {/* Quick Amount Buttons */}
        <div className="grid grid-cols-4 gap-2">
          {["0.1", "0.5", "1", "Max"].map((preset) => (
            <Button
              key={preset}
              variant="outline"
              size="sm"
              onClick={() => setAmount(preset === "Max" ? "100" : preset)}
              className="border-gray-600 hover:bg-gray-700 text-xs"
            >
              {preset}
            </Button>
          ))}
        </div>

        {/* Total Value */}
        {amount && (
          <div className="p-3 bg-gray-700 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">Total Value</span>
              <span className="font-semibold">${calculateTotal()} USD</span>
            </div>
          </div>
        )}

        {/* Slippage Settings */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm text-gray-400">Slippage Tolerance</label>
            <Settings className="w-4 h-4 text-gray-400" />
          </div>
          <div className="flex gap-2">
            {["0.1", "0.5", "1.0"].map((value) => (
              <Button
                key={value}
                variant={slippage === value ? "default" : "outline"}
                size="sm"
                onClick={() => setSlippage(value)}
                className={`flex-1 text-xs ${
                  slippage === value ? "bg-blue-600 hover:bg-blue-700" : "border-gray-600 hover:bg-gray-700"
                }`}
              >
                {value}%
              </Button>
            ))}
          </div>
        </div>

        {/* Trade Button */}
        <Button
          className={`w-full ${
            activeTab === "buy" ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700"
          }`}
          onClick={handleTrade}
          disabled={!amount || Number.parseFloat(amount) <= 0}
        >
          {!isWalletConnected ? "Connect Wallet" : `${activeTab === "buy" ? "Buy" : "Sell"} ${coin.symbol}`}
        </Button>

        {/* Trading Info */}
        <div className="text-xs text-gray-400 space-y-1">
          <div className="flex justify-between">
            <span>Network Fee:</span>
            <span>~0.0001 SOL</span>
          </div>
          <div className="flex justify-between">
            <span>Platform Fee:</span>
            <span>0.25%</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
