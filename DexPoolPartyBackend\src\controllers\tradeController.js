const Trade = require('../models/trade.model');

exports.getAllTrades = async (req, res) => {
  const trades = await Trade.find();
  res.json(trades);
};

exports.getTradesByToken = async (req, res) => {
  const trades = await Trade.find({ token: req.params.tokenId });
  res.json(trades);
};

exports.getTradesByUser = async (req, res) => {
  const trades = await Trade.find({ user: req.params.userId });
  res.json(trades);
};

exports.createTrade = async (req, res) => {
  const trade = new Trade(req.body);
  await trade.save();
  res.status(201).json(trade);
};

exports.updateTradeStatus = async (req, res) => {
  const trade = await Trade.findByIdAndUpdate(req.params.id, req.body, { new: true });
  if (!trade) return res.status(404).json({ message: 'Trade not found' });
  res.json(trade);
};

exports.filterTrades = async (req, res) => {
  const { minPrice, maxPrice, startDate, endDate } = req.query;
  const filter = {};

  if (minPrice && maxPrice) {
    filter.price = { $gte: minPrice, $lte: maxPrice };
  }

  if (startDate && endDate) {
    filter.timestamp = { $gte: new Date(startDate), $lte: new Date(endDate) };
  }

  const trades = await Trade.find(filter);
  res.json(trades);
};
