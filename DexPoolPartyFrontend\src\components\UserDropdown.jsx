// components/UserDropdown.jsx
'use client';
import React, { useState } from 'react';
import { User, Wallet, LogOut, Edit, Mail, Copy } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { toast } from 'sonner';

const UserDropdown = ({
  username,
  email,
  walletAddress,
  onDisconnect,
  onClose,
  profileImage
}) => {
  const [showWalletPopup, setShowWalletPopup] = useState(false);

  const handleWalletClick = (e) => {
    e.preventDefault();
    setShowWalletPopup(true);
  };

  const handleCloseWallet = () => {
    setShowWalletPopup(false);
  };

  const handleDisconnectWallet = async () => {
    if (window.solana && walletAddress) {
      try {
        await window.solana.disconnect();
      } catch (error) {
        console.error('Error disconnecting from Phantom:', error);
      }
    }
    if (onDisconnect) onDisconnect();
    handleCloseWallet();
    if (onClose) onClose();
  };

  const copyToClipboard = async (text) => {
    if (!text) return;

    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${text.includes('@') ? 'Email' : 'Wallet address'} copied to clipboard!`);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const formatDisplayName = () => {
    if (username) return username;
    if (walletAddress) return `${walletAddress.slice(0, 4)}...${walletAddress.slice(-4)}`;
    if (email) return email.split('@')[0];
    return 'User';
  };

  const formatWalletAddress = () => {
    if (!walletAddress) return 'No wallet connected';
    return `${walletAddress.slice(0, 4)}...${walletAddress.slice(-4)}`;
  };

  return (
    <>
      <div
        className="absolute right-0 mt-2 w-52 rounded-lg shadow-lg z-50"
        style={{
          background: '#18181b',
          border: '1px solid #23272f',
          color: '#e5e7eb',
        }}
      >
        <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="user-menu">
          <div
            className="px-4 py-2 text-sm border-b"
            style={{
              color: '#a1a1aa',
              borderColor: '#23272f',
              background: 'transparent',
            }}
          >
            Connected as<br />
            <span style={{ color: '#e5e7eb' }} className="font-medium whitespace-nowrap">{formatDisplayName()}</span>
          </div>

          <Link
            href="/profile"
            className="flex items-center px-4 py-2 text-sm hover:bg-[#23272f]"
            style={{ color: '#e5e7eb' }}
          >
            <User className="mr-3 h-4 w-4" color="#a1a1aa" />
            Profile
          </Link>

          {walletAddress && (
            <button
              onClick={handleWalletClick}
              className="w-full flex items-center px-4 py-2 text-sm hover:bg-[#23272f]"
              style={{ color: '#e5e7eb' }}
            >
              <Wallet className="mr-3 h-4 w-4" color="#a1a1aa" />
              My Wallet
            </button>
          )}

          <button
            onClick={handleDisconnectWallet}
            className="w-full flex items-center px-4 py-2 text-sm hover:bg-[#23272f]"
            style={{ color: '#f87171' }}
          >
            <LogOut className="mr-3 h-4 w-4" color="#f87171" />
            Logout
          </button>
        </div>
      </div>

      {showWalletPopup && (
        <div className="fixed inset-0 z-50 flex items-center justify-center" style={{ background: 'rgba(24,24,27,0.95)' }}>
          <div className="fixed left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2 rounded-lg shadow-lg w-[360px] text-primary"
            style={{ background: '#18181b', border: '1px solid #23272f', color: '#e5e7eb', padding: '1.5rem' }}>
            <h2 className="text-lg font-semibold mb-4 text-center" style={{ color: '#e5e7eb' }}>Wallet Details</h2>
            <div className="mb-4">
              <p className="text-sm mb-1" style={{ color: '#a1a1aa' }}>Connected Wallet:</p>
              <div className="flex items-center rounded px-3 py-2" style={{ background: '#23272f' }}>
                <span className="truncate text-sm w-full" title={walletAddress} style={{ color: '#e5e7eb' }}>
                  {walletAddress}
                </span>
                <button
                  onClick={() => copyToClipboard(walletAddress)}
                  className="ml-2 hover:text-[#a1a1aa]"
                  style={{ color: '#a1a1aa' }}
                  title="Copy"
                >
                  <Copy className="w-4 h-4" color="#a1a1aa" />
                </button>
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={handleCloseWallet}
                className="text-sm px-4 py-2 rounded"
                style={{ background: '#23272f', color: '#e5e7eb' }}
              >
                Close
              </button>
              <button
                onClick={handleDisconnectWallet}
                className="text-sm px-4 py-2 rounded"
                style={{ background: '#f87171', color: '#fff' }}
              >
                Disconnect Wallet
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default UserDropdown;