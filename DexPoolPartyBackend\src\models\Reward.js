const mongoose = require("mongoose")

/**
 * @swagger
 * components:
 *   schemas:
 *     Reward:
 *       type: object
 *       properties:
 *         userId:
 *           type: string
 *           description: User ID who earned the reward
 *         type:
 *           type: string
 *           enum: [referral, trade_bonus, loyalty_bonus, daily_bonus, milestone_bonus, withdrawal]
 *         amount:
 *           type: number
 *           description: Reward amount
 *         description:
 *           type: string
 *         status:
 *           type: string
 *           enum: [pending, completed, withdrawn]
 */

const rewardSchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      ref: "User",
      required: true,
    },
    type: {
      type: String,
      enum: ["referral", "trade_bonus", "loyalty_bonus", "daily_bonus", "milestone_bonus", "withdrawal"],
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ["pending", "completed", "withdrawn"],
      default: "completed",
    },
    referralUserId: {
      type: String,
      ref: "User",
    },
    tradeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Trade",
    },
    metadata: {
      tradeAmount: Number,
      milestone: String,
      level: Number,
      withdrawalMethod: String,
      transactionHash: String,
    },
  },
  { timestamps: true },
)

// Index for better query performance
rewardSchema.index({ userId: 1, createdAt: -1 })
rewardSchema.index({ type: 1 })
rewardSchema.index({ status: 1 })

module.exports = mongoose.model("Reward", rewardSchema)
