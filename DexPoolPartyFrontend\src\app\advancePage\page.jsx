"use client"

import { useState, useMemo, useEffect } from "react"
import { Search, TrendingUp, Pause, ChevronRight, RefreshCw, Filter, ChevronLeft, MoreHorizontal, ArrowLeft } from 'lucide-react'
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { useTokenData } from "@/hook/use-token-data"
import FilterModal from "@/components/FilterModal"
import { useRouter } from 'next/navigation'
import BackButton from '@/components/BackButton.js'
import { SidebarContent } from "@/components/Sidebar";

// Constants
const ITEMS_PER_PAGE = 20
const MAX_VISIBLE_PAGES = 5

// Utility functions
const formatNumber = (num) => {
  const parsedNum = Number(num)
  if (num === null || num === undefined || isNaN(parsedNum)) return "N/A"
  if (parsedNum >= 1e9) return (parsedNum / 1e9).toFixed(2) + "B"
  if (parsedNum >= 1e6) return (parsedNum / 1e6).toFixed(2) + "M"
  if (parsedNum >= 1e3) return (parsedNum / 1e3).toFixed(2) + "K"
  return parsedNum.toFixed(2)
}

const formatPrice = (price) => {
  const parsedPrice = Number(price)
  if (price === null || price === undefined || isNaN(parsedPrice)) return "N/A"
  if (parsedPrice < 0.000001) return `$${parsedPrice.toExponential(3)}`
  if (parsedPrice < 0.01) return `$${parsedPrice.toFixed(6)}`
  if (parsedPrice < 1) return `$${parsedPrice.toFixed(4)}`
  return `$${parsedPrice.toFixed(2)}`
}

// Transform token data with graduation status
const transformTokenData = (rawToken) => {
  const mockPriceChange = (Math.random() - 0.5) * 20
  const supply = Number(rawToken.supply) || 1000000000
  const marketCap = Number(rawToken.marketCapUSD) || Math.random() * 1000000
  const mockPrice = marketCap / supply

  // Determine graduation status based on market cap and platform
  const isNewlyCreated = new Date(rawToken.createdAt) > new Date(Date.now() - 24 * 60 * 60 * 1000)
  const isAboutToGraduate = rawToken.isPumpFun && marketCap > 50000 && marketCap < 100000
  const isGraduated = (!rawToken.isPumpFun && !rawToken.isLaunchpad) || marketCap > 100000 || rawToken.isBoopFun

  return {
    id: rawToken.id || rawToken.mintAddress,
    symbol: rawToken.symbol || "N/A",
    name: rawToken.name || "Unknown Token",
    logo: rawToken.logo || "/placeholder.svg?height=48&width=48",
    price: mockPrice > 0 ? mockPrice : Math.random() * 0.001,
    change24h: mockPriceChange,
    marketCap: marketCap,
    volume: Math.random() * 1000000,
    ath: mockPrice * (1 + Math.random()),
    time: rawToken.createdAtFormatted || new Date(rawToken.createdAt).toLocaleString(),
    isNew: isNewlyCreated,
    isAboutToGraduate: isAboutToGraduate,
    isGraduated: isGraduated,
    isFeatured: (rawToken.isPumpFun && marketCap > 10000) || rawToken.isBoopFun,
    platform: rawToken.platform || "unknown",
    isPumpFun: rawToken.isPumpFun || false,
    isLaunchpad: rawToken.isLaunchpad || false,
    isBoopFun: rawToken.isBoopFun || false,
    isBonk: rawToken.isBonk || false,
    creator: rawToken.creator,
    mintAddress: rawToken.mintAddress,
    decimals: rawToken.decimals || 6,
    supply: rawToken.supply,
    transactionSignature: rawToken.transactionSignature,
    dataSource: rawToken.dataSource,
  }
}

// Pagination Component
const Pagination = ({ currentPage, totalPages, onPageChange, totalItems, currentRange, isMobile }) => {
  if (totalPages <= 1) return null

  const getVisiblePages = () => {
    const pages = []
    const maxVisible = isMobile ? 3 : MAX_VISIBLE_PAGES

    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      const half = Math.floor(maxVisible / 2)
      let start = Math.max(1, currentPage - half)
      let end = Math.min(totalPages, start + maxVisible - 1)

      if (end - start + 1 < maxVisible) {
        start = Math.max(1, end - maxVisible + 1)
      }

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
    }

    return pages
  }

  const visiblePages = getVisiblePages()
  const showFirstPage = visiblePages[0] > 1
  const showLastPage = visiblePages[visiblePages.length - 1] < totalPages

  return (
    <div className="flex flex-col gap-2 p-3 border-t border-zinc-700/50 bg-zinc-800/20">
      {/* Results info */}
      <div className="text-xs text-zinc-400 text-center">
        Showing {currentRange.start}-{currentRange.end} of {totalItems} tokens
      </div>

      {/* Pagination controls */}
      <div className="flex items-center justify-center gap-1">
        {/* Previous button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="h-8 w-8 p-0 text-zinc-400 hover:text-white hover:bg-zinc-700/50 disabled:opacity-30"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>

        {/* First page */}
        {showFirstPage && (
          <>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onPageChange(1)}
              className="h-8 w-8 p-0 text-zinc-400 hover:text-white hover:bg-zinc-700/50"
            >
              1
            </Button>
            {visiblePages[0] > 2 && (
              <span className="text-zinc-500 px-1">
                <MoreHorizontal className="w-4 h-4" />
              </span>
            )}
          </>
        )}

        {/* Visible pages */}
        {visiblePages.map((page) => (
          <Button
            key={page}
            variant="ghost"
            size="sm"
            onClick={() => onPageChange(page)}
            className={`h-8 w-8 p-0 ${currentPage === page
                ? "bg-purple-600/20 text-purple-400 border border-purple-600/30"
                : "text-zinc-400 hover:text-white hover:bg-zinc-700/50"
              }`}
          >
            {page}
          </Button>
        ))}

        {/* Last page */}
        {showLastPage && (
          <>
            {visiblePages[visiblePages.length - 1] < totalPages - 1 && (
              <span className="text-zinc-500 px-1">
                <MoreHorizontal className="w-4 h-4" />
              </span>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              className="h-8 w-8 p-0 text-zinc-400 hover:text-white hover:bg-zinc-700/50"
            >
              {totalPages}
            </Button>
          </>
        )}

        {/* Next button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="h-8 w-8 p-0 text-zinc-400 hover:text-white hover:bg-zinc-700/50 disabled:opacity-30"
        >
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )
}

// Skeleton Components
const TokenEntrySkeleton = ({ isMobile, isCollapsed = false }) => {
  if (isCollapsed) {
    return (
      <div className="flex flex-col items-center px-2 py-3 border-b border-zinc-700/30 animate-pulse">
        <div className="w-8 h-8 rounded-lg shimmer mb-2"></div>
        <div className="w-12 h-3 shimmer rounded mb-1"></div>
        <div className="w-16 h-3 shimmer rounded"></div>
        <div className="w-8 h-2.5 shimmer rounded mt-1"></div>
      </div>
    )
  }

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center px-4 py-4 border-b border-zinc-700/30 gap-3 sm:gap-0 animate-pulse rounded-lg mx-2 mb-1">
      <div className="flex items-center gap-3 min-w-0 w-full sm:w-[220px]">
        <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl shimmer flex-shrink-0"></div>
        <div className="min-w-0 flex-1">
          <div className="flex items-center gap-2 mb-1">
            <div className="w-20 h-4 shimmer rounded"></div>
            <div className="w-16 h-3 shimmer rounded hidden sm:inline"></div>
          </div>
          <div className="w-24 h-3 shimmer rounded mt-1 hidden sm:block"></div>
        </div>
      </div>
      <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-[180px] justify-between sm:justify-end mt-2 sm:mt-0">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <div className="w-7 h-7 shimmer rounded"></div>
            <div className="w-7 h-7 shimmer rounded"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

const CategoryColumnSkeleton = ({ isCollapsed, isMobile, count = 5 }) => {
  return (
    <div
      className={`bg-[#111115] border-b sm:border-r border-zinc-700/50 transition-all duration-300 ease-in-out
        ${isMobile ? "w-full" : isCollapsed ? "w-24 min-w-[96px]" : "flex-1 min-w-0 max-w-[480px]"}
        flex flex-col overflow-hidden shadow-lg`}
    >
      <div className="flex items-center justify-between py-3 px-4 bg-zinc-800/30 border-b border-zinc-700/50 sticky top-0 z-10 backdrop-blur-sm animate-pulse">
        <div className="flex items-center gap-3 overflow-hidden w-full">
          <div className="w-3 h-3 bg-zinc-700/50 rounded-full flex-shrink-0"></div>
          {(!isCollapsed || isMobile) && (
            <>
              <div className="w-24 h-4 bg-zinc-700/50 rounded"></div>
              <div className="w-16 h-4 bg-zinc-700/50 rounded-md"></div>
              <div className="w-8 h-4 bg-zinc-700/50 rounded-md ml-1"></div>
            </>
          )}
        </div>
        {!isMobile && <div className="w-7 h-7 bg-zinc-700/50 rounded-full flex-shrink-0 ml-2"></div>}
      </div>
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        <div className={`${isCollapsed && !isMobile ? "space-y-0" : "space-y-1 p-2"}`}>
          {Array.from({ length: count }).map((_, i) => (
            <TokenEntrySkeleton key={i} isMobile={isMobile} isCollapsed={isCollapsed && !isMobile} />
          ))}
        </div>
      </div>
    </div>
  )
}

// Token Entry Component
const TokenEntry = ({ token, isMobile, isCollapsed = false }) => {
  const isPositive = typeof token.change24h === "number" && token.change24h >= 0;

  // Always wrap the card in a Link to /[id] (token.mintAddress), for both collapsed and expanded
  return (
    <Link href={`/${token.mintAddress}`} className="block group">
      {isCollapsed ? (
        <div className="flex flex-col items-center px-2 py-3 hover:bg-zinc-700/20 border-b border-zinc-700/30 group relative transition-all duration-200">
          <div className="w-8 h-8 rounded-lg overflow-hidden bg-zinc-700/50 relative flex-shrink-0 mb-2">
            <Image
              src={token.logo || "/placeholder.svg"}
              alt={token.name}
              width={32}
              height={32}
              className="object-cover"
              onError={(e) => {
                e.target.src = "/placeholder.svg?height=32&width=32";
              }}
            />
          </div>
          <div className="text-center">
            <h3 className="font-semibold text-white text-xs truncate max-w-[60px]">{token.symbol.slice(0, 6)}</h3>
            <div className="text-xs text-zinc-400 mt-1">{formatPrice(token.price)}</div>
            <div
              className={`text-xs flex items-center justify-center gap-1 mt-1 ${isPositive ? "text-green-400" : "text-red-400"}`}
            >
              <TrendingUp className={`w-2.5 h-2.5 ${!isPositive ? "rotate-180" : ""}`} />
              <span className="text-[10px]">
                {isPositive ? "+" : ""}
                {Math.abs(token.change24h).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col sm:flex-row items-start sm:items-center px-4 py-4 hover:bg-zinc-700/20 border-b border-zinc-700/30 group relative gap-3 sm:gap-0 transition-all duration-200 rounded-lg mx-2 mb-1">
          <div className="flex items-center gap-3 min-w-0 w-full sm:w-[220px]">
            <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl overflow-hidden bg-zinc-700/50 relative flex-shrink-0 ring-1 ring-zinc-600/20">
              <Image
                src={token.logo || "/placeholder.svg"}
                alt={token.name}
                width={48}
                height={48}
                className="object-cover"
                onError={(e) => {
                  e.target.src = "/placeholder.svg?height=48&width=48";
                }}
              />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-bold text-white text-sm truncate max-w-[calc(100%-80px)]">{token.symbol}</h3>
                <span className="text-xs text-zinc-400 truncate flex-shrink-0 hidden sm:inline">
                  {token.name.length > 12 ? `${token.name.slice(0, 12)}...` : token.name}
                </span>

                {token.isPumpFun && (
                  <Badge variant="secondary" className="text-[8px] bg-purple-600/20 text-purple-400 px-1 flex-shrink-0">
                    PF
                  </Badge>
                )}
                {token.isLaunchpad && (
                  <Badge variant="secondary" className="text-[8px] bg-blue-600/20 text-blue-400 px-1 flex-shrink-0">
                    LP
                  </Badge>
                )}
                {token.isBoopFun && (
                  <Badge variant="secondary" className="text-[8px] bg-green-600/20 text-green-400 px-1 flex-shrink-0">
                    BF
                  </Badge>
                )}
                {token.isBonk && (
                  <Badge variant="secondary" className="text-[8px] bg-green-600/20 text-green-400 px-1 flex-shrink-0">
                    BN
                  </Badge>
                )}
              </div>
              <div className="flex flex-wrap items-center gap-2 text-xs text-zinc-400 mt-1">
                <span className="whitespace-nowrap bg-zinc-800/50 px-2 py-0.5 rounded-md flex-shrink-0">
                  MC: {formatNumber(token.marketCap)}
                </span>
                <span className="whitespace-nowrap bg-zinc-800/50 px-2 py-0.5 rounded-md flex-shrink-0">
                  Vol: {formatNumber(token.volume)}
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-[180px] justify-between sm:justify-end mt-2 sm:mt-0">
            <div className="text-left sm:text-right flex-grow sm:flex-grow-0">
              <div className="text-sm text-white font-bold">{formatPrice(token.price)}</div>
              <div
                className={`text-sm flex items-center sm:justify-end gap-1.5 ${isPositive ? "text-green-400" : "text-red-400"}`}
              >
                <TrendingUp className={`w-3.5 h-3.5 ${!isPositive ? "rotate-180" : ""}`} />
                <span className="font-medium whitespace-nowrap">
                  {isPositive ? "+" : ""}
                  {Math.abs(token.change24h).toFixed(1)}%
                </span>
              </div>
              <div className="text-xs text-zinc-500 hidden sm:block mt-0.5 whitespace-nowrap">
                Supply: {formatNumber(token.supply)}
              </div>
            </div>
          </div>
        </div>
      )}
    </Link>
  );
};

// Category Column Component
const CategoryColumn = ({ category, isCollapsed, onToggle, isMobile, currentPage, onPageChange }) => {
  const totalPages = Math.ceil(category.allTokens.length / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const endIndex = startIndex + ITEMS_PER_PAGE
  const paginatedTokens = category.allTokens.slice(startIndex, endIndex)

  const currentRange = {
    start: category.allTokens.length > 0 ? startIndex + 1 : 0,
    end: Math.min(endIndex, category.allTokens.length)
  }

  return (
    <div
      className={`bg-[#111115] border-b sm:border-r border-zinc-700/50 transition-all duration-300 ease-in-out
        ${isMobile ? "w-full" : isCollapsed ? "w-24 min-w-[96px]" : "flex-1 min-w-0 max-w-[480px]"}
        flex flex-col overflow-hidden shadow-lg`}
    >
      <div className="flex items-center justify-between py-3 px-4 bg-zinc-800/30 border-b border-zinc-700/50 sticky top-0 z-10 backdrop-blur-sm">
        <div className="flex items-center gap-3 overflow-hidden">
          <span className={`w-3 h-3 ${category.color} rounded-full flex-shrink-0 shadow-sm`}></span>
          {(!isCollapsed || isMobile) && (
            <>
              <span className="text-sm font-semibold text-zinc-200 truncate">{category.title}</span>
              <Badge
                variant="outline"
                className="text-[10px] border-zinc-600/50 text-zinc-400 px-2 py-0.5 flex-shrink-0 bg-zinc-700/30"
              >
                {category.filter || "all"}
              </Badge>
              <Badge
                variant="secondary"
                className="text-[10px] bg-zinc-700/50 text-zinc-300 ml-1 flex-shrink-0 font-medium"
              >
                {category.allTokens.length}
              </Badge>
              {totalPages > 1 && (
                <Badge
                  variant="outline"
                  className="text-[9px] border-purple-600/30 text-purple-400 px-1.5 py-0.5 flex-shrink-0 bg-purple-600/10"
                >
                  {currentPage}/{totalPages}
                </Badge>
              )}
            </>
          )}
          {isCollapsed && !isMobile && (
            <div className="flex flex-col items-center">
              <span className="text-xs font-medium text-zinc-300 writing-mode-vertical transform rotate-90 whitespace-nowrap">
                {category.title.split(" ")[0]}
              </span>
              <Badge variant="secondary" className="text-[9px] bg-zinc-700/50 text-zinc-300 mt-2 px-1">
                {category.allTokens.length}
              </Badge>
            </div>
          )}
        </div>
        {!isMobile && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onToggle}
            className="h-7 w-7 p-0 hover:bg-zinc-700/50 flex-shrink-0 ml-2 transition-all duration-200"
          >
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4 text-purple-400" />
            ) : (
              <Pause className="w-4 h-4 text-purple-400" />
            )}
          </Button>
        )}
      </div>

      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 overflow-y-auto custom-scrollbar">
          <div className={`${isCollapsed && !isMobile ? "space-y-0" : "space-y-1 p-2"}`}>
            {paginatedTokens.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-10 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto mb-2 w-10 h-10 text-zinc-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2a4 4 0 018 0v2m-4-4a4 4 0 100-8 4 4 0 000 8zm0 0v2m0 4h.01" /></svg>
                <span className="text-zinc-400 text-base font-medium">No tokens found.</span>
                <span className="text-zinc-500 text-xs mt-1">Try adjusting your filters or <button onClick={window.location.reload} className="text-purple-400 underline hover:text-purple-300">refreshing</button>.</span>
              </div>
            ) : (
              paginatedTokens.map((token) => (
                <TokenEntry token={token} isMobile={isMobile} isCollapsed={isCollapsed && !isMobile} />
              ))
            )}
          </div>
        </div>

        {/* Pagination */}
        {!isCollapsed && totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
            totalItems={category.allTokens.length}
            currentRange={currentRange}
            isMobile={isMobile}
          />
        )}
      </div>
    </div>
  )
}

// Main Component
const TokenFeedContent = () => {
  const [searchQuery, setSearchQuery] = useState("")
  const [collapsedColumns, setCollapsedColumns] = useState({})
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [activeMobileCategory, setActiveMobileCategory] = useState("newly-created")
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false)
  const [activeFilters, setActiveFilters] = useState({
    minMarketCap: "",
    maxMarketCap: "",
    minVolume: "",
    change24hType: "any",
    categories: [],
  })

  // Pagination state for each category
  const [categoryPages, setCategoryPages] = useState({
    "newly-created": 1,
    "about-to-graduate": 1,
    "graduated": 1,
    "featured": 1,
  })

  const { tokens: rawTokens, loading, error, refetch, platformStatus } = useTokenData()
  const router = useRouter()

  // Helper: Are all platforms green (loaded and no error)?
  const allPlatformsGreen = Object.values(platformStatus).every(
    (status) => status.loading === false && !status.error
  );

  const baseCategories = useMemo(
    () => [
      { id: "newly-created", title: "Newly Created", color: "bg-green-500", filter: "24h" },
      { id: "about-to-graduate", title: "About to Graduate", color: "bg-yellow-500", filter: "50K-100K MC" },
      { id: "graduated", title: "Graduated", color: "bg-blue-500", filter: ">100K MC" },
      { id: "featured", title: "Featured", color: "bg-purple-500", filter: "trending" },
    ],
    [],
  )

  const transformedTokens = useMemo(() => {
    if (!rawTokens || !Array.isArray(rawTokens)) return []
    return rawTokens.map(transformTokenData)
  }, [rawTokens])

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 640)
      setIsTablet(window.innerWidth >= 640 && window.innerWidth < 1024)
    }
    checkScreenSize()
    window.addEventListener("resize", checkScreenSize)
    return () => window.removeEventListener("resize", checkScreenSize)
  }, [])

  const handleApplyFilters = (filters) => {
    setActiveFilters(filters)
    // Reset pagination when filters change
    setCategoryPages({
      "newly-created": 1,
      "about-to-graduate": 1,
      "graduated": 1,
      "featured": 1,
    })
  }

  // Reset pagination when search query changes
  useEffect(() => {
    setCategoryPages({
      "newly-created": 1,
      "about-to-graduate": 1,
      "graduated": 1,
      "featured": 1,
    })
  }, [searchQuery])

  const filteredAndCategorizedData = useMemo(() => {
    if (!transformedTokens.length) return baseCategories.map((cat) => ({ ...cat, allTokens: [] }))

    let currentFilteredTokens = transformedTokens

    const lowerSearch = searchQuery.toLowerCase()
    currentFilteredTokens = currentFilteredTokens.filter(
      (t) => t.symbol?.toLowerCase().includes(lowerSearch) || t.name?.toLowerCase().includes(lowerSearch),
    )

    // Apply active filters
    if (activeFilters.minMarketCap) {
      currentFilteredTokens = currentFilteredTokens.filter((t) => t.marketCap >= Number(activeFilters.minMarketCap))
    }
    if (activeFilters.maxMarketCap) {
      currentFilteredTokens = currentFilteredTokens.filter((t) => t.marketCap <= Number(activeFilters.maxMarketCap))
    }
    if (activeFilters.minVolume) {
      currentFilteredTokens = currentFilteredTokens.filter((t) => t.volume >= Number(activeFilters.minVolume))
    }

    if (activeFilters.change24hType === "positive") {
      currentFilteredTokens = currentFilteredTokens.filter((t) => t.change24h > 0)
    } else if (activeFilters.change24hType === "negative") {
      currentFilteredTokens = currentFilteredTokens.filter((t) => t.change24h < 0)
    }

    if (activeFilters.categories && activeFilters.categories.length > 0) {
      currentFilteredTokens = currentFilteredTokens.filter((token) =>
        activeFilters.categories.some(
          (selectedCategory) =>
            (selectedCategory === "newly-created" && token.isNew) ||
            (selectedCategory === "about-to-graduate" && token.isAboutToGraduate) ||
            (selectedCategory === "graduated" && token.isGraduated) ||
            (selectedCategory === "featured" && token.isFeatured),
        ),
      )
    }

    return baseCategories.map((cat) => {
      let categoryTokens = []

      switch (cat.id) {
        case "newly-created":
          categoryTokens = currentFilteredTokens.filter((token) => token.isNew)
          break
        case "about-to-graduate":
          categoryTokens = currentFilteredTokens.filter((token) => token.isAboutToGraduate)
          break
        case "graduated":
          categoryTokens = currentFilteredTokens.filter((token) => token.isGraduated)
          break
        case "featured":
          categoryTokens = currentFilteredTokens.filter((token) => token.isFeatured)
          break
        default:
          categoryTokens = []
      }

      categoryTokens.sort((a, b) => (b.marketCap || 0) - (a.marketCap || 0))
      return { ...cat, allTokens: categoryTokens }
    })
  }, [transformedTokens, searchQuery, activeFilters, baseCategories])

  useEffect(() => {
    if (transformedTokens.length && Object.keys(collapsedColumns).length === 0) {
      const initialStates = {}
      baseCategories.forEach((cat) => (initialStates[cat.id] = false))
      setCollapsedColumns(initialStates)
    }
  }, [transformedTokens, baseCategories])

  const categoriesToRender = useMemo(() => {
    if (isMobile || isTablet) {
      return filteredAndCategorizedData.filter((cat) => cat.id === activeMobileCategory)
    }
    return filteredAndCategorizedData
  }, [isMobile, isTablet, filteredAndCategorizedData, activeMobileCategory])

  const handlePageChange = (categoryId, newPage) => {
    setCategoryPages(prev => ({
      ...prev,
      [categoryId]: newPage
    }))
  }

  return (
    <div className="w-full min-h-screen bg-[#111115] overflow-hidden">
      {/* Mobile Back Arrow - only on mobile, sticky at top */}
      {/* <div className="md:hidden sticky top-0 z-30 bg-[#111115]/95 backdrop-blur-md border-b border-zinc-700/50 flex items-center h-12 px-2">
        <BackButton />
      </div> */}
      <div className="sticky top-0 z-20 bg-[#111115]/95 backdrop-blur-md border-b border-zinc-700/50 shadow-lg">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4 max-w-[1800px] mx-auto p-4 mt-5">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 w-full">
            <div className="w-full sm:flex-1 sm:max-w-[400px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400" />
                <input
                  type="text"
                  placeholder="Search tokens..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-zinc-800/50 text-white pl-10 pr-4 py-3 rounded-xl border border-zinc-700/50 focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20 text-sm transition-all duration-200 placeholder-zinc-500"
                />
              </div>
            </div>
            <Button
              variant="outline"
              onClick={refetch}
              disabled={loading}
              className="w-full sm:w-auto bg-zinc-800/50 border-zinc-700/50 text-white hover:bg-zinc-700/50 hover:border-zinc-600/50 hover:text-white transition-all duration-200 px-6 py-3 rounded-xl"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsFilterModalOpen(true)}
              className="w-full sm:w-auto bg-zinc-800/50 border-zinc-700/50 text-white hover:bg-zinc-700/50 hover:border-zinc-600/50 hover:text-white transition-all duration-200 px-6 py-3 rounded-xl"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>
        </div>

        {/* Platform Status Indicator */}
        <div className="px-4 pb-2">
          <div className="flex gap-2 text-xs">
            {Object.entries(platformStatus).map(([platform, status]) => (
              <div key={platform} className="flex items-center gap-1">
                <div
                  className={`w-2 h-2 rounded-full ${status.loading ? "bg-yellow-500 animate-pulse" : status.error ? "bg-red-500" : "bg-green-500"
                    }`}
                ></div>
                <span className="text-zinc-400 capitalize">{platform}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {(isMobile || isTablet) && (
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 px-4 py-4 border-b border-zinc-700/50 bg-[#111115] sticky top-[120px] z-10">
          {baseCategories.map((cat) => {
            const isActive = activeMobileCategory === cat.id
            const categoryData = filteredAndCategorizedData.find(c => c.id === cat.id)
            const totalTokens = categoryData?.allTokens.length || 0

            return (
              <Button
                key={cat.id}
                onClick={() => setActiveMobileCategory(cat.id)}
                variant="outline"
                className={`relative px-4 py-3 text-xs font-semibold rounded-xl transition-all duration-300 ease-in-out
                  ${isActive
                    ? "bg-zinc-700/50 text-white shadow-lg scale-[1.02] border-purple-500/30"
                    : "bg-zinc-800/30 text-zinc-400 border-zinc-700/30"
                  } hover:bg-zinc-700/40 hover:border-zinc-600/40`}
              >
                <div className="flex flex-col items-center gap-1">
                  <span>{cat.title}</span>
                  <span className="text-[10px] opacity-70">({totalTokens})</span>
                </div>
                {isActive && (
                  <span className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-3 h-3 bg-purple-500 rounded-full transition-all duration-300 shadow-lg" />
                )}
              </Button>
            )
          })}
        </div>
      )}

      <div className="max-w-[1800px] w-full mx-auto relative">
        <div
          className={`${isMobile || isTablet ? "flex flex-col" : "flex flex-1 space-x-0"} h-[calc(100vh-140px)] overflow-x-auto`}
        >
          {!allPlatformsGreen ? (
            isMobile || isTablet ? (
              <CategoryColumnSkeleton isMobile={isMobile || isTablet} isCollapsed={false} count={5} />
            ) : (
              baseCategories.map((cat) => (
                <CategoryColumnSkeleton
                  key={cat.id}
                  isMobile={isMobile || isTablet}
                  isCollapsed={collapsedColumns[cat.id] || false}
                  count={5}
                />
              ))
            )
          ) : error ? (
            <div className="flex-1 flex flex-col items-center justify-center text-red-400 text-lg p-8">
              <p>Failed to load tokens: {error}</p>
              <Button onClick={refetch} className="mt-4">
                Try Again
              </Button>
            </div>
          ) : (
            categoriesToRender.map((cat) => (
              <CategoryColumn
                key={cat.id}
                category={cat}
                isCollapsed={!isMobile && !isTablet && (collapsedColumns[cat.id] || false)}
                onToggle={() =>
                  setCollapsedColumns({
                    ...collapsedColumns,
                    [cat.id]: !collapsedColumns[cat.id],
                  })
                }
                isMobile={isMobile || isTablet}
                currentPage={categoryPages[cat.id]}
                onPageChange={(newPage) => handlePageChange(cat.id, newPage)}
              />
            ))
          )}
        </div>
      </div>

      <FilterModal
        isOpen={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        onApplyFilters={handleApplyFilters}
        currentFilters={activeFilters}
      />

      {/* Mobile Bottom Sidebar Navigation for Marketplace */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 z-50 h-[56px] bg-black/95 backdrop-blur-sm border-t border-white/10">
        {/* Reuse SidebarContent from Sidebar.js for consistency */}
        <SidebarContent isMobile={true} collapsed={false} />
      </div>

      <style jsx global>{`
        html, body {
          overflow: auto;
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        html::-webkit-scrollbar,
        body::-webkit-scrollbar {
          display: none;
        }
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
          -ms-overflow-style: none;
        }
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          transition: background 0.2s ease;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }

        @media (max-width: 640px) {
          .custom-scrollbar::-webkit-scrollbar {
            display: none;
          }
        }
        /* Shimmer animation for skeletons */
        .shimmer {
          position: relative;
          overflow: hidden;
          background: linear-gradient(90deg, #23232a 25%, #2a2a2f 50%, #23232a 75%);
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite linear;
        }
        @keyframes shimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }
      `}</style>
    </div>
  )
}

export default function TokenFeedPage() {
  return <TokenFeedContent />
}
