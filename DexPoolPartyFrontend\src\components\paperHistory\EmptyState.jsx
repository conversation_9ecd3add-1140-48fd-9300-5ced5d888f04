import React from 'react';
import { Search } from 'lucide-react';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

const EmptyState = ({ onClearFilters, isFiltered }) => {
  return (
    <div className="flex flex-col items-center justify-center py-16">
      <div className="w-40 h-40 mb-6">
        <DotLottieReact
          src="https://lottie.host/badb476d-3b91-40d4-8c3d-12ed0bc86b83/DBa7HQ2Qcb.lottie"
          loop
          autoplay
        />
      </div>
      <h3 className="text-2xl font-bold text-gray-200 mb-2 border-b-4 border-blue-500 pb-2">No trades found</h3>
      <p className="text-gray-400 mb-4">Try adjusting your search or filter criteria.</p>
      {isFiltered && (
        <button
          onClick={onClearFilters}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl shadow font-semibold text-base transition"
        >
          Clear all filters
        </button>
      )}
    </div>
  );
};

export default EmptyState;