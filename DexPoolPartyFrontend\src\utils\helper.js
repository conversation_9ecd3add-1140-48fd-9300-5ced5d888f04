export const getStateConfig = (state) => {
  switch (state) {
    case 'completed':
      return { 
        color: 'bg-emerald-500/10 text-emerald-400 border-emerald-500/20', 
        label: 'Completed'
      };
    case 'pending':
      return { 
        color: 'bg-amber-500/10 text-amber-400 border-amber-500/20', 
        label: 'Pending'
      };
    case 'partial':
      return { 
        color: 'bg-blue-500/10 text-blue-400 border-blue-500/20', 
        label: 'Partial'
      };
    case 'cancelled':
      return { 
        color: 'bg-red-500/10 text-red-400 border-red-500/20', 
        label: 'Cancelled'
      };
    default:
      return { 
        color: 'bg-gray-500/10 text-gray-400 border-gray-500/20', 
        label: 'Unknown'
      };
  }
};

export const getPredictionConfig = (prediction) => {
  if (!prediction) {
    return {
      color: 'bg-gray-500/10 text-gray-400 border-gray-500/20',
      label: 'No Analysis',
      bgGradient: 'from-gray-500/5 to-transparent'
    };
  }

  const { trend } = prediction;
  
  switch (trend) {
    case 'bullish':
      return {
        color: 'bg-emerald-500/10 text-emerald-400 border-emerald-500/20',
        label: 'Bullish',
        bgGradient: 'from-emerald-500/5 to-transparent'
      };
    case 'bearish':
      return {
        color: 'bg-red-500/10 text-red-400 border-red-500/20',
        label: 'Bearish',
        bgGradient: 'from-red-500/5 to-transparent'
      };
    case 'neutral':
      return {
        color: 'bg-gray-500/10 text-gray-400 border-gray-500/20',
        label: 'Neutral',
        bgGradient: 'from-gray-500/5 to-transparent'
      };
    case 'recovery':
      return {
        color: 'bg-purple-500/10 text-purple-400 border-purple-500/20',
        label: 'Recovery',
        bgGradient: 'from-purple-500/5 to-transparent'
      };
    default:
      return {
        color: 'bg-gray-500/10 text-gray-400 border-gray-500/20',
        label: 'Unknown',
        bgGradient: 'from-gray-500/5 to-transparent'
      };
  }
};

export const getConfidenceColor = (confidence) => {
  if (!confidence) return 'text-gray-400';
  if (confidence >= 80) return 'text-emerald-400';
  if (confidence >= 60) return 'text-amber-400';
  return 'text-red-400';
};

export const formatSignal = (signal) => {
  if (!signal) return '';
  return signal.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

export const formatDate = (timestamp) => {
  if (!timestamp) return 'N/A';
  return new Date(timestamp).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const formatAddress = (address) => {
  if (!address) return 'N/A';
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};