import React from 'react'

const getCoins = (req,res) => {
    const getData = async () =>{
        const response = await fetch(`${process.env.COINMARKETCAP_BASE_URL}/v1/cryptocurrency/listings/latest?CMC_PRO_API_KEY=${process.env.COINMARKETCAP_API_KEY}`,
            {
                method: 'GET',
                headers:{
                    Accept:'*/*'
                },
            },
        )
        const data = await response.json()
        res.status(200).json(data);
    }
    getData();
}

export default getCoins
// https://pro-api.coinmarketcap.com/v1/cryptocurrency/categories


// const response = await fetch('https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest?CMC_PRO_API_KEY=${process.env.COINMARKETCAP_API_KEY}`',
// pages/api/getCoins.js

// export default async function handler(req, res) {
//     try {
//       const response = await fetch(
//         'https://pro-api.coinmarketcap.com/v1/cryptocurrency/info?id=1',
//         {
//           method: 'GET',
//           headers: {
//             'X-CMC_PRO_API_KEY': '${process.env.COINMARKETCAP_API_KEY}`',
//             'Accept': 'application/json',
//             'Accept-Encoding': 'deflate, gzip',
//           },
//         }
//       );
  
//       if (!response.ok) {
//         const errorData = await response.json();
//         console.error('API Error:', errorData);
//         return res.status(response.status).json({
//           error: 'Failed to fetch data',
//           details: errorData,
//         });
//       }
  
//       const data = await response.json();
//       res.status(200).json(data);
//     } catch (error) {
//       console.error('Server Error:', error);
//       res.status(500).json({
//         error: 'Internal Server Error',
//         message: error.message,
//       });
//     }
//   }
  