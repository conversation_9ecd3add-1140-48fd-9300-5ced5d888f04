const BITQUERY_ENDPOINT = "https://streaming.bitquery.io/eap"
const BITQUERY_API_KEY = process.env.BITQUERY_API_KEY || ""

const BONK_INITIALIZE_QUERY = `
  query GetBonkCreationEvents($limit: Int = 100, $since: DateTime, $mint: String) {
    Solana {
      InstructionBalanceUpdates(
        where: {
          BalanceUpdate: { Currency: { MintAddress: { is: $mint } } }
          Instruction: {
            Program: {
              Address: { is: "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj" }
              Method: { is: "initialize" }
            }
          }
          Transaction: { Result: { Success: true } }
          Block: {
            Time: { since: $since }
          }
        }
        orderBy: { descending: Block_Time }
        limit: { count: $limit }
      ) {
        BalanceUpdate {
          Currency {
            MintAddress
            Name
            Symbol
            Decimals
            UpdateAuthority
            Uri # This is the metadata URI
            VerifiedCollection
            Wrapped
            ProgramAddress
          }
          PostBalance
        }
        Block {
          Time
        }
        Transaction {
          Signature
          Signer
        }
      }
    }
  }
`

// Helper function to resolve IPFS URIs
function resolveIpfsUri(uri) {
  if (uri && uri.startsWith("ipfs://")) {
    return `https://ipfs.io/ipfs/${uri.substring(7)}` // Using a public IPFS gateway
  }
  // You might want to add other resolvers for arweave:// or other protocols
  return uri
}

// Add helper to fetch latest price from Bitquery DEXTrades
async function fetchLatestPriceUSD(mintAddress) {
  const BITQUERY_API_KEY = process.env.BITQUERY_API_KEY;
  const BITQUERY_API_URL = "https://streaming.bitquery.io/eap";
  const query = `{
    Solana {
      DEXTrades(
        where: {
          Trade: { Buy: { Currency: { MintAddress: { is: \"${mintAddress}\" } } } }
          Transaction: { Result: { Success: true } }
        }
        orderBy: { descending: Block_Time }
        limit: 1
      ) {
        Trade { Buy { PriceInUSD } }
      }
    }
  }`;
  const response = await fetch(BITQUERY_API_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-API-KEY": BITQUERY_API_KEY,
    },
    body: JSON.stringify({ query }),
  });
  const data = await response.json();
  const trades = data?.data?.Solana?.DEXTrades || [];
  return trades[0]?.Trade?.Buy?.PriceInUSD || 0;
}

export default async function handler(req, res) {
  try {
    const { searchParams } = new URL(req.url, `http://${req.headers.host}`)
    const limit = Number.parseInt(searchParams.get("limit") || "100")
    const since = searchParams.get("since")
    const mint = searchParams.get("mint") || undefined;

    // Default to the last 24 hours if no 'since' parameter is provided
    const defaultSince = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    const sinceParam = since || defaultSince

    const requestBody = {
      query: BONK_INITIALIZE_QUERY,
      variables: {
        limit: limit,
        since: sinceParam,
        mint: mint,
      },
    }

    if (req.method === "POST") {
      const body = await req.json()
      const { customVariables = {} } = body
      requestBody.variables = {
        ...requestBody.variables,
        ...customVariables,
      }
    }

    console.log("Making BitQuery request for Bonk with variables:", requestBody.variables)

    const response = await fetch(BITQUERY_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BITQUERY_API_KEY}`,
        "X-API-KEY": BITQUERY_API_KEY,
      },
      body: JSON.stringify(requestBody),
    })

    // Handle non-JSON responses (e.g., API quota exceeded)
    const contentType = response.headers.get("content-type")
    if (!contentType || !contentType.includes("application/json")) {
      const errorText = await response.text()
      console.error(`BitQuery API returned non-JSON response for Bonk: ${response.status} - ${errorText.substring(0, 200)}`)
      return res.status(response.status).json({
        success: false,
        error: `BitQuery API error: ${response.status} ${response.statusText}`,
        details: "API quota exceeded or service unavailable",
        timestamp: new Date().toISOString(),
      })
    }

    if (!response.ok) {
      const errorData = await response.json()
      console.error(`BitQuery API error for Bonk: ${response.status} ${response.statusText}`, errorData)
      return res.status(response.status).json({
        success: false,
        error: `BitQuery API error: ${response.statusText}`,
        details: errorData,
        timestamp: new Date().toISOString(),
      })
    }

    const result = await response.json()

    if (result.errors) {
      console.error("BitQuery errors for Bonk:", result.errors)
      return res.status(400).json({
        success: false,
        error: "BitQuery returned GraphQL errors for Bonk",
        details: result.errors,
        timestamp: new Date().toISOString(),
      })
    }

    // Transform Bonk data to a consistent format
    const transformedDataPromises = (result.data?.Solana?.InstructionBalanceUpdates || []).map(async (item) => {
      const currency = item.BalanceUpdate?.Currency || {}
      const block = item.Block || {}
      const transaction = item.Transaction || {}

      let logoUrl = null
      if (currency.Uri) {
        const metadataUri = resolveIpfsUri(currency.Uri)
        try {
          const metadataResponse = await fetch(metadataUri, {
            cache: "force-cache", // Consider caching if these are frequently accessed
            signal: AbortSignal.timeout(5000), // Timeout for metadata fetch
          })
          if (metadataResponse.ok) {
            const metadataJson = await metadataResponse.json()
            if (metadataJson && typeof metadataJson.image === "string") {
              logoUrl = resolveIpfsUri(metadataJson.image)
            }
          } else {
            console.warn(`Failed to fetch metadata from ${metadataUri}: ${metadataResponse.status} - ${metadataResponse.statusText}`)
          }
        } catch (metadataError) {
          // Log specific errors for metadata fetching, but don't crash the main API route
          console.error(`Error fetching or parsing metadata for ${currency.MintAddress} from ${metadataUri}:`, metadataError)
        }
      }

      // Fetch price and calculate market cap
      const priceUSD = await fetchLatestPriceUSD(currency.MintAddress);
      let supply = item.BalanceUpdate?.PostBalance || 0;
      let marketCapUSD = 0;
      if (priceUSD && supply) {
        marketCapUSD = Number(priceUSD) * Number(supply);
      }

      return {
        id: currency.MintAddress,
        symbol: currency.Symbol,
        name: currency.Name,
        mintAddress: currency.MintAddress,
        creator: transaction.Signer || "Unknown",
        createdAt: block.Time ? new Date(block.Time).toISOString() : null,
        createdAtFormatted: block.Time ? new Date(block.Time).toLocaleString() : "Unknown",
        blockNumber: 0, // Not directly available in this query
        blockHash: "", // Not directly available
        transactionSignature: transaction.Signature || "",
        transactionFee: 0, // Not available in this specific query
        supply: "N/A",
        postBalance: item.BalanceUpdate?.PostBalance,
        marketCapUSD: marketCapUSD.toString(),
        decimals: currency.Decimals,
        isMutable: true,
        fungible: !currency.Wrapped,
        metadataUri: currency.Uri || null,
        logo: logoUrl || `/placeholder.svg`, // Fallback to a local placeholder
        updateAuthority: currency.UpdateAuthority || null,
        tokenStandard: null,
        programAddress: currency.ProgramAddress || "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj",
        primarySaleHappened: true,
        isPumpFun: false,
        isLaunchpad: false,
        isBoopFun: false,
        isBonk: true,
        platform: "bonk",
        isGraduated: false,
        dataSource: "bonk",
        fetchedAt: new Date().toISOString(),
      }
    })

    const transformedData = await Promise.all(transformedDataPromises)

    return res.status(200).json({
      success: true,
      data: transformedData,
      count: transformedData.length,
      queryType: "bonk_initialize",
      since: sinceParam,
      timestamp: new Date().toISOString(),
      message:
        transformedData.length > 0
          ? `Found ${transformedData.length} Bonk initialization events.`
          : "No Bonk initialization events found in the specified time range.",
    })
  } catch (error) {
    console.error("Bonk API Error:", error)
    return res.status(500).json({
      success: false,
      error: "Internal Server Error",
      details: error.message,
      data: [],
      count: 0,
      timestamp: new Date().toISOString(),
    })
  }
}