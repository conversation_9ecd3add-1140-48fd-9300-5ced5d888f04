require("dotenv").config();
const mongoose = require("mongoose");
const User = require("./src/models/user.model.js");

async function testUsernameFix() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Find any user to test with
    console.log("🔍 Looking for any user in the database...");
    const users = await User.find({}).limit(1);

    if (users.length === 0) {
      console.log("❌ No users found in database");
      return;
    }

    const user = users[0];
    const testEmail = user.email;

    console.log(`🔍 Found user with email: ${testEmail}`);

    if (!user) {
      console.log("❌ User not found");
      return;
    }

    console.log("👤 Current user data:", {
      id: user._id,
      email: user.email,
      username: user.username,
      hasCustomUsername: user.hasCustomUsername,
      authProvider: user.authProvider,
    });

    // Test updating the username and hasCustomUsername flag
    console.log("\n🔧 Testing username update...");

    const originalUsername = user.username;
    const testUsername = "kapil";

    user.username = testUsername;
    user.hasCustomUsername = true;

    await user.save();
    console.log("✅ User updated successfully");

    // Verify the update
    const updatedUser = await User.findOne({ email: testEmail });
    console.log("🔍 Verification - Updated user data:", {
      id: updatedUser._id,
      email: updatedUser.email,
      username: updatedUser.username,
      hasCustomUsername: updatedUser.hasCustomUsername,
      authProvider: updatedUser.authProvider,
    });

    // Test the sync logic
    console.log("\n🔄 Testing sync logic...");

    const mockFirebaseUser = {
      uid: user._id,
      email: user.email,
      name: "private for", // Original Google name
      display_name: "private for",
      email_verified: true,
    };

    // Simulate the sync logic
    if (
      updatedUser.hasCustomUsername === undefined ||
      updatedUser.hasCustomUsername === null
    ) {
      const googleName = mockFirebaseUser.name || mockFirebaseUser.display_name;
      if (
        googleName &&
        updatedUser.username &&
        updatedUser.username !== googleName
      ) {
        updatedUser.hasCustomUsername = true;
        console.log(
          `🔧 [MIGRATION] User has custom username: "${updatedUser.username}" vs Google: "${googleName}"`
        );
      } else {
        updatedUser.hasCustomUsername = false;
        console.log(
          `🔧 [MIGRATION] User using Google username: "${googleName}"`
        );
      }
    } else {
      console.log(
        `ℹ️ [SYNC] User hasCustomUsername already set: ${updatedUser.hasCustomUsername}`
      );
    }

    // Test the username preservation logic
    const googleName = mockFirebaseUser.name || mockFirebaseUser.display_name;
    if (googleName && !updatedUser.hasCustomUsername) {
      console.log(
        `🔄 [SYNC] Would update username from Google: "${updatedUser.username}" -> "${googleName}"`
      );
      // Don't actually update in test
    } else if (googleName && updatedUser.hasCustomUsername) {
      console.log(
        `🚫 [SYNC] Would preserve custom username: "${updatedUser.username}" (Google: "${googleName}")`
      );
    }

    console.log("\n✅ Test completed successfully");
  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

testUsernameFix();
