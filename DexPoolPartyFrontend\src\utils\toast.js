import { toast } from 'sonner';

// Set global toast limit
if (typeof window !== 'undefined' && toast) {
  toast.configure && toast.configure({ limit: 3 });
}

export const showToast = {
  success: (message, options = {}) => {
    toast.success(message, {
      duration: 4000,
      position: 'top-right',
      ...options,
    });
  },
  
  error: (message, options = {}) => {
    toast.error(message, {
      duration: 5000,
      position: 'top-right',
      ...options,
    });
  },
  
  warning: (message, options = {}) => {
    toast.warning(message, {
      duration: 4000,
      position: 'top-right',
      ...options,
    });
  },
  
  info: (message, options = {}) => {
    toast.info(message, {
      duration: 3000,
      position: 'top-right',
      ...options,
    });
  },
  dismiss: () => toast.dismiss(),
};

export const handleApiError = (error, customMessage = 'Something went wrong') => {
  console.error('API Error:', error);
  
  let message = customMessage;
  
  if (error.response?.data?.message) {
    message = error.response.data.message;
  } else if (error.message) {
    message = error.message;
  } else if (typeof error === 'string') {
    message = error;
  }
  
  showToast.error(message);
  return message;
};

export const handleApiSuccess = (message = 'Operation completed successfully') => {
  showToast.success(message);
}; 

export function showLoginFirstToast() {
  toast.warning('Please login first');
} 