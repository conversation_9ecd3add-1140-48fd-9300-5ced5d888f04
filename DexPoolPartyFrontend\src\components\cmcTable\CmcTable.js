"use client";
import React, { useState, useContext, useEffect, useCallback } from "react";
import { CoinMarketContext } from "../../../context/context";

const CmcTable = () => {
  const { getCoins } = useContext(CoinMarketContext);
  const [coinData, setCoinData] = useState([]);

  const fetchData = useCallback(async () => {
    try {
      const apiResponse = await getCoins();
    //   console.log("API Response:", apiResponse);

      const filterResponse = Array.isArray(apiResponse)
        ? apiResponse.filter((coin) => coin?.cmc_rank <= 10).slice(0, 10)
        : [];

      setCoinData(filterResponse);
    } catch (error) {
      console.error("Error fetching data:", error);
      setCoinData([]); 
    }
  }, [getCoins]);
  
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full text-left text-sm">
        <thead className="bg-gray-100 text-xs uppercase text-gray-700">
          <tr>
            <th className="px-4 py-2">Rank</th>
            <th className="px-4 py-2">Name</th>
            <th className="px-4 py-2">Symbol</th>
            <th className="px-4 py-2">Supply</th>
          </tr>
        </thead>
        <tbody>
          {coinData?.length > 0 ? (
            coinData.map((coin) => (
              <tr key={coin.id} className="border-b hover:bg-gray-50">
                <td className="px-4 py-2">{coin.cmc_rank}</td>
                <td className="px-4 py-2">{coin.name}</td>
                <td className="px-4 py-2">{coin.symbol}</td>
                <td className="px-4 py-2">
                  {Number(coin.circulating_supply).toLocaleString()}
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td className="px-4 py-2" colSpan={4}>
                No data available.
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default CmcTable;
