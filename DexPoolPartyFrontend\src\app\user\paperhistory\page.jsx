"use client"
import React, { useState, useMemo, useEffect } from 'react';
import { apiService } from '@/lib/paperHistoryApi'; 
import { devnetApiService } from '@/lib/devnetApi';
import { handleApiError, handleApiSuccess } from '@/utils/toast';
import FilterSection from '@/components/paperHistory/FilterSection';
import TradeCard from '@/components/paperHistory/TradeCard';
import PaginationControls from '@/components/paperHistory/PaginationControls';
import StatsSection from '@/components/paperHistory/StatsSection';
import LoadingScreen from '@/components/paperHistory/LoadingScreen';
import ErrorScreen from '@/components/paperHistory/ErrorScreen';
import EmptyState from '@/components/paperHistory/EmptyState';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import LoginModel from '@/components/LoginModel';

function App() {
  // State management
  const [trades, setTrades] = useState([]);
  const [devnetTrades, setDevnetTrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [mainnetError, setMainnetError] = useState(null);
  const [devnetError, setDevnetError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('mainnet');
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTrades, setTotalTrades] = useState(0);
  
  // Devnet pagination state
  const [devnetCurrentPage, setDevnetCurrentPage] = useState(1);
  const [devnetTotalPages, setDevnetTotalPages] = useState(1);
  const [devnetTotalTrades, setDevnetTotalTrades] = useState(0);
  
  // Filter and search state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedState, setSelectedState] = useState('all');
  const [selectedProfitFilter, setProfitFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [showFilters, setShowFilters] = useState(false);
  
  const itemsPerPage = 6;

  useEffect(() => {
    fetchTradeHistory(1, true);
    fetchDevnetHistory(1, false);
  }, []);

  // Fetch mainnet paper trade history
  const fetchTradeHistory = async (page = 1, showLoader = true) => {
    try {
      if (showLoader) setLoading(true);
      setMainnetError(null);
      
      const data = await apiService.fetchTradeHistory(page, itemsPerPage);
      
      setTrades(data.history || []);
      setCurrentPage(data.page || 1);
      setTotalPages(data.totalPages || 1);
      setTotalTrades(data.total || 0);
      
    } catch (err) {
      console.error('Error fetching trade history:', err);
      const errorMessage = handleApiError(err, 'Failed to fetch mainnet trade history');
      setMainnetError(errorMessage);
      setTrades([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch devnet transactions
  const fetchDevnetHistory = async (page = 1, showLoader = true) => {
    try {
      if (showLoader) setLoading(true);
      setDevnetError(null);
      
      const data = await devnetApiService.fetchDevnetTransactions(page, itemsPerPage);
      
      setDevnetTrades(data.transactions || []);
      setDevnetCurrentPage(data.page || 1);
      setDevnetTotalPages(data.totalPages || 1);
      setDevnetTotalTrades(data.total || 0);
      
    } catch (err) {
      console.error('Error fetching devnet history:', err);
      const errorMessage = handleApiError(err, 'Failed to fetch devnet transaction history');
      setDevnetError(errorMessage);
      setDevnetTrades([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle page change for mainnet
  const handlePageChange = (newPage) => {
    if (newPage !== currentPage && newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      fetchTradeHistory(newPage, false);
    }
  };

  // Handle page change for devnet
  const handleDevnetPageChange = (newPage) => {
    if (newPage !== devnetCurrentPage && newPage >= 1 && newPage <= devnetTotalPages) {
      setDevnetCurrentPage(newPage);
      fetchDevnetHistory(newPage, false);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    if (activeTab === 'mainnet') {
      fetchTradeHistory(currentPage, false);
    } else {
      fetchDevnetHistory(devnetCurrentPage, false);
    }
  };

  // Filter and sort trades (client-side for current page)
  const filteredAndSortedTrades = useMemo(() => {
    let filtered = trades.filter(trade => {
      const matchesSearch = 
        trade?.token?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade?.token?.symbol?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade?.token?.address?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesState = selectedState === 'all' || trade?.state === selectedState;
      
      let matchesProfit = true;
      if (selectedProfitFilter !== 'all' && trade?.sell) {
        const profit = trade.sell.totalValue - trade.buy.totalValue;
        if (selectedProfitFilter === 'profit' && profit <= 0) matchesProfit = false;
        if (selectedProfitFilter === 'loss' && profit >= 0) matchesProfit = false;
      } else if (selectedProfitFilter !== 'all' && !trade?.sell) {
        matchesProfit = false;
      }
      
      return matchesSearch && matchesState && matchesProfit;
    });

    // Sort trades
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b?.buy?.timestamp || 0).getTime() - new Date(a?.buy?.timestamp || 0).getTime();
        case 'oldest':
          return new Date(a?.buy?.timestamp || 0).getTime() - new Date(b?.buy?.timestamp || 0).getTime();
        case 'highest-value':
          return (b?.buy?.totalValue || 0) - (a?.buy?.totalValue || 0);
        case 'lowest-value':
          return (a?.buy?.totalValue || 0) - (b?.buy?.totalValue || 0);
        case 'most-profitable':
          const profitA = a?.sell ? a.sell.totalValue - a.buy.totalValue : 0;
          const profitB = b?.sell ? b.sell.totalValue - b.buy.totalValue : 0;
          return profitB - profitA;
        default:
          return 0;
      }
    });

    return filtered;
  }, [trades, searchTerm, selectedState, selectedProfitFilter, sortBy]);

  // Filter and sort devnet trades
  const filteredAndSortedDevnetTrades = useMemo(() => {
    let filtered = devnetTrades.filter(trade => {
      const matchesSearch = 
        trade?.token?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade?.token?.symbol?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade?.token?.address?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesState = selectedState === 'all' || trade?.state === selectedState;
      
      let matchesProfit = true;
      if (selectedProfitFilter !== 'all' && trade?.sell) {
        const profit = trade.sell.totalValue - trade.buy.totalValue;
        if (selectedProfitFilter === 'profit' && profit <= 0) matchesProfit = false;
        if (selectedProfitFilter === 'loss' && profit >= 0) matchesProfit = false;
      } else if (selectedProfitFilter !== 'all' && !trade?.sell) {
        matchesProfit = false;
      }
      
      return matchesSearch && matchesState && matchesProfit;
    });

    // Sort devnet trades
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b?.buy?.timestamp || 0).getTime() - new Date(a?.buy?.timestamp || 0).getTime();
        case 'oldest':
          return new Date(a?.buy?.timestamp || 0).getTime() - new Date(b?.buy?.timestamp || 0).getTime();
        case 'highest-value':
          return (b?.buy?.totalValue || 0) - (a?.buy?.totalValue || 0);
        case 'lowest-value':
          return (a?.buy?.totalValue || 0) - (b?.buy?.totalValue || 0);
        case 'most-profitable':
          const profitA = a?.sell ? a.sell.totalValue - a.buy.totalValue : 0;
          const profitB = b?.sell ? b.sell.totalValue - b.buy.totalValue : 0;
          return profitB - profitA;
        default:
          return 0;
      }
    });

    return filtered;
  }, [devnetTrades, searchTerm, selectedState, selectedProfitFilter, sortBy]);

  const activeFiltersCount = [
    selectedState !== 'all',
    selectedProfitFilter !== 'all',
    sortBy !== 'newest'
  ].filter(Boolean).length;

  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedState('all');
    setProfitFilter('all');
    setSortBy('newest');
  };

  // Calculate stats for mainnet
  const totalProfit = trades.reduce((sum, trade) => {
    if (trade?.sell) {
      return sum + (trade.sell.totalValue - trade.buy.totalValue);
    }
    return sum;
  }, 0);

  const completedTrades = trades.filter(t => t?.sell);
  const winRate = completedTrades.length > 0 
    ? completedTrades.filter(t => (t.sell.totalValue - t.buy.totalValue) > 0).length / completedTrades.length * 100
    : 0;

  // Calculate stats for devnet
  const devnetTotalProfit = devnetTrades.reduce((sum, trade) => {
    if (trade?.sell) {
      return sum + (trade.sell.totalValue - trade.buy.totalValue);
    }
    return sum;
  }, 0);

  const devnetCompletedTrades = devnetTrades.filter(t => t?.sell);
  const devnetWinRate = devnetCompletedTrades.length > 0 
    ? devnetCompletedTrades.filter(t => (t.sell.totalValue - t.buy.totalValue) > 0).length / devnetCompletedTrades.length * 100
    : 0;

  return (
    <div className="min-h-screen my-10 bg-[#111115] text-white">
      {/* Header */}
      <FilterSection
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        selectedState={selectedState}
        setSelectedState={setSelectedState}
        selectedProfitFilter={selectedProfitFilter}
        setProfitFilter={setProfitFilter}
        sortBy={sortBy}
        setSortBy={setSortBy}
        showFilters={showFilters}
        setShowFilters={setShowFilters}
        activeFiltersCount={activeFiltersCount}
        clearAllFilters={clearAllFilters}
        refreshing={refreshing}
        handleRefresh={handleRefresh}
        totalProfit={activeTab === 'mainnet' ? totalProfit : devnetTotalProfit}
        winRate={activeTab === 'mainnet' ? winRate : devnetWinRate}
      />

      {/* Main Content */}
      <div className="max-w-6xl mx-auto p-4 md:p-6">
        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList className="grid w-full grid-cols-2 bg-gray-800/60 backdrop-blur-sm border border-gray-700/50">
            <TabsTrigger 
              value="mainnet" 
              className="data-[state=active]:bg-blue-600 text-white data-[state=active]:text-white"
            >
              Mainnet Paper Trading
            </TabsTrigger>
            <TabsTrigger 
              value="devnet" 
              className="data-[state=active]:bg-green-600 text-white data-[state=active]:text-white"
            >
              Devnet Transactions
            </TabsTrigger>
          </TabsList>

          {/* Mainnet Tab */}
          <TabsContent value="mainnet" className="mt-6">
            {/* Results Summary */}
            <div className="mb-6 flex items-center justify-between text-sm text-gray-400">
              <div>
                Showing {filteredAndSortedTrades.length} of {totalTrades} trades
                {searchTerm && ` for "${searchTerm}"`}
              </div>
              {totalPages > 1 && (
                <div className="text-gray-400">
                  Page {currentPage} of {totalPages}
                </div>
              )}
            </div>

            {/* Trading Cards */}
            {filteredAndSortedTrades.length > 0 ? (
              <div className="space-y-6">
                {filteredAndSortedTrades.map((trade) => (
                  <TradeCard key={trade?.tradeId || trade?._id} trade={trade} />
                ))}
              </div>
            ) : (
              <EmptyState onClearFilters={clearAllFilters} isFiltered={true} />
            )}

            {/* Pagination */}
            <PaginationControls 
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />

            {/* Footer Stats */}
            <StatsSection 
              trades={trades}
              totalProfit={totalProfit}
              winRate={winRate}
            />
          </TabsContent>

          {/* Devnet Tab */}
          <TabsContent value="devnet" className="mt-6">
            {/* Results Summary */}
            <div className="mb-6 flex items-center justify-between text-sm text-gray-400">
              <div>
                Showing {filteredAndSortedDevnetTrades.length} of {devnetTotalTrades} devnet transactions
                {searchTerm && ` for "${searchTerm}"`}
              </div>
              {devnetTotalPages > 1 && (
                <div className="text-gray-400">
                  Page {devnetCurrentPage} of {devnetTotalPages}
                </div>
              )}
            </div>

            {/* Devnet Trading Cards */}
            {filteredAndSortedDevnetTrades.length > 0 ? (
              <div className="space-y-6">
                {filteredAndSortedDevnetTrades.map((trade) => (
                  <TradeCard key={trade?._id} trade={trade} isDevnet={true} />
                ))}
              </div>
            ) : (
              <EmptyState onClearFilters={clearAllFilters} isFiltered={true} />
            )}

            {/* Pagination */}
            <PaginationControls 
              currentPage={devnetCurrentPage}
              totalPages={devnetTotalPages}
              onPageChange={handleDevnetPageChange}
            />

            {/* Footer Stats */}
            <StatsSection 
              trades={devnetTrades}
              totalProfit={devnetTotalProfit}
              winRate={devnetWinRate}
              isDevnet={true}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export default App;