'use client';

import React, { useEffect, useState, useCallback, useMemo } from 'react';
import axios from 'axios';
import Image from 'next/image';
import ActivityComments from '@/components/Activity.jsx';
import CommentPopup from '@/components/CommentPopup';
import ReplyModal from '@/components/ReplyModal.jsx';
import CommentItem from '@/components/CommentItem.jsx';
import TokenChart from '@/components/TokenChart';

// Solana Wallet Adapter imports
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { PublicKey, Transaction, LAMPORTS_PER_SOL, VersionedTransaction } from '@solana/web3.js'; // Ensure VersionedTransaction is imported

const SOLANA_RPC_URL = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || 'https://api.devnet.solana.com';
const JUPITER_API_HOST = process.env.NEXT_PUBLIC_JUPITER_API_HOST || 'https://quote-api.jup.ag/v6';
const SOL_MINT_ADDRESS = 'So11111111111111111111111111111111111111112';

const CoinDetailsPage = ({ params }) => {
  const unwrappedParams = typeof params.then === 'function' ? React.use(params) : params;
  const id = unwrappedParams.id;

  const [coinData, setCoinData] = useState(null);
  const [error, setError] = useState(null);
  const [amount, setAmount] = useState('');

  const [showPopup, setShowPopup] = useState(false);
  const [comments, setComments] = useState([]);
  const [commentsLoading, setCommentsLoading] = useState(true);
  const [commentsError, setCommentsError] = useState(null);
  const [showReplyModal, setShowReplyModal] = useState(false);
  const [activeCommentId, setActiveCommentId] = useState(null);
  const [openReplies, setOpenReplies] = useState({});

  const [realtimePrice, setRealtimePrice] = useState(null);
  const [realtimePriceChange, setRealtimePriceChange] = useState(null);

  const { connection } = useConnection();
  const { publicKey, sendTransaction, connected } = useWallet();
  const [inputMint, setInputMint] = useState(new PublicKey(SOL_MINT_ADDRESS));
  const [outputMint, setOutputMint] = useState(null);
  const [slippage, setSlippage] = useState(1);
  const [quoteResponse, setQuoteResponse] = useState(null);
  const [loadingQuote, setLoadingQuote] = useState(false);
  const [loadingSwap, setLoadingSwap] = useState(false);
  const [swapError, setSwapError] = useState(null);
  const [isBuying, setIsBuying] = useState(true);
  const [outputAmount, setOutputAmount] = useState('');

  const [isLoading, setIsLoading] = useState(true);

  // Removed user extraction for page protection; only use for gated actions

  const toggleReplies = (commentId) => {
    setOpenReplies((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  };

  const handleOpenReplyModal = (commentId) => {
    setActiveCommentId(commentId);
    setShowReplyModal(true);
  };

  const fetchComments = useCallback(async () => {
    setCommentsLoading(true);
    setCommentsError(null);

    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_BACKEND_URL}/tokens/${id}/comments`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('authToken')}`,
        },
      });
      setComments(response.data.comments || []);
    } catch (error) {
      console.error("Error fetching comments:", error);
      setComments([]);
      setCommentsError('Failed to load comments. Please try again.');
    } finally {
      setCommentsLoading(false);
    }
  }, [id]);

  const handleAddComment = async (commentData) => {
    console.log('Attempting to post comment to backend:', commentData);

    const authToken = localStorage.getItem('authToken');
    if (!authToken) {
      setCommentsError("Please log in to post a comment.");
      return;
    }

    const payload = {
      tokenId: id,
      content: commentData.text,
      imageUrl: commentData.imageUrl || '',
      parentId: null
    };
    console.log(payload);
    console.log(authToken);

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/comments/add-comment/${id}`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${authToken}`
          }
        }
      );
      console.log('Comment posted successfully:', response.data);
      setShowPopup(false);
      setCommentsError(null);
      fetchComments();
    } catch (error) {
      console.error('Error posting comment:', error);
      setCommentsError('Failed to post comment. Please try again.');
    }
  };

  const handleReplySubmit = async ({ text: replyText, image }) => {
    if (!replyText?.trim()) {
      setCommentsError("Reply cannot be empty.");
      return;
    }
    const authToken = localStorage.getItem("authToken");
    if (!authToken) {
      setCommentsError("Please log in to post a reply.");
      return;
    }

    try {
      console.log(authToken);

      const payload = {
        content: replyText.trim(),
      };

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/comments/add-reply/${activeCommentId}`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      console.log("Reply posted:", response.data);
      setShowReplyModal(false);
      setActiveCommentId(null);
      setCommentsError(null);
      await fetchComments();
    } catch (error) {
      console.error("Error posting reply:", error);
      setCommentsError("Failed to post reply. Please try again.");
    }
  };

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/trending');
      const data = await response.json();
      if (data && data.data) {
        setCoinData(data.data);
      } else {
        setCoinData([]);
      }
    } catch (error) {
      console.error('Error fetching Bitquery tokens:', error);
      setCoinData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const currentCoin = useMemo(() => {
    if (!coinData) return null;
    return coinData.find(coin => coin.id === id);
  }, [coinData, id]);

  useEffect(() => {
    if (id) {
      try {
        const coinPublicKey = new PublicKey(id);
        if (coinPublicKey.toBase58() !== SOL_MINT_ADDRESS) {
          setOutputMint(coinPublicKey);
          setIsBuying(true);
          setInputMint(new PublicKey(SOL_MINT_ADDRESS));
        } else {
          setOutputMint(null);
          setIsBuying(true);
          setInputMint(new PublicKey(SOL_MINT_ADDRESS));
        }
      } catch (e) {
        console.error("Invalid token ID as mint address from URL:", e);
        setError("Invalid token address provided for swap in URL.");
        setOutputMint(null);
      }
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      fetchComments();
    }
  }, [fetchComments, id]);

  useEffect(() => {
    let intervalId;
    const fetchRealtimePrice = async () => {
      if (!id) return;

      try {
        const response = await axios.get(`/api/trending`);
        const trendingTokens = response.data.data;
        const targetToken = trendingTokens.find(token => token.id.toString() === id);

        if (targetToken && targetToken.quote && targetToken.quote.USD && targetToken.quote.USD.price) {
          const newPrice = targetToken.quote.USD.price;
          const newPriceChange = targetToken.quote.USD.percent_change_24h;

          setRealtimePrice(newPrice);
          setRealtimePriceChange(newPriceChange);

          setCoinData(prevData => {
            if (!prevData) return null;
            return prevData.map(coin =>
              coin.id.toString() === id
                ? {
                  ...coin,
                  quote: {
                    ...coin.quote,
                    USD: {
                      ...coin.quote.USD,
                      price: newPrice,
                      percent_change_24h: newPriceChange,
                    },
                  },
                }
                : coin
            );
          });
        } else {
          console.warn(`Real-time price data not found for token ID: ${id}`);
        }
      } catch (err) {
        console.error("Error fetching real-time price:", err);
      }
    };

    fetchRealtimePrice();
    intervalId = setInterval(fetchRealtimePrice, 10000);

    return () => clearInterval(intervalId);
  }, [id]);

  const getQuote = useCallback(async () => {
    console.log('--- getQuote called ---');
    console.log({
        isBuying,
        inputMint: inputMint?.toBase58(),
        outputMint: outputMint?.toBase58(),
        amount,
        publicKey: publicKey?.toBase58(),
        currentCoinSymbol: currentCoin?.symbol,
        idFromParams: id
    });

    if (!publicKey) {
        setSwapError("Connect wallet to get quote.");
        setLoadingQuote(false);
        setQuoteResponse(null);
        setOutputAmount('');
        return;
    }

    if (!inputMint || !outputMint || inputMint.toBase58() === outputMint.toBase58()) {
        setQuoteResponse(null);
        setOutputAmount('');
        if (inputMint?.toBase58() === outputMint?.toBase58()) {
            setSwapError("Input and output tokens cannot be the same.");
        } else {
            setSwapError("Select valid input/output tokens.");
        }
        setLoadingQuote(false);
        return;
    }

    // Clear previous errors
    setSwapError(null);
    setLoadingQuote(true);

    const parsedAmount = parseFloat(amount);

    // Validate amount
    if (isNaN(parsedAmount) || parsedAmount <= 0) {
      setSwapError("Please enter a valid amount.");
      setLoadingQuote(false);
      setQuoteResponse(null);
      setOutputAmount('');
      return;
    }

    let inputAmountInSmallestUnit = 0;
    const solDecimals = 9;

    if (isBuying) {
        // Buying: SOL -> Token
        inputAmountInSmallestUnit = Math.floor(parsedAmount * LAMPORTS_PER_SOL);
    } else {
        // Selling: Token -> SOL
        // Ensure currentCoin and its decimals are available. Fallback to SOL decimals.
        const tokenDecimals = currentCoin?.decimals !== undefined ? currentCoin.decimals : solDecimals;
        inputAmountInSmallestUnit = Math.floor(parsedAmount * Math.pow(10, tokenDecimals));
    }

    try {
      const quoteUrl = `${JUPITER_API_HOST}/quote`;
      const params = {
        inputMint: inputMint.toBase58(),
        outputMint: outputMint.toBase58(),
        amount: String(inputAmountInSmallestUnit),
        slippageBps: slippage * 100,
        // Add `onlyDirectRoutes: true` if you want to strictly limit to direct pairs,
        // but this might reduce quote availability for illiquid tokens.
        // `autoPriorityFee: true` is also an option for Jupiter v6.
      };

      console.log('Jupiter API call:', { quoteUrl, params });

      const { data } = await axios.get(quoteUrl, { params });

      console.log('Jupiter API response:', data);

      if (data && data.outAmount) {
        setQuoteResponse(data);
        const actualOutputTokenDecimals = isBuying ? (currentCoin?.decimals !== undefined ? currentCoin.decimals : solDecimals) : solDecimals;
        const estimatedOutput = parseFloat(data.outAmount) / Math.pow(10, actualOutputTokenDecimals);
        setOutputAmount(estimatedOutput.toFixed(6));
        console.log("Jupiter Quote success:", data);
      } else {
        setQuoteResponse(null);
        setOutputAmount('');
        setSwapError("No suitable quotes found for this swap.");
      }
    } catch (err) {
      console.error("Error fetching Jupiter quote:", err);
      
      let errorMessage = "Failed to fetch swap quotes. Please try again.";
      
      if (err.response?.data) {
        // Jupiter API returns errors with 'error' or 'message' property
        if (err.response.data.error) {
          errorMessage = `Jupiter API Error: ${err.response.data.error}`;
        } else if (err.response.data.message) {
          errorMessage = `Error: ${err.response.data.message}`;
        }
        // Specific check for 'No routes found' error from Jupiter
        if (err.response.data.error === "No routes found" || err.response.data.message?.includes("No routes found")) {
            errorMessage = "No suitable quotes found for this swap. The token may have very low liquidity for the requested amount, or there might be no direct trading pairs available on integrated DEXs.";
        }
      } else if (err.message) {
        errorMessage = `Network Error: ${err.message}`;
      }
      
      setSwapError(errorMessage);
      setQuoteResponse(null);
      setOutputAmount('');
    } finally {
      setLoadingQuote(false);
    }
  }, [inputMint, outputMint, amount, slippage, publicKey, isBuying, currentCoin?.decimals, JUPITER_API_HOST, id]);

  // Debounced quote fetching
  useEffect(() => {
    if (!amount || amount === '' || parseFloat(amount) <= 0) {
      setQuoteResponse(null);
      setOutputAmount('');
      setSwapError(null);
      return;
    }

    // Do not fetch quote if currentCoin is SOL and buying/selling SOL for SOL
    const isCurrentCoinSOL = id === SOL_MINT_ADDRESS; // Define here for immediate use in effect
    if (isCurrentCoinSOL && (isBuying || !isBuying)) {
        setSwapError("Cannot swap SOL for SOL.");
        setQuoteResponse(null);
        setOutputAmount('');
        return;
    }


    const handler = setTimeout(() => {
      getQuote();
    }, 800); // Increased debounce time

    return () => clearTimeout(handler);
  }, [amount, inputMint, outputMint, slippage, isBuying, getQuote, id]); // Updated dependencies

  const performSwap = async () => {
    if (!publicKey || !connected || !quoteResponse || !connection) {
      setSwapError("Please connect your wallet and get a quote first.");
      return;
    }

    setLoadingSwap(true);
    setSwapError(null);

    try {
      console.log('Performing swap with quote:', quoteResponse);

      const swapRequestBody = {
        quoteResponse,
        userPublicKey: publicKey.toBase58(),
        wrapAndUnwrapSol: true, // Auto wrap/unwrap SOL
        dynamicComputeUnitLimit: true, // Let Jupiter determine CUs
        prioritizationFeeLamports: 'auto' // Let Jupiter determine prioritization fee
      };

      console.log('Swap request body:', swapRequestBody);

      const { data } = await axios.post(`${JUPITER_API_HOST}/swap`, swapRequestBody, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('Swap response:', data);

      const swapTransactionBuf = Buffer.from(data.swapTransaction, 'base64');
      let transaction;

      // --- REVISED CRITICAL FIX START ---
      // Directly attempt VersionedTransaction.deserialize first.
      // If that fails, it's either an invalid transaction or genuinely a legacy one.
      try {
        transaction = VersionedTransaction.deserialize(swapTransactionBuf);
        console.log('Deserialized transaction as VersionedTransaction.');
      } catch (versionedError) {
        console.warn('Failed to deserialize as VersionedTransaction, attempting legacy Transaction:', versionedError);
        // Fallback to legacy Transaction.
        // This will often throw the "Versioned messages must be deserialized..." error
        // if the transaction *is* versioned and this fallback is hit unnecessarily.
        // The goal is to catch that specific error if it occurs here.
        try {
          transaction = Transaction.from(swapTransactionBuf);
          console.log('Deserialized transaction as legacy Transaction.');
        } catch (legacyError) {
          console.error("Completely failed to deserialize transaction. Neither VersionedTransaction nor legacy Transaction worked:", legacyError);
          // If legacy also fails with the *same specific error*, then the original attempt failed
          // for some other reason, but the transaction *is* versioned.
          if (legacyError.message && legacyError.message.includes("Versioned messages must be deserialized with VersionedMessage.deserialize()")) {
              throw new Error("Jupiter returned a versioned transaction, but deserialization failed unexpectedly after the first attempt. Ensure @solana/web3.js is up to date.");
          }
          throw new Error(`Failed to deserialize transaction from Jupiter: ${legacyError.message}`);
        }
      }
      // --- REVISED CRITICAL FIX END ---

      // Note: Solana Wallet Adapter's `sendTransaction` can handle missing
      // recentBlockhash and feePayer for `VersionedTransaction` by fetching them.
      // Explicitly setting them might not be strictly necessary if the wallet adapter
      // handles it, but ensures clarity. Jupiter's /swap endpoint usually returns
      // a fully formed transaction ready for signing.

      // For VersionedTransactions, `recentBlockhash` and `lastValidBlockHeight`
      // are part of the `messageV0` object, not directly on the `VersionedTransaction` instance.
      // The wallet adapter's `sendTransaction` takes care of this.

      console.log('Sending transaction...', transaction);
      // The wallet adapter's `sendTransaction` can accept both `Transaction` and `VersionedTransaction`
      const signature = await sendTransaction(transaction, connection, {
        skipPreflight: false,
        preflightCommitment: 'confirmed' // 'confirmed' is a good balance for production
      });
      
      console.log('Swap transaction sent:', signature);

      // Wait for confirmation
      // For versioned transactions, the `lastValidBlockHeight` is part of the transaction's message.
      // The `connection.confirmTransaction` function will use this if provided,
      // or fetch the latest blockhash if needed.
      const confirmation = await connection.confirmTransaction(signature, 'confirmed');

      if (confirmation.value.err) {
        throw new Error(`Transaction failed: ${confirmation.value.err.toString()}`);
      }

      alert('Swap successful! Transaction ID: ' + signature);
      
      // Reset form
      setAmount('');
      setOutputAmount('');
      setQuoteResponse(null);

    } catch (err) {
      console.error("Error performing swap:", err);
      
      let errorMessage = 'Failed to perform swap. Please try again.';
      
      if (err.message.includes('User rejected')) {
        errorMessage = 'Transaction rejected by user.';
      } else if (err.response?.data?.error) {
        errorMessage = `Swap Error: ${err.response.data.error}`;
      } else if (err.message) {
        errorMessage = `Error: ${err.message}`;
      }
      
      setSwapError(errorMessage);
    } finally {
      setLoadingSwap(false);
    }
  };

  const priceImpact = useMemo(() => {
    if (!quoteResponse || !quoteResponse.priceImpactPct) return null;
    return parseFloat(quoteResponse.priceImpactPct) * 100;
  }, [quoteResponse]);

  // Helper to update like in nested comments/replies
  function updateCommentLikeInTree(comments, commentId, userId, liked) {
    return comments.map(comment => {
      if (comment._id === commentId) {
        return {
          ...comment,
          likedBy: liked
            ? (comment.likedBy || []).filter(uid => uid !== userId)
            : [...(comment.likedBy || []), userId],
          liked: !liked
        };
      }
      if (comment.replies && comment.replies.length > 0) {
        return {
          ...comment,
          replies: updateCommentLikeInTree(comment.replies, commentId, userId, liked)
        };
      }
      return comment;
    });
  }

  // Add handleLike function
  const handleLike = async (commentId, liked) => {
    if (!publicKey) { // Only require login for liking
      setCommentsError("Please log in to like/unlike comments.");
      return;
    }
    try {
      const url = `${process.env.NEXT_PUBLIC_BACKEND_URL}/comments/${liked ? 'unlike' : 'like'}/${commentId}`;
      const res = await fetch(url, {
        method: 'POST',
        headers: { Authorization: `Bearer ${localStorage.getItem('authToken')}` },
      });
      if (!res.ok) throw new Error('Failed to like/unlike');
      setComments(prevComments =>
        updateCommentLikeInTree(prevComments, commentId, publicKey.toBase58(), liked)
      );
    } catch (err) {
      console.error('Error liking/unliking comment:', err);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <div className="loader"></div>
      </div>
    );
  }

  let selectedTokenForChart = null;
  if (currentCoin) {
    selectedTokenForChart = {
      id: currentCoin.id.toString(),
      name: currentCoin.name,
      symbol: currentCoin.symbol,
      mintAddress: id,
      logo: currentCoin.logo,
      quote: {
        USD: {
          price: realtimePrice !== null ? realtimePrice : currentCoin.quote.USD.price,
          volume_24h: currentCoin.quote.USD.volume_24h,
          percent_change_24h: realtimePriceChange !== null ? realtimePriceChange : currentCoin.quote.USD.percent_change_24h,
        },
      },
    };
  }

  const isCurrentCoinSOL = id === SOL_MINT_ADDRESS;
  const displayTokenSymbol = currentCoin?.symbol || 'Token';


  return (
    <div className="flex flex-col items-center w-full mt-[50px] md:px-[20px] py-[30px] md:py-[90px] min-h-screen text-white">
      <div className="w-full md:pl-[50px] mt-2">
        <div className="mb-1 mt-2 flex w-full text-sm md:text-large flex-wrap items-center gap-2 md:gap-3 gap-y-[8px] md:gap-y-[14px] font-bold text-white">
          <div>{currentCoin?.name || 'Unknown Token'} ({displayTokenSymbol})</div>
          <div className='bg-yellow-200 text-black rounded px-2 py-0.5 text-xs md:text-base font-semibold opacity-80'>D3psZ1</div>
          <div className="text-xs md:text-base text-gray-400 font-normal">about 1 hour ago</div>
          {currentCoin && (
            <div className='text-green-200 text-xs md:text-base font-normal'>market cap: ${currentCoin?.quote?.USD?.market_cap?.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}</div>
          )}
          <div className="text-xs md:text-base text-gray-400 font-normal">replies: {comments.length}</div>
        </div>
        <div className="mt-6 md:mt-10 grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8">
          <div className="lg:col-span-2">
            {selectedTokenForChart?.mintAddress && (
              <TokenChart initialSelectedToken={selectedTokenForChart?.mintAddress} />
            )}
            <div className="mt-6 md:mt-10">
              <div className="mb-4 flex items-center flex-wrap gap-2">
                <button className="flex items-center gap-1 rounded-md bg-[#303947] px-3 py-1.5 text-xs md:text-sm text-white shadow-sm transition duration-200 hover:bg-gray-600 focus:outline-none">
                  sort: time (oldest)
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-up">
                    <path d="m5 12 7-7 7 7"></path>
                    <path d="M12 19V5"></path>
                  </svg>
                </button>
                <div
                  className="flex cursor-pointer items-center gap-1 rounded-md bg-green-400 px-3 py-1.5 text-xs md:text-sm text-black shadow-sm transition duration-200 hover:bg-green-400/80 font-semibold"
                  onClick={() => setShowPopup(true)}
                >
                  Post a Comment
                </div>
                {showPopup && (
                  <CommentPopup
                    onClose={() => setShowPopup(false)}
                    onSubmit={handleAddComment}
                  />
                )}
              </div>
              <div className="bg-[#23242c] rounded-xl p-6 shadow-lg border border-[#353545]">
                {commentsLoading ? (
                  <div className="flex items-center justify-center w-full mt-4">
                    <div className="loader"></div>
                  </div>
                ) : commentsError ? (
                  <div className="text-gray-500 mt-4 text-center p-4 rounded-md bg-gray-800 ">{commentsError}</div>
                ) : comments.length === 0 ? (
                  <div className="text-gray-400 mt-4 text-center p-4 rounded-md bg-gray-800 border border-gray-700">No comments yet. Be the first to add one!</div>
                ) : (
                  <div className="mt-4 space-y-4">
                    {comments.map((comment) => (
                      <CommentItem
                        key={comment._id}
                        comment={{
                          ...comment,
                          liked: comment.likedBy && publicKey ? comment.likedBy.includes(publicKey.toBase58()) : false,
                        }}
                        openReplies={openReplies}
                        toggleReplies={toggleReplies}
                        handleOpenReplyModal={handleOpenReplyModal}
                        handleLike={handleLike}
                        user={null} // Pass null as user is not available for this page
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
            {/* <ActivityComments /> */}
          </div>

          <div className="lg:col-span-1">
            <div className="grid h-fit w-full gap-3 md:gap-4">
              <div className="grid gap-3 md:gap-4 rounded-t-lg border border-none bg-[#2e303a] p-3 md:p-4 text-gray-400 transition-all duration-300 ease-in-out md:rounded-lg">
                <div className="mb-1 flex items-center justify-between">
                  <div className="flex flex-1 gap-2">
                    <button
                      className={`flex-grow rounded px-2 md:px-3 py-1 md:py-2 text-center text-sm md:text-base font-normal transition-colors ${isBuying ? 'bg-green-400 text-black' : 'bg-gray-800 text-white hover:bg-gray-700'} ${isCurrentCoinSOL ? 'opacity-50 cursor-not-allowed' : ''}`}
                      onClick={() => {
                        if (isCurrentCoinSOL) {
                          setSwapError("Cannot buy SOL for SOL.");
                          return;
                        }
                        setIsBuying(true);
                        setInputMint(new PublicKey(SOL_MINT_ADDRESS));
                        setOutputMint(new PublicKey(id));
                        setSwapError(null);
                        setQuoteResponse(null);
                        setOutputAmount('');
                      }}
                      disabled={isCurrentCoinSOL}
                    >
                      buy
                    </button>
                    <button
                      className={`flex-grow rounded px-2 md:px-3 py-1 md:py-2 text-center text-sm md:text-base font-normal transition-colors ${!isBuying ? 'bg-green-400 text-black' : 'bg-gray-800 text-white hover:bg-gray-700'} ${isCurrentCoinSOL ? 'opacity-50 cursor-not-allowed' : ''}`}
                      onClick={() => {
                        if (isCurrentCoinSOL) {
                          setSwapError("Cannot sell SOL for SOL.");
                          return;
                        }
                        setIsBuying(false);
                        setInputMint(new PublicKey(id));
                        setOutputMint(new PublicKey(SOL_MINT_ADDRESS));
                        setSwapError(null);
                        setQuoteResponse(null);
                        setOutputAmount('');
                      }}
                      disabled={isCurrentCoinSOL}
                    >
                      sell
                    </button>
                  </div>
                </div>
                
                <div className="flex w-full justify-between flex-wrap gap-2">
                  <button className="rounded px-2 py-1 bg-gray-300 text-black font-bold hover:bg-gray-800 hover:text-gray-300 text-sm md:text-base">
                    {isBuying ? `Pay SOL` : `Sell ${displayTokenSymbol}`}
                  </button>
                  <button
                    className="rounded bg-background px-2 py-1 text-black font-bold hover:bg-gray-800 hover:text-gray-300 text-sm md:text-base"
                    type="button"
                    onClick={() => setSlippage(slippage === 1 ? 0.5 : slippage === 0.5 ? 2 : 1)}
                  >
                    Slippage: {slippage}%
                  </button>
                </div>
                
                <div className="flex flex-col">
                  <div className="relative flex items-center rounded-md bg-[#2e303a] border border-gray-600">
                    <input
                      id="amount"
                      value={amount}
                      onChange={(e) => {
                        const value = e.target.value;
                        // Allow only numbers and decimal points
                        if (value === '' || /^\d*\.?\d*$/.test(value)) {
                          setAmount(value);
                        }
                      }}
                      placeholder="0.00"
                      type="text"
                      inputMode="decimal"
                      className="flex h-10 w-full rounded-md border-0 bg-transparent px-3 py-2 text-sm text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400"
                    />
                    <div className="absolute right-2 ml-2 flex items-center">
                      <span className="mr-2 text-white text-sm md:text-base font-medium">
                        {isBuying ? 'SOL' : displayTokenSymbol}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mt-2 flex gap-1 rounded-lg bg-[#2e303a] p-1 flex-wrap">
                    <button 
                      className="rounded bg-gray-600 px-2 py-1 font-bold text-white hover:bg-gray-500 text-xs" 
                      onClick={() => {
                        setAmount('');
                        setQuoteResponse(null);
                        setOutputAmount('');
                      }}
                    >
                      reset
                    </button>
                    <button 
                      className="rounded bg-gray-600 px-2 py-1 font-bold text-white hover:bg-gray-500 text-xs" 
                      onClick={() => setAmount('0.1')}
                    >
                      0.1 {isBuying ? 'SOL' : displayTokenSymbol}
                    </button>
                    <button 
                      className="rounded bg-gray-600 px-2 py-1 font-bold text-white hover:bg-gray-500 text-xs" 
                      onClick={() => setAmount('0.5')}
                    >
                      0.5 {isBuying ? 'SOL' : displayTokenSymbol}
                    </button>
                    <button 
                      className="rounded bg-gray-600 px-2 py-1 font-bold text-white hover:bg-gray-500 text-xs" 
                      onClick={() => setAmount('1')}
                    >
                      1 {isBuying ? 'SOL' : displayTokenSymbol}
                    </button>
                    {/* Max button functionality (requires fetching user balance) */}
                    {/* <button className="rounded bg-gray-600 px-2 py-1 font-bold text-white hover:bg-gray-500 text-xs">max</button> */}
                  </div>
                </div>

                {loadingQuote && (
                  <div className="text-center text-blue-400 bg-blue-900/20 p-2 rounded">
                    Fetching best quote...
                  </div>
                )}
                
                {swapError && (
                  <div className="text-red-400 text-center text-sm bg-red-900/20 p-2 rounded">
                    {swapError}
                  </div>
                )}

                {quoteResponse && outputAmount && (
                  <div className="mt-2 text-sm text-gray-300 bg-gray-800/50 p-3 rounded">
                    <p className="font-medium">
                      You {isBuying ? 'receive' : 'pay'}: <span className="text-green-400">{outputAmount} {isBuying ? displayTokenSymbol : 'SOL'}</span>
                    </p>
                    {priceImpact !== null && (
                      <p>
                        Price Impact: <span className={priceImpact > 5 ? 'text-red-400' : priceImpact > 1 ? 'text-yellow-400' : 'text-green-400'}>
                          {priceImpact.toFixed(2)}%
                        </span>
                      </p>
                    )}
                  </div>
                )}

                <div className="flex flex-col gap-2">
                  {!connected ? (
                    <WalletMultiButton className="inline-flex items-center justify-center gap-2 transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 shadow h-8 md:h-10 px-3 md:px-4 w-full rounded-md py-2 md:py-3 text-sm md:text-base font-normal bg-green-700 text-primary-foreground hover:bg-green-500">
                      Connect Wallet to Trade
                    </WalletMultiButton>
                  ) : (
                    <button
                      onClick={performSwap}
                      disabled={!quoteResponse || loadingSwap || loadingQuote || isCurrentCoinSOL}
                      className="inline-flex items-center justify-center gap-2 transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 shadow h-8 md:h-10 px-3 md:px-4 w-full rounded-md py-2 md:py-3 text-sm md:text-base font-normal bg-green-700 text-primary-foreground hover:bg-green-500"
                    >
                      {loadingSwap ? 'Swapping...' : `Swap ${isBuying ? 'SOL for ' + displayTokenSymbol : displayTokenSymbol + ' for SOL'}`}
                    </button>
                  )}
                </div>
              </div>

              <div className="grid w-full gap-3 md:gap-4 rounded-lg border border-none bg-transparent text-gray-400">
                <div className="h-fit items-start gap-2 flex flex-col md:flex-row">
                  {currentCoin?.logo && (
                    <Image
                      alt={`${currentCoin.name} logo`}
                      loading="lazy"
                      width="256"
                      height="256"
                      decoding="async"
                      className="w-full md:w-32 cursor-pointer object-contain"
                      src={currentCoin.logo}
                      style={{ color: 'transparent' }}
                    />
                  )}
                  <div>
                    <div className="text-sm font-bold">{currentCoin?.name || 'Unknown Token'} ({displayTokenSymbol})</div>
                    <div className="break-anywhere break-words text-xs text-gray-400">
                      You found your Golden Ticket. Out of a million rugs, a million scams, a million charts you could've clicked, you somehow stumbled into this one. You got lucky when you were trenching late at night, half awake, chasing the next pump. But just like Charlie, finding the ticket was only step one. Now comes the real test. You have to hold through the noise, the fear, the dips that are designed to make you quit. You already beat the odds once. Now you have to out-hold, out-believe, and outlast everyone else if you want your small buy to turn into your golden ticket to generational wealth.
                    </div>
                  </div>
                </div>

                <div className="grid gap-3 md:gap-4">
                  <div>
                    <div className="flex items-center text-xs md:text-sm text-gray-400">
                      <span>bonding curve progress: 100%</span>
                      <button data-state="closed" className="ml-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-info" aria-label="information">
                          <circle cx="12" cy="12" r="10"></circle>
                          <path d="M12 16v-4"></path>
                          <path d="M12 8h.01"></path>
                        </svg>
                      </button>
                    </div>
                    <div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100" className="relative h-3 md:h-4 overflow-hidden rounded-full dark:bg-slate-800 mt-1 md:mt-2 w-full bg-gray"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showReplyModal && (
        <ReplyModal
          visible={showReplyModal}
          onClose={() => setShowReplyModal(false)}
          onSubmit={handleReplySubmit}
          commentId={activeCommentId}
        />
      )}
    </div>
  );
};

export default CoinDetailsPage;