const express = require('express');
const router = express.Router();
const tradeController = require('../controllers/tradeController');

/**
 * @swagger
 * tags:
 *   name: Trades
 *   description: Trade management API
 */

/**
 * @swagger
 * /api/trades:
 *   get:
 *     summary: Get all trades
 *     tags: [Trades]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of trades per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, completed, cancelled]
 *         description: Filter by trade status
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [buy, sell]
 *         description: Filter by trade type
 *     responses:
 *       200:
 *         description: List of all trades
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Trade'
 *   post:
 *     summary: Create a new trade
 *     tags: [Trades]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tokenId
 *               - type
 *               - amount
 *               - price
 *             properties:
 *               tokenId:
 *                 type: string
 *                 description: ID of the token to trade
 *               type:
 *                 type: string
 *                 enum: [buy, sell]
 *                 description: Type of trade
 *               amount:
 *                 type: number
 *                 description: Amount of tokens to trade
 *               price:
 *                 type: number
 *                 description: Price per token
 *     responses:
 *       201:
 *         description: Trade created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Trade'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid input
 */

/**
 * @swagger
 * /api/trades/{id}:
 *   get:
 *     summary: Get trade by ID
 *     tags: [Trades]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Trade ID
 *     responses:
 *       200:
 *         description: Trade details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Trade'
 *       404:
 *         description: Trade not found
 * 
 *   patch:
 *     summary: Update trade status
 *     tags: [Trades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Trade ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [pending, completed, cancelled]
 *                 description: New trade status
 *     responses:
 *       200:
 *         description: Trade updated successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Trade not found
 */

/**
 * @swagger
 * /api/trades/token/{tokenId}:
 *   get:
 *     summary: Get trades by token ID
 *     tags: [Trades]
 *     parameters:
 *       - in: path
 *         name: tokenId
 *         required: true
 *         schema:
 *           type: string
 *         description: Token ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of trades per page
 *     responses:
 *       200:
 *         description: List of trades for the token
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Trade'
 *       404:
 *         description: Token not found
 */

/**
 * @swagger
 * /api/trades/user/{userId}:
 *   get:
 *     summary: Get trades by user ID
 *     tags: [Trades]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of trades per page
 *     responses:
 *       200:
 *         description: List of user's trades
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Trade'
 *       404:
 *         description: User not found
 */

/**
 * @swagger
 * /api/trades/filter/custom:
 *   get:
 *     summary: Filter trades with custom criteria
 *     tags: [Trades]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: Filter by user ID
 *       - in: query
 *         name: tokenId
 *         schema:
 *           type: string
 *         description: Filter by token ID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [buy, sell]
 *         description: Filter by trade type
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, completed, cancelled]
 *         description: Filter by trade status
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering
 *     responses:
 *       200:
 *         description: Filtered trades
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Trade'
 */

/**
 * @swagger
 * /api/trades/testnet/tokens:
 *   get:
 *     summary: Get testnet tokens
 *     tags: [Trades]
 *     responses:
 *       200:
 *         description: List of testnet tokens
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Token'
 */

/**
 * @swagger
 * /api/trades/testnet/transaction:
 *   post:
 *     summary: Create testnet transaction
 *     tags: [Trades]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tokenId
 *               - type
 *               - amount
 *               - price
 *             properties:
 *               tokenId:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [buy, sell]
 *               amount:
 *                 type: number
 *               price:
 *                 type: number
 *     responses:
 *       201:
 *         description: Testnet transaction created
 *       401:
 *         description: Unauthorized
 */

/**
 * @swagger
 * /api/trades/testnet/transactions:
 *   get:
 *     summary: Get testnet transactions
 *     tags: [Trades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of transactions per page
 *     responses:
 *       200:
 *         description: List of testnet transactions
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Trade'
 *       401:
 *         description: Unauthorized
 */

router.get('/', tradeController.getAllTrades);
router.get('/token/:tokenId', tradeController.getTradesByToken);
router.get('/user/:userId', tradeController.getTradesByUser);
router.post('/', tradeController.createTrade);
router.patch('/:id', tradeController.updateTradeStatus);
router.get('/filter/custom', tradeController.filterTrades);

module.exports = router;
