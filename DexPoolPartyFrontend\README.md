# Dexpoolparty Testnet DEX Demo

## Overview
This project now includes a fully functional **testnet DEX demo** page that integrates with the real Solana devnet/testnet using Phantom wallet. It allows users to:
- Connect their Phantom wallet (devnet/testnet/mainnet)
- View real SOL balances on the selected network
- Airdrop free SOL on devnet/testnet
- Perform demo buy/sell actions that send real transactions (safe, no real SOL lost)
- Experience a true DEX UI/UX with TradingView chart, transaction history, and wallet integration

## New Features

### Devnet Token Market
- **Browse 15+ Devnet Tokens:** View a curated list of development tokens including Solana Dev, Phantom Coin, Jupiter Token, and more
- **Search & Filter:** Find tokens by name or symbol, sort by price, volume, market cap, or 24h change
- **One-Click Trading:** Click any token to open the trading interface on the testnet page
- **Real Blockchain Integration:** All trades use actual Solana devnet/testnet transactions

### Testnet DEX Trading
- **Phantom Wallet Integration:**
  - Detects and connects to Phantom wallet
  - Shows wallet address and real SOL balance
  - Network selector (devnet, testnet, mainnet)
- **Airdrop:**
  - Get free SOL on devnet/testnet with one click
- **Buy/Sell Demo:**
  - Sends a real transaction (transfer to self) on the selected network
  - No mainnet SOL or tokens are ever used or at risk
- **TradingView Chart:**
  - Embedded for a professional DEX look
- **Modern UI/UX:**
  - Responsive, Jupiter-style buy/sell panel
  - Transaction history and error handling

## Setup Instructions

1. **Install dependencies:**
   ```bash
   npm install
   npm install @solana/web3.js
   ```

2. **Run the development server:**
   ```bash
   npm run dev
   ```

3. **Access the devnet token market:**
   - Go to `/devnet` to browse all available devnet tokens
   - Click on any token to open the trading interface

4. **Access the testnet DEX page directly:**
   - Go to `/testnet/[id]` (e.g., `/testnet/devnet-1` for Solana Dev token)
   - You can use any of the devnet token IDs or the original testnet tokens

5. **Connect Phantom wallet:**
   - Make sure you have the Phantom extension installed
   - Switch Phantom to devnet or testnet (recommended: devnet)
   - Click "Connect Phantom Wallet" on the page

6. **Airdrop SOL:**
   - Click "Airdrop 1 SOL" to get free testnet SOL

7. **Buy/Sell Demo:**
   - Enter an amount and click Buy/Sell
   - A real transaction (transfer to self) is sent on devnet/testnet
   - Your balance updates accordingly

## Available Devnet Tokens

The devnet market includes 15 tokens:
- **Solana Dev (SOLDEV)** - Development token for Solana ecosystem
- **Phantom Coin (PHANTOM)** - Native token for Phantom wallet ecosystem
- **Jupiter Token (JUP)** - Aggregator token for DEX trading
- **Raydium Dev (RAYDEV)** - Development version of Raydium DEX
- **Serum Test (SRM)** - Testnet version of Serum DEX
- **Orca Dev (ORCA)** - Development token for Orca DEX
- **Mango Markets (MNGO)** - Testnet token for Mango Markets
- **Saber Dev (SBR)** - Development token for Saber protocol
- **Anchor Protocol (ANC)** - Testnet token for Anchor lending
- **Marinade Dev (MNDE)** - Development token for Marinade Finance
- **Lido Dev (LDO)** - Development token for Lido staking
- **Step Finance (STEP)** - Testnet token for Step Finance
- **Bonk Dev (BONK)** - Development version of Bonk meme token
- **Samoyed Dev (SAMO)** - Development token for Samoyedcoin
- **Dogwifhat Dev (WIF)** - Development token for Dogwifhat

## Notes
- **No mainnet funds are ever at risk.**
- All blockchain actions are on devnet/testnet only.
- You can expand this to real token minting, devnet swaps, or more advanced features as needed.

## File Changes
- `src/app/devnet/page.jsx`: New devnet token market page with 15 tokens
- `src/app/testnet/[id]/page.jsx`: Updated to handle devnet token IDs
- `src/components/Sidebar.js`: Added devnet navigation link
- `package.json`: Now includes `@solana/web3.js`

## Further Improvements
- Add real devnet token minting and transfer logic
- Integrate with a devnet/testnet swap program for more realistic swaps
- Fetch and display real transaction history from the blockchain
- Add more tokens and categories to the devnet market

---

**Enjoy your safe, real Solana testnet DEX experience with the new devnet token market!**

# Dexpoolparty Frontend

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.js`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Authentication Flow (Login & Signup)

### UI Changes
- The Navbar now has both **Login** and **Sign Up** buttons.
- Clicking **Login** opens the modal in login mode; **Sign Up** opens it in signup mode.
- The modal allows toggling between login and signup forms.

### Signup Flow
1. User clicks **Sign Up** and enters email and password.
2. Email and password are validated on the frontend (email format, password length).
3. If valid, the app calls Firebase to create the user and sends a verification email.
4. User is prompted to verify their email before logging in.
5. After verification, user can log in normally.

### Login Flow
1. User clicks **Login** and enters email and password.
2. Email and password are validated on the frontend.
3. If valid, the app calls Firebase to authenticate.
4. If the user is verified, the app calls the backend `/users/auth/email/signup` endpoint with `isNewUser: false` to get a JWT and user info.
5. If the user is not verified, the app prompts for email verification.

### Backend API
- **Signup/Login endpoint:** `POST /users/auth/email/signup`
  - `isNewUser: true` for signup, `isNewUser: false` for login
  - Expects a Firebase ID token
  - Handles user creation, verification, and JWT issuance

### Error Handling & Validation
- Frontend checks for valid email and password before API calls.
- User-friendly error messages for all common issues (invalid email, weak password, unverified email, etc).