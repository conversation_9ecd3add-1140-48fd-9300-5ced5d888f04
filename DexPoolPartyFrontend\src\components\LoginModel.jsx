"use client";

import { useState, useEffect } from "react";
import { PartyPopper, X, Mail, Loader2, Lock, Eye, <PERSON>Off, <PERSON>rk<PERSON> } from "lucide-react";
import Connect2Phantom from "./Connect2Phantom"; // Assuming Connect2Phantom is in the same directory
import { signInWithGoogle, signInWithEmail, signUpWithEmail, signInWithApple, resendVerificationEmail } from "@/lib/firebase"; // Ensure these are correctly imported
import { API_BASE_URL } from "@/services/api"; // Ensure this is correctly imported
import { useAuth } from "@/contexts/AuthContext";
import { getAuth, OAuthProvider, sendPasswordResetEmail, signInWithPopup } from "firebase/auth";
import { toast } from "sonner"; // Ensure sonner is installed: npm install sonner
import { getImageUrl } from '@/lib/utils';
import ForgotPasswordSentModal from './ForgotPasswordSentModal';

const LoginModal = ({ onClose, onConnect, initialMode = 'login' }) => {
  const auth = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showResendVerification, setShowResendVerification] = useState(false);
  const [mode, setMode] = useState(initialMode); // 'login' or 'signup'
  const [showForgotSent, setShowForgotSent] = useState(false);
  const [forgotEmail, setForgotEmail] = useState("");
  useEffect(() => {
    setMode(initialMode);
  }, [initialMode]);
  const { isWalletConnected, disconnectWallet } = auth;

  // Clear any existing toasts when component mounts
  useEffect(() => {
    toast.dismiss();
  }, []);

  // Handle notification display with delay
  const showNotification = (type, message, description = "") => {
    toast.dismiss(); // Clear existing toasts
    setTimeout(() => {
      switch (type) {
        case 'success':
          toast.success(message, { description });
          break;
        case 'error':
          toast.error(message, { description });
          break;
        case 'info':
          toast.info(message, { description });
          break;
        case 'warning':
          toast.warning(message, { description });
          break;
        default:
          toast(message, { description });
      }
    }, 100); // Small delay to ensure previous toasts are cleared
  };

  // Helper to check if wallet is truly connected
  const isTrulyWalletConnected = isWalletConnected && typeof window !== 'undefined' && window.solana && window.solana.isConnected;

  // Handles connection of a Phantom wallet
  const handleWalletConnect = (publicKey) => {
    const user = JSON.parse(localStorage.getItem("user"));
    const token = localStorage.getItem("authToken");

    if (user && token) {
      auth.login(user, token);
      onConnect?.(publicKey);
      onClose?.();
      showNotification('success', "Wallet connected successfully!");
    } else {
      showNotification('error', "Wallet connection failed", "Missing user/token.");
    }
  };

  // Handles sending a password reset email
  const handleForgotPasswordSend = async (emailToSend) => {
    const authFirebase = getAuth();
    await sendPasswordResetEmail(authFirebase, emailToSend);
    // No need to showNotification here, modal handles confirmation
  };

  // Handles Google OAuth login
  const handleGoogleLogin = async () => {
    console.log('🔐 [LOGIN] Starting Google OAuth login process');
    setLoading(true);
    try {
      disconnectWallet(); // Clear wallet state on Google login
      console.log('🔐 [LOGIN] Disconnected wallet for Google login');

      console.log('🔐 [LOGIN] Calling Firebase signInWithGoogle...');
      const { idToken } = await signInWithGoogle();
      console.log('🔐 [LOGIN] Firebase Google sign-in successful, got ID token');

      console.log('🔐 [LOGIN] Sending ID token to backend for JWT generation...');
      const response = await fetch(`${API_BASE_URL}/users/auth/google`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ idToken }),
      });

      console.log('🔐 [LOGIN] Backend response status:', response.status);
      const data = await response.json();
      console.log('🔐 [LOGIN] Backend response data:', {
        success: response.ok,
        hasToken: !!data.token,
        hasUser: !!data.user,
        message: data.message
      });

      if (!response.ok) {
        // Handle specific error for unverified email during Google login
        if (response.status === 403 && !data.emailVerified) {
          console.log('🔐 [LOGIN] Email verification required');
          throw new Error("Email not verified. Please verify your email address.");
        }
        console.log('🔐 [LOGIN] Backend authentication failed:', data.message);
        throw new Error(data.message || "Backend authentication failed");
      }

      const { token, user } = data;
      console.log('🔐 [LOGIN] Backend authentication successful, storing tokens...');

      // Store tokens and user data in local storage
      localStorage.setItem("authToken", token);
      localStorage.setItem("firebaseToken", idToken);
      localStorage.setItem("user", JSON.stringify(user));

      console.log('🔐 [LOGIN] Tokens stored in localStorage, updating auth context...');
      auth.login(user, token); // Update auth context

      console.log('🔐 [LOGIN] Google login completed successfully');
      onClose(); // Close the modal
      showNotification('success', "Signed in with Google!");
    } catch (error) {
      console.log('🔐 [LOGIN] Google login error:', error.message);
      showNotification('error', error.message || "Google login error");
    } finally {
      setLoading(false);
    }
  };

  // Handles Apple OAuth login with improved error handling
  const handleAppleLogin = async () => {
    console.log('🔐 [LOGIN] Starting Apple OAuth login process');
    try {
      setLoading(true);
      disconnectWallet(); // Clear wallet state on Apple login
      console.log('🔐 [LOGIN] Disconnected wallet for Apple login');

      console.log('🔐 [LOGIN] Calling Firebase signInWithApple...');
      const { user, idToken } = await signInWithApple();
      console.log('🔐 [LOGIN] Firebase Apple sign-in successful:', {
        userEmail: user.email,
        hasIdToken: !!idToken,
        emailVerified: user.emailVerified,
        displayName: user.displayName
      });

      console.log('🔐 [LOGIN] Sending ID token to backend for JWT generation...');
      const response = await fetch(`${API_BASE_URL}/users/auth/apple`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ idToken }),
      });

      console.log('🔐 [LOGIN] Backend response status:', response.status);
      const data = await response.json();
      console.log('🔐 [LOGIN] Backend response data:', {
        success: response.ok,
        hasToken: !!data.token,
        hasUser: !!data.user,
        message: data.message,
        error: data.error
      });

      if (!response.ok) {
        if (response.status === 403 && data.needsVerification) {
          console.log('🔐 [LOGIN] Email verification required for Apple login');
          showNotification('info', "Please verify your email address before signing in with Apple.");
          return;
        }

        if (response.status === 401) {
          console.log('🔐 [LOGIN] Authentication token error:', data.error);
          if (data.error === 'INVALID_TOKEN') {
            showNotification('error', "Authentication failed. Please try signing in again.");
          } else if (data.error === 'TOKEN_EXPIRED') {
            showNotification('error', "Session expired. Please sign in again.");
          } else {
            showNotification('error', data.message || "Authentication failed. Please try again.");
          }
          return;
        }

        console.log('🔐 [LOGIN] Backend authentication failed:', data.message);
        throw new Error(data.message || "Backend authentication failed with Apple");
      }

      const { token, user: userData } = data;
      console.log('🔐 [LOGIN] Backend authentication successful, storing tokens...');

      localStorage.setItem("authToken", token);
      localStorage.setItem("firebaseToken", idToken);
      localStorage.setItem("user", JSON.stringify(userData));

      console.log('🔐 [LOGIN] Tokens stored in localStorage, updating auth context...');
      auth.login(userData, token);

      console.log('🔐 [LOGIN] Apple login completed successfully');
      onClose();
      showNotification('success', "Signed in with Apple successfully!");
    } catch (error) {
      console.log('🔐 [LOGIN] Apple login error:', error.message);

      // Handle specific Firebase Apple Sign-In errors
      if (error.code === "auth/operation-not-allowed") {
        showNotification('error', "Apple Sign-In is not enabled for this project. Please contact support or try a different sign-in method.");
      } else if (error.code === "auth/popup-closed-by-user") {
        showNotification('info', "Apple Sign-In popup closed. Please try again.");
      } else if (error.code === "auth/popup-blocked") {
        showNotification('error', "Apple Sign-In popup was blocked. Please allow popups for this site and try again.");
      } else if (error.code === "auth/unauthorized-domain") {
        showNotification('error', "This domain is not authorized for Apple Sign-In. Please contact support.");
      } else if (error.code === "auth/network-request-failed") {
        showNotification('error', "Network error during Apple Sign-In. Please check your internet connection and try again.");
      } else if (error.code === "auth/missing-or-invalid-nonce") {
        showNotification('error', "Apple Sign-In security validation failed. Please try again.");
      } else if (error.code === "auth/cancelled-popup-request") {
        showNotification('info', "Apple Sign-In was cancelled. Please try again.");
      } else if (error.code === "auth/account-exists-with-different-credential") {
        showNotification('error', "An account already exists with the same email address but different sign-in credentials. Please use a different sign-in method.");
      } else if (error.message.includes("Email verification required")) {
        showNotification('info', "Please verify your email address before signing in with Apple.");
      } else {
        showNotification('error', error.message || "Apple Sign-In failed. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  // Email validation helper
  const isValidEmail = (email) => {
    // Simple regex for email validation
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // Handles unified Email/Password authentication (Sign In or Sign Up)
  const handleEmailAuth = async (e) => {
    e.preventDefault();
    setLoading(true);
    setShowResendVerification(false);
    disconnectWallet(); // Clear wallet state on email login
    // Validation
    if (!isValidEmail(email)) {
      showNotification('error', 'Please enter a valid email address.');
      setLoading(false);
      return;
    }
    if (password.length < 6) {
      showNotification('error', 'Password should be at least 6 characters.');
      setLoading(false);
      return;
    }
    try {
      let firebaseResult;
      let signInError = null;

      try {
        firebaseResult = await signInWithEmail(email, password);
      } catch (error) {
        signInError = error;
        // If user not found or invalid credential, attempt sign up (which sends verification email)
        if (error.code === "auth/user-not-found" || error.code === "auth/invalid-credential") {
          try {
            await signUpWithEmail(email, password);
            showNotification('info', "No account found. A verification link has been sent to your email. Please verify to activate your account.");
            setShowResendVerification(true);
          } catch (signupError) {
            if (signupError.code === "auth/email-already-in-use") {
              showNotification('error', "An account with this email already exists. Please try logging in.");
            } else if (signupError.code === "auth/weak-password") {
              showNotification('error', "Password should be at least 6 characters.");
            } else {
              showNotification('error', signupError.message || "Failed to send verification email.");
            }
          }
          setLoading(false);
          return;
        } else if (error.code === "auth/wrong-password") {
          showNotification('error', "Incorrect password.");
          setLoading(false);
          return;
        } else if (error.code === "auth/invalid-email") {
          showNotification('error', "Invalid email address.");
          setLoading(false);
          return;
        } else if (error.code === "auth/too-many-requests") {
          showNotification('error', "Too many failed attempts. Please try again later.");
          setLoading(false);
          return;
        } else {
          showNotification('error', error.message || "Authentication failed. Try again later.");
          setLoading(false);
          return;
        }
      }

      // At this point, signInWithEmail succeeded
      const { idToken, user } = firebaseResult;
      // If user is not verified, do not log in
      if (!user.emailVerified) {
        showNotification('info', "Please verify your email. A verification link has been sent.");
        await resendVerificationEmail();
        setShowResendVerification(true);
        setLoading(false);
        return;
      }

      // User is verified, proceed to backend
      const response = await fetch(`${API_BASE_URL}/users/auth/email/signup`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ idToken, isNewUser: false }),
      });
      const data = await response.json();

      if (!response.ok) {
        if (response.status === 403 && data.needsVerification) {
          showNotification('info', "Please verify your email. A verification link has been sent.");
          setShowResendVerification(true);
          setLoading(false);
          return;
        }
        throw new Error(data.message || "Authentication failed");
      }

      const { token, user: backendUser, message } = data;
      localStorage.setItem("authToken", token);
      localStorage.setItem("firebaseToken", idToken);
      localStorage.setItem("user", JSON.stringify(backendUser));
      auth.login(backendUser, token);
      onClose();
      showNotification('success', message);
    } catch (error) {
      if (error.code === "auth/email-already-in-use") {
        showNotification('error', "An account with this email already exists. Please try logging in.");
      } else if (error.code === "auth/weak-password") {
        showNotification('error', "Password should be at least 6 characters.");
      } else {
        showNotification('error', error.message || "Authentication failed. Try again later.");
      }
    } finally {
      setLoading(false);
    }
  };

  // Handles signup with email and password
  const handleSignup = async (e) => {
    e.preventDefault();
    setLoading(true);
    setShowResendVerification(false);
    disconnectWallet();
    // Validation
    if (!isValidEmail(email)) {
      showNotification('error', 'Please enter a valid email address.');
      setLoading(false);
      return;
    }
    if (password.length < 6) {
      showNotification('error', 'Password should be at least 6 characters.');
      setLoading(false);
      return;
    }
    try {
      // Try to sign up with Firebase
      await signUpWithEmail(email, password);
      showNotification('info', "Verification link sent to your email. Please verify to activate your account.");
      setShowResendVerification(true);
      setMode('login'); // Switch to login after signup
    } catch (signupError) {
      if (signupError.code === "auth/email-already-in-use") {
        showNotification('error', "An account with this email already exists. Please try logging in.");
      } else if (signupError.code === "auth/weak-password") {
        showNotification('error', "Password should be at least 6 characters.");
      } else {
        showNotification('error', signupError.message || "Failed to send verification email.");
      }
    } finally {
      setLoading(false);
    }
  };

  // Handles resending email verification link
  const handleResendVerification = async () => {
    if (!email) {
      showNotification('error', "Please enter your email address first.");
      return;
    }
    setLoading(true);
    try {
      // const response = await fetch(`${API_BASE_URL}/users/auth/resend-verification`, {
      //   method: "POST",
      //   headers: { "Content-Type": "application/json" },
      //   body: JSON.stringify({ email }),
      // });
      // const data = await response.json();
      // if (!response.ok) {
      //   if (response.status === 429) { // HTTP 429 Too Many Requests
      //     throw new Error("You're sending too many requests. Please wait a few minutes before trying again.");
      //   }
      //   throw new Error(data.message || "Failed to resend verification email");
      // }
       await resendVerificationEmail()
      showNotification('info', "Verification email sent! Please check your inbox (and spam folder).");
    } catch (error) {
      showNotification('error', error.message || "Failed to resend verification email.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 w-full z-50 flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm p-2 sm:p-4">
      <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border border-slate-600 rounded-3xl w-full max-w-sm max-h-[90vh] flex flex-col p-4 relative shadow-2xl overflow-y-auto hide-scrollbar">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-slate-400 hover:text-white transition-colors duration-200 z-10"
        >
          <X className="h-5 w-5" />
        </button>

        <div className="text-center mb-4 flex-shrink-0">
          <div className="flex items-center justify-center mb-2">
            <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <PartyPopper className="h-5 w-5 text-white" />
            </div>
          </div>
          <h2 className="text-lg font-bold text-white mb-1">
            {mode === 'login' ? 'Welcome Back' : 'Create an Account'}
          </h2>
          <p className="text-slate-400 text-sm">
            {mode === 'login' ? 'Choose your preferred login method' : 'Sign up with your email and password'}
          </p>
        </div>

        <div className="flex-1 space-y-2.5">
          {/* Phantom Wallet Connection */}
          {isTrulyWalletConnected ? (
            <button
              onClick={disconnectWallet}
              className="w-full flex items-center justify-center gap-2 py-2.5 px-3 rounded-xl bg-slate-800/50 hover:bg-slate-700/50 border border-slate-600 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed text-white transition-all duration-200 text-sm"
            >
              <img src="/phantom.svg" alt="Phantom" className="w-4 h-4" />
              <span className="font-medium">Disconnect Phantom</span>
            </button>
          ) : (
            <Connect2Phantom mode="login" onConnect={handleWalletConnect} isDisabled={loading} />
          )}

          {/* Google Login Button */}
          <button
            onClick={handleGoogleLogin}
            disabled={loading}
            className="w-full flex items-center justify-center gap-2 py-2.5 px-3 rounded-xl bg-slate-800/50 hover:bg-slate-700/50 border border-slate-600 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed text-white transition-all duration-200 text-sm"
          >
            {loading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <>
                <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M22.56 12.25C22.56 11.47 22.49 10.72 22.36 10H12V14.255H17.92C17.665 15.63 16.89 16.795 15.725 17.575V20.335H19.28C21.36 18.42 22.56 15.6 22.56 12.25Z"
                    fill="#4285F4"
                  />
                  <path
                    d="M12 23c3.04 0 5.584-1.01 7.455-2.715l-3.49-2.74c-.955.64-2.175.995-3.965.995-3.05 0-5.635-2.06-6.555-4.83H1.28v2.82C3.115 20.365 7.015 23 12 23Z"
                    fill="#34A853"
                  />
                  <path
                    d="M5.445 13.71c-.19-.57-.295-1.185-.295-1.81s.105-1.24.295-1.81V7.27H1.28C.645 8.525.245 9.915.245 11.4s.4 2.875 1.035 4.13l3.21-2.52.955-.74Z"
                    fill="#FBBC05"
                  />
                  <path
                    d="M12 5.58c1.715 0 2.87.745 3.53 1.37l2.63-2.63C17.565 2.81 15.035 1.5 12 1.5c-4.985 0-8.885 2.635-10.72 6.77l4.165 3.24C6.365 7.645 8.95 5.58 12 5.58Z"
                    fill="#EA4335"
                  />
                </svg>
                <span className="font-medium">Continue with Google</span>
              </>
            )}
          </button>

          {/* Apple Login Button */}
          <button
            onClick={handleAppleLogin}
            disabled={loading}
            className="w-full flex items-center justify-center gap-2 py-2.5 px-3 rounded-xl bg-slate-800/50 hover:bg-slate-700/50 border border-slate-600 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed text-white transition-all duration-200 text-sm"
          >
            {loading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <>
                <svg className="w-4 h-4" fill="white" viewBox="0 0 24 24">
                  <path d="M16.365 1.43c0 1.14-.475 2.212-1.227 3.014-.737.786-1.948 1.393-3.03 1.22-.122-1.065.365-2.185 1.104-2.947.745-.762 2.03-1.27 3.153-1.287zM20.737 17.71c-.927 2.003-1.924 3.977-3.423 3.998-1.37.02-1.727-.872-3.197-.872-1.476 0-1.87.857-3.05.894-1.27.036-2.247-2.008-3.172-4.002-1.73-3.755-1.51-8.598.755-10.995 1.21-1.31 2.828-1.382 3.685-1.382 1.555 0 2.443.893 3.662.893 1.2 0 2.14-.9 3.685-.9.6 0 2.74.05 3.897 2.488-3.37 1.68-2.812 6.058-.342 9.877z" />
                </svg>
                <span className="font-medium">Continue with Apple</span>
              </>
            )}
          </button>

          <div className="flex items-center my-3">
            <hr className="flex-grow border-slate-600" />
            <span className="mx-2 text-xs text-slate-400 font-medium">or</span>
            <hr className="flex-grow border-slate-600" />
          </div>

          {/* Email & Password Form */}
          {mode === 'login' ? (
            <form onSubmit={handleEmailAuth} className="space-y-2.5">
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-slate-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full pl-8 pr-3 py-2.5 bg-slate-800/50 border border-slate-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-slate-400 transition-all duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  required
                  disabled={loading}
                />
              </div>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-slate-400" />
                <input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  className="w-full pl-8 pr-9 py-2.5 bg-slate-800/50 border border-slate-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-slate-400 transition-all duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  required
                  disabled={loading}
                  minLength={6}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors disabled:opacity-50"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="h-3.5 w-3.5" /> : <Eye className="h-3.5 w-3.5" />}
                </button>
              </div>
              <div className="text-right">
                <button
                  type="button"
                  onClick={() => setShowForgotSent(true)}
                  className="text-xs text-blue-400 hover:underline focus:outline-none"
                  disabled={loading}
                >
                  Forgot Password?
                </button>
              </div>
              <button
                type="submit"
                disabled={loading}
                className="w-full py-2.5 px-3 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl text-sm"
              >
                {loading ? <Loader2 className="w-4 h-4 mr-1.5 animate-spin" /> : <Sparkles className="w-4 h-4 mr-1.5" />}
                Sign In
              </button>
              {/* Resend verification button */}
              {showResendVerification && (
                <button
                  type="button"
                  onClick={handleResendVerification}
                  className="w-full py-2.5 px-3 rounded-xl bg-slate-700 hover:bg-slate-600 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl text-sm mt-2"
                  disabled={loading}
                >
                  {loading ? <Loader2 className="w-4 h-4 mr-1.5 animate-spin" /> : null}
                  Resend verification email
                </button>
              )}
            </form>
          ) : (
            <form onSubmit={handleSignup} className="space-y-2.5">
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-slate-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full pl-8 pr-3 py-2.5 bg-slate-800/50 border border-slate-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-slate-400 transition-all duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  required
                  disabled={loading}
                />
              </div>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-slate-400" />
                <input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Create a password"
                  className="w-full pl-8 pr-9 py-2.5 bg-slate-800/50 border border-slate-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-slate-400 transition-all duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  required
                  disabled={loading}
                  minLength={6}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors disabled:opacity-50"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="h-3.5 w-3.5" /> : <Eye className="h-3.5 w-3.5" />}
                </button>
              </div>
              <button
                type="submit"
                disabled={loading}
                className="w-full py-2.5 px-3 rounded-xl bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl text-sm"
              >
                {loading ? <Loader2 className="w-4 h-4 mr-1.5 animate-spin" /> : <Sparkles className="w-4 h-4 mr-1.5" />}
                Sign Up
              </button>
            </form>
          )}
        </div>
        <div className="mt-4 text-center flex-shrink-0">
          <p className="text-xs text-slate-500">
            {mode === 'login' ? (
              <>Don't have an account?{' '}
                <button
                  className="text-blue-400 hover:underline focus:outline-none"
                  onClick={() => setMode('signup')}
                  disabled={loading}
                >
                  Sign Up
                </button>
              </>
            ) : (
              <>Already have an account?{' '}
                <button
                  className="text-blue-400 hover:underline focus:outline-none"
                  onClick={() => setMode('login')}
                  disabled={loading}
                >
                  Sign In
                </button>
              </>
            )}
          </p>
        </div>
      </div>
      {showForgotSent && (
        <ForgotPasswordSentModal
          onClose={() => setShowForgotSent(false)}
          onSendEmail={handleForgotPasswordSend}
          onGoogleLogin={handleGoogleLogin}
          onAppleLogin={handleAppleLogin}
          onPhantomLogin={() => {
            setShowForgotSent(false);
            // Focus Phantom connect button in main modal if needed
          }}
        />
      )}
    </div>
  );
};

export default LoginModal;