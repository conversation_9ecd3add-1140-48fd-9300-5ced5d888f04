const mongoose = require('mongoose');

const tokenSchema = new mongoose.Schema({
  _id: { type: String, required: true }, // Use string as the primary key
  name: {
    type: String,
    required: [true, 'Token name is required'],
    trim: true
  },
  symbol: {
    type: String,
    required: [true, 'Token symbol is required'],
    uppercase: true,
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Token description is required']
  },
  media: {
    type: String
  },
  links: {
    telegram: String,
    website: String,
    twitter: String
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  creator: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  ourPlatformToken: {
    type: Boolean,
    default: false,
  },
  otherTokenId: {
    type: String,
    default: null,
    // unique: true
  },
  comments: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment'
    }
  ]
}, {
  timestamps: true
});

module.exports = mongoose.model('Token', tokenSchema);
