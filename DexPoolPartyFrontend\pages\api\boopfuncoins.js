const BITQUERY_ENDPOINT = "https://streaming.bitquery.io/eap"
const BITQUERY_API_KEY = process.env.BITQUERY_API_KEY || ""

const BOOP_FUN_PROGRAM = "boop8hVGQGqehUK2iVEMEnMrL5RbjywRzHKBmBE7ry4"

const BOOP_FUN_MIGRATIONS_QUERY = `query GetBoopFunMigrations($limit: Int = 100, $since: DateTime) {
  Solana {
    Instructions(
      where: {
        Transaction: { Result: { Success: true } }
        Instruction: {
          Program: {
            Address: { is: "${BOOP_FUN_PROGRAM}" }
            Method: { is: "graduate" }
          }
        }
        Block: {
          Time: { since: $since }
        }
      }
      limit: { count: $limit }
      orderBy: { descending: Block_Time }
    ) {
      Block {
        Time
      }
      Transaction {
        Signature
        Signer
        Fee
      }
      Instruction {
        Accounts {
          IsWritable
          Address
          Token {
            Mint
            Owner
            ProgramId
          }
        }
        Program {
          AccountNames
          Address
          Arguments {
            Name
            Type
            Value {
              ... on Solana_ABI_Json_Value_Arg {
                json
              }
              ... on Solana_ABI_Float_Value_Arg {
                float
              }
              ... on Solana_ABI_Boolean_Value_Arg {
                bool
              }
              ... on Solana_ABI_Bytes_Value_Arg {
                hex
              }
              ... on Solana_ABI_BigInt_Value_Arg {
                bigInteger
              }
              ... on Solana_ABI_Address_Value_Arg {
                address
              }
              ... on Solana_ABI_String_Value_Arg {
                string
              }
              ... on Solana_ABI_Integer_Value_Arg {
                integer
              }
            }
          }
          Method
          Name
        }
      }
    }
  }
}`

function resolveIpfsUri(uri) {
  if (uri && uri.startsWith("ipfs://")) {
    return `https://ipfs.io/ipfs/${uri.substring(7)}`
  }
  return uri
}

function extractTokenFromAccounts(accounts) {
  if (!accounts || !Array.isArray(accounts)) return null

  const tokenAccount = accounts.find((account) => account.Token && account.Token.Mint)
  return tokenAccount ? tokenAccount.Token.Mint : null
}

async function fetchTokenMetadata(mintAddress) {
  console.warn(`[Placeholder] Fetching metadata for mint: ${mintAddress}`)
  return {
    name: `Boop Token ${mintAddress.substring(0, 8)}`,
    symbol: `BOOP${mintAddress.substring(0, 4)}`,
    decimals: 6,
    metadataUri: null,
    logo: null,
  }
}

async function transformBoopFunData(data) {
  const items = data?.Solana?.Instructions || []

  if (!items.length) return []

  const transformedItemsPromises = items.map(async (item, index) => {
    const block = item.Block || {}
    const transaction = item.Transaction || {}
    const instruction = item.Instruction || {}
    const accounts = instruction.Accounts || []

    const mintAddress =
      extractTokenFromAccounts(accounts) ||
      `boop-${block.Time || index}-${transaction.Signature?.substring(0, 8) || ""}`

    const metadata = await fetchTokenMetadata(mintAddress)

    let logo = null
    if (metadata.metadataUri) {
      const metadataUri = resolveIpfsUri(metadata.metadataUri)
      try {
        const metadataResponse = await fetch(metadataUri, {
          cache: "force-cache",
          signal: AbortSignal.timeout(5000),
        })
        if (metadataResponse.ok) {
          const metadataJson = await metadataResponse.json()
          if (metadataJson && typeof metadataJson.image === "string") {
            logo = resolveIpfsUri(metadataJson.image)
          }
        }
      } catch (metadataError) {
        console.error(`Error fetching metadata for ${mintAddress}:`, metadataError)
      }
    }

    return {
      id: mintAddress,
      symbol: metadata.symbol || `BOOP${index + 1}`,
      name: metadata.name || `Boop.fun Token ${index + 1}`,
      mintAddress: mintAddress,
      creator: transaction.Signer || "Unknown",
      createdAt: block.Time ? new Date(block.Time).toISOString() : new Date().toISOString(),
      createdAtFormatted: block.Time ? new Date(block.Time).toLocaleString() : "Unknown",
      blockNumber: 0,
      blockHash: "",
      transactionSignature: transaction.Signature || "",
      transactionFee: transaction.Fee || 0,
      supply: "1000000000",
      postBalance: "0",
      marketCapUSD: "0",
      decimals: metadata.decimals || 6,
      isMutable: true,
      fungible: true,
      metadataUri: metadata.metadataUri || null,
      logo: logo || metadata.logo || `/placeholder.svg?height=48&width=48`,
      updateAuthority: null,
      tokenStandard: null,
      programAddress: BOOP_FUN_PROGRAM,
      primarySaleHappened: true,
      isBoopFun: true,
      platform: "boop.fun",
      isGraduated: true,
    }
  })

  return Promise.all(transformedItemsPromises)
}

export default async function handler(req, res) {
  try {
    const { searchParams } = new URL(req.url, `http://${req.headers.host}`)
    const limit = Number.parseInt(searchParams.get("limit") || "100")
    const since = searchParams.get("since")

    const defaultSince = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    const sinceParam = since || defaultSince

    const requestBody = {
      query: BOOP_FUN_MIGRATIONS_QUERY,
      variables: {
        limit: limit,
        since: sinceParam,
      },
    }

    if (req.method === "POST") {
      const body = await req.json()
      const { customVariables = {} } = body
      requestBody.variables = {
        ...requestBody.variables,
        ...customVariables,
      }
    }

    console.log("Making BitQuery request for boop.fun with variables:", requestBody.variables)

    const response = await fetch(BITQUERY_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BITQUERY_API_KEY}`,
        "X-API-KEY": BITQUERY_API_KEY,
      },
      body: JSON.stringify(requestBody),
    })

    // Handle non-JSON responses
    const contentType = response.headers.get("content-type")
    if (!contentType || !contentType.includes("application/json")) {
      const errorText = await response.text()
      console.error(`BitQuery API returned non-JSON response: ${response.status} - ${errorText.substring(0, 200)}`)

      return res.status(response.status).json({
        success: false,
        error: `BitQuery API error: ${response.status} ${response.statusText}`,
        details: "API quota exceeded or service unavailable",
        timestamp: new Date().toISOString(),
      })
    }

    if (!response.ok) {
      const errorData = await response.json()
      console.error(`BitQuery API error: ${response.status} ${response.statusText}`, errorData)

      return res.status(response.status).json({
        success: false,
        error: `BitQuery API error: ${response.statusText}`,
        details: errorData,
        timestamp: new Date().toISOString(),
      })
    }

    const result = await response.json()

    if (result.errors) {
      console.error("BitQuery errors:", result.errors)
      return res.status(400).json({
        success: false,
        error: "BitQuery returned GraphQL errors",
        details: result.errors,
        timestamp: new Date().toISOString(),
      })
    }

    const transformedData = await transformBoopFunData(result.data)

    return res.status(200).json({
      success: true,
      data: transformedData,
      count: transformedData.length,
      queryType: "migrations",
      since: sinceParam,
      timestamp: new Date().toISOString(),
      message:
        transformedData.length > 0
          ? `Found ${transformedData.length} boop.fun token migrations.`
          : "No migrations found in the specified time range.",
    })
  } catch (error) {
    console.error("API Error:", error)

    return res.status(500).json({
      success: false,
      error: "Internal Server Error",
      details: error.message,
      data: [],
      count: 0,
      timestamp: new Date().toISOString(),
    })
  }
}
