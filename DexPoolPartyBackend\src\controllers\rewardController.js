const User = require("../models/user.model");
const Reward = require("../models/Reward");

/**
 * Get user rewards summary
 * GET /api/rewards/user/:userId
 */
exports.getUserRewardsSummary = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findOne({ _id: userId })
      .populate("referrals.userId", "username profileImage joinDate")
      .select("rewards referralCode referrals stats");

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    const recentRewards = await Reward.find({ userId })
      .sort({ createdAt: -1 })
      .limit(10)
      .populate("referralUserId", "username");

    const rewardStats = await Reward.aggregate([
      { $match: { userId: user._id } }, // Ensure ObjectId for aggregation
      {
        $group: {
          _id: "$type",
          total: { $sum: "$amount" },
          count: { $sum: 1 },
        },
      },
    ]);

    res.json({
      user: {
        rewards: user.rewards,
        referralCode: user.referralCode,
        referrals: user.referrals,
        stats: user.stats,
      },
      recentRewards,
      rewardStats,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Apply referral code
 * POST /api/rewards/referral/apply
 */
exports.applyReferralCode = async (req, res) => {
  try {
    const { userId, referralCode } = req.body;

    const user = await User.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    if (user.referredBy) {
      return res.status(400).json({ error: "User already has a referrer" });
    }

    const referrer = await User.findOne({ referralCode });
    if (!referrer) {
      return res.status(404).json({ error: "Invalid referral code" });
    }

    if (referrer._id.toString() === userId) {
      return res.status(400).json({ error: "Cannot refer yourself" });
    }

    // Update user with referrer
    user.referredBy = referrer._id;
    await user.save();

    // Add to referrer's referrals
    referrer.referrals.push({
      userId: user._id,
      joinedAt: new Date(),
      totalRewardEarned: 0,
    });

    // Give referral bonus to referrer
    const referralBonus = 100; // 100 tokens bonus
    referrer.rewards.referralEarnings += referralBonus;
    referrer.rewards.totalEarned += referralBonus;
    referrer.rewards.availableBalance += referralBonus;

    await referrer.save();

    // Create reward record for referrer
    await new Reward({
      userId: referrer._id,
      type: "referral",
      amount: referralBonus,
      description: `Referral bonus for inviting ${user.username || "new user"}`,
      referralUserId: user._id,
    }).save();

    // Give welcome bonus to new user
    const welcomeBonus = 50;
    user.rewards.totalEarned += welcomeBonus;
    user.rewards.availableBalance += welcomeBonus;
    await user.save();

    await new Reward({
      userId: user._id,
      type: "referral",
      amount: welcomeBonus,
      description: "Welcome bonus for joining with referral code",
      referralUserId: referrer._id,
    }).save();

    res.json({
      message: "Referral code applied successfully",
      referralBonus,
      welcomeBonus,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Award trade bonus
 * POST /api/rewards/trade-bonus
 */
exports.awardTradeBonus = async (req, res) => {
  try {
    const { userId, tradeAmount, tradeId } = req.body;

    const user = await User.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Calculate trade bonus (1% of trade amount)
    const bonusPercentage = 0.01;
    const tradeBonus = Math.floor(tradeAmount * bonusPercentage);

    if (tradeBonus > 0) {
      // Update user rewards
      user.rewards.tradeBonus += tradeBonus;
      user.rewards.totalEarned += tradeBonus;
      user.rewards.availableBalance += tradeBonus;
      user.stats.totalTrades += 1;

      await user.save();

      // Create reward record
      await new Reward({
        userId,
        type: "trade_bonus",
        amount: tradeBonus,
        description: `Trade bonus for ${tradeAmount} tokens trade`,
        tradeId,
        metadata: { tradeAmount },
      }).save();

      // Check for referrer bonus
      if (user.referredBy) {
        const referrer = await User.findOne({ _id: user.referredBy });
        if (referrer) {
          const referrerBonus = Math.floor(tradeBonus * 0.1); // 10% of trade bonus

          referrer.rewards.referralEarnings += referrerBonus;
          referrer.rewards.totalEarned += referrerBonus;
          referrer.rewards.availableBalance += referrerBonus;

          // Update referral stats
          const referralIndex = referrer.referrals.findIndex(
            (ref) => ref.userId.toString() === userId
          );
          if (referralIndex !== -1) {
            referrer.referrals[referralIndex].totalRewardEarned += referrerBonus;
          }

          await referrer.save();

          await new Reward({
            userId: referrer._id,
            type: "referral",
            amount: referrerBonus,
            description: `Referral trade bonus from ${user.username || "referral"}`,
            referralUserId: userId,
            metadata: { tradeAmount },
          }).save();
        }
      }
    }

    res.json({
      message: "Trade bonus awarded",
      tradeBonus,
      totalRewards: user.rewards.totalEarned,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Get rewards leaderboard
 * GET /api/rewards/leaderboard
 */
exports.getRewardsLeaderboard = async (req, res) => {
  try {
    const { type = "total", limit = 10 } = req.query;

    let sortField;
    switch (type) {
      case "referral":
        sortField = "rewards.referralEarnings";
        break;
      case "trade":
        sortField = "rewards.tradeBonus";
        break;
      default:
        sortField = "rewards.totalEarned";
    }

    const leaderboard = await User.find({
      "rewards.totalEarned": { $gt: 0 },
      banned: { $ne: true },
    })
      .select("username profileImage rewards stats referrals")
      .sort({ [sortField]: -1 })
      .limit(Number.parseInt(limit));

    res.json(leaderboard);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Withdraw rewards
 * POST /api/rewards/withdraw
 */
exports.withdrawRewards = async (req, res) => {
  try {
    const { userId, amount, withdrawalMethod = "wallet" } = req.body;

    const user = await User.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    if (amount > user.rewards.availableBalance) {
      return res.status(400).json({ error: "Insufficient balance" });
    }

    const minWithdraw = 100; // Minimum withdrawal amount
    if (amount < minWithdraw) {
      return res
        .status(400)
        .json({ error: `Minimum withdrawal amount is ${minWithdraw}` });
    }

    // Update user balance
    user.rewards.availableBalance -= amount;
    user.rewards.totalWithdrawn += amount;
    await user.save();

    // Create withdrawal record
    await new Reward({
      userId,
      type: "withdrawal",
      amount: -amount, // Negative amount to represent withdrawal
      description: `Reward withdrawal of ${amount} tokens`,
      status: "pending",
      metadata: { withdrawalMethod },
    }).save();

    res.json({
      message: "Withdrawal request submitted",
      remainingBalance: user.rewards.availableBalance,
      withdrawalId: new Date().getTime(), // Simple ID for tracking
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Claim daily bonus
 * POST /api/rewards/daily-bonus
 */
exports.claimDailyBonus = async (req, res) => {
  try {
    const { userId } = req.body;

    const user = await User.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Check if user already claimed today
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayBonus = await Reward.findOne({
      userId,
      type: "daily_bonus",
      createdAt: { $gte: today },
    });

    if (todayBonus) {
      return res.status(400).json({ error: "Daily bonus already claimed today" });
    }

    const dailyBonus = 10; // 10 tokens daily bonus

    // Update user rewards
    user.rewards.loyaltyBonus += dailyBonus;
    user.rewards.totalEarned += dailyBonus;
    user.rewards.availableBalance += dailyBonus;
    user.lastActive = new Date();

    await user.save();

    // Create reward record
    await new Reward({
      userId,
      type: "daily_bonus",
      amount: dailyBonus,
      description: "Daily login bonus",
    }).save();

    res.json({
      message: "Daily bonus claimed successfully",
      bonusAmount: dailyBonus,
      totalBalance: user.rewards.availableBalance,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};