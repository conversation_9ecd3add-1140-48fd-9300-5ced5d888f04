"use client";

import React from "react";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import categories from "../../../src/category.json";
import coins from "../../../src/coins.json";
// import trending from "../../../src/trending.json";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { Switch } from "@/components/ui/switch";
import { CoinMarketProvider } from "../../../context/trendingData";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import TrendingCoinsCarousel from "@/components/TrendingCoinsCarousel";
import AvailableCoins from "@/components/AvailableCoins";
import { useAuth } from "@/contexts/AuthContext";
import { tokenService } from "@/services/api";
import { toast } from "sonner";

// Helper function to get image URL from backend path
const getImageUrl = (mediaPath) => {
  if (!mediaPath) return "/token.png"; // fallback
  if (mediaPath.startsWith("http") || mediaPath.startsWith("data:"))
    return mediaPath;

  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
  if (!backendUrl) {
    console.warn("NEXT_PUBLIC_BACKEND_URL is not defined.");
    return "/token.png";
  }

  // Normalize Windows-style paths
  const normalizedPath = mediaPath.replace(/\\/g, "/");

  // Remove /api from backend URL if present since uploads are served at root level
  const baseUrl = backendUrl.replace(/\/api$/, "");

  // Combine the base URL and the path properly
  const finalPath = baseUrl.endsWith("/")
    ? normalizedPath.replace(/^\//, "")
    : `/${normalizedPath.replace(/^\//, "")}`;

  return `${baseUrl}${finalPath}`;
};

const HomePage = ({ Component, pageProps }) => {
  // printing the coins data here

  const { user } = useAuth();
  const [createdCoins, setCreatedCoins] = useState([]);
  const [loadingCreatedCoins, setLoadingCreatedCoins] = useState(true);

  useEffect(() => {
    const fetchCreatedCoins = async () => {
      if (!user) {
        setLoadingCreatedCoins(false);
        setCreatedCoins([]); // Set empty array when no user
        return;
      }
      try {
        setLoadingCreatedCoins(true);
        const response = await tokenService.getUserTokens(user.id);
        // Extract tokens array from response and ensure it's an array
        const tokens = response.tokens || [];
        setCreatedCoins(tokens);
      } catch (error) {
        console.error("Failed to fetch created coins:", error);
        setCreatedCoins([]); // Set empty array on error
      } finally {
        setLoadingCreatedCoins(false);
      }
    };

    fetchCreatedCoins();
  }, [user]);

  return (
    <div className="w-full max-w-7xl mx-auto px-2 sm:px-6 md:px-8 mt-8 mb-20">
      {/* Trending Section */}
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl md:text-4xl font-extrabold tracking-tight">
            Now Trending
          </h2>
        </div>
        <section className="rounded-2xl bg-[#18181c] shadow-lg p-4 md:p-6 transition-all">
          <TrendingCoinsCarousel {...pageProps} />
        </section>
      </div>

      {/* Categories Section */}
      <div className="mt-10">
        <h3 className="text-xl md:text-2xl font-bold mb-4">Categories</h3>
        <div className="flex items-center gap-2 overflow-x-auto pb-2">
          {categories.categories.map((cat, idx) => (
            <div
              key={idx}
              className="flex items-center px-4 py-2 rounded-full border border-white/20 bg-[#23232a] text-sm font-medium shadow-sm hover:bg-[#292933] transition cursor-pointer whitespace-nowrap"
            >
              {cat}
            </div>
          ))}
        </div>
      </div>

      {/* Filters Section */}
      <div className="flex flex-wrap items-center gap-4 mt-8 mb-4">
        <Button className="bg-purple-600 hover:bg-purple-700 rounded-lg px-4 py-2 text-sm font-semibold shadow transition">
          Sort Featured
        </Button>
        <div className="flex items-center gap-2">
          <Label htmlFor="show-animations" className="text-sm">
            Show animations:
          </Label>
          <Switch id="show-animations" />
        </div>
        <div className="flex items-center gap-2">
          <Label htmlFor="include-nsfw" className="text-sm">
            Include NSFW:
          </Label>
          <Switch id="include-nsfw" />
        </div>
      </div>

      {/* Available Coins Section */}
      <div className="mt-8">
        <h3 className="text-xl md:text-2xl font-bold mb-4">Available Coins</h3>
        <section className="rounded-2xl bg-[#18181c] shadow-lg p-4 md:p-6 transition-all">
          <AvailableCoins {...pageProps} />
        </section>
      </div>

      {/* User Created Coins Section */}
      {user && (
        <div className="mt-10">
          <h3 className="text-xl md:text-2xl font-bold mb-4">
            Your Created Coins
          </h3>
          <section className="rounded-2xl bg-[#18181c] shadow-lg p-4 md:p-6 transition-all">
            {loadingCreatedCoins ? (
              <div className="text-center text-gray-400 py-4">
                Loading created coins...
              </div>
            ) : !createdCoins || createdCoins.length === 0 ? (
              <div className="text-center text-gray-400 py-4">
                No coins created yet.
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {createdCoins.map((coin, idx) => (
                  <Card
                    key={idx}
                    className="rounded-xl bg-[#23232a] shadow-md hover:shadow-xl transition p-4 flex flex-col items-center"
                  >
                    <Image
                      src={getImageUrl(coin.media)}
                      alt={coin.name}
                      width={64}
                      height={64}
                      className="rounded-full mb-2"
                    />
                    <div className="font-bold text-lg mb-1">{coin.name}</div>
                    <div className="text-sm text-gray-400 mb-2">
                      {coin.symbol}
                    </div>
                    <Link href={`/${coin.id}`} className="mt-auto">
                      <Button className="bg-purple-600 hover:bg-purple-700 rounded-full px-4 py-1 text-sm font-semibold shadow transition">
                        View
                      </Button>
                    </Link>
                  </Card>
                ))}
              </div>
            )}
          </section>
        </div>
      )}
    </div>
  );
};

export default HomePage;
