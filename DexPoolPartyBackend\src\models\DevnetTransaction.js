const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     DevnetTransaction:
 *       type: object
 *       required:
 *         - user
 *         - token
 *         - type
 *         - amount
 *         - price
 *       properties:
 *         user:
 *           type: string
 *           description: Reference to the user making the transaction
 *         token:
 *           type: string
 *           description: Reference to the token involved
 *         type:
 *           type: string
 *           enum: [buy, sell]
 *           description: Type of transaction
 *         amount:
 *           type: number
 *           description: Amount of tokens
 *         price:
 *           type: number
 *           description: Price per token
 *         totalValue:
 *           type: number
 *           description: Total value of the transaction
 *         txHash:
 *           type: string
 *           description: Transaction hash
 *         timestamp:
 *           type: string
 *           format: date-time
 *         status:
 *           type: string
 *           enum: [pending, completed, failed]
 *           default: pending
 *           description: Status of the transaction
 *         network:
 *           type: string
 *           default: devnet
 *           description: Network type (should be 'devnet')
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

const devnetTransactionSchema = new mongoose.Schema({
  token: { type: String, required: true }, // Changed from ObjectId to String
  user: { type: String, required: true },  // Changed from ObjectId to String
  userWallet: { type: String, required: true },
  type: {
    type: String,
    enum: ['buy', 'sell'],
    required: true
  },
  amount: { type: Number, required: true },
  price: { type: Number, required: true },
  totalValue: { type: Number, required: true },
  txHash: { type: String },
  timestamp: { type: Date, default: Date.now },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed'],
    default: 'pending'
  },
  network: {
    type: String,
    default: 'devnet'
  }
}, { timestamps: true });

module.exports = mongoose.model('DevnetTransaction', devnetTransactionSchema); 