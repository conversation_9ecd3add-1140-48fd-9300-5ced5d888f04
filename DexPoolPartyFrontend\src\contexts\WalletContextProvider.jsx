// src/context/WalletContextProvider.jsx
'use client';

import React, { useMemo } from 'react';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import {
  PhantomWalletAdapter,
  SolflareWalletAdapter,
  // Add other wallets you want to support
} from '@solana/wallet-adapter-wallets';
import {
  WalletModalProvider,
  WalletMultiButton, // For a simple connect button
} from '@solana/wallet-adapter-react-ui';

// Default styles for the wallet adapter
import '@solana/wallet-adapter-react-ui/styles.css';

const WalletContextProvider = ({ children }) => {
  const network = WalletAdapterNetwork.Devnet; // Or Mainnet, Testnet

  const wallets = useMemo(
    () => [
      new PhantomWalletAdapter(),
      new SolflareWalletAdapter({ network }),
      // Add more wallets here
    ],
    [network]
  );

  const endpoint = useMemo(() => process.env.NEXT_PUBLIC_SOLANA_RPC_URL || 'https://api.devnet.solana.com', []);

  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
};

export default WalletContextProvider;