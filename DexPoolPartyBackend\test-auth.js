const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:4000';
const API_URL = `${BASE_URL}/api`;

// Test scenarios
const testScenarios = [
  {
    name: 'Test 1: Request without Authorization header',
    method: 'GET',
    url: `${API_URL}/users`,
    headers: {},
    expectedStatus: 401
  },
  {
    name: 'Test 2: Request with invalid Authorization format',
    method: 'GET',
    url: `${API_URL}/users`,
    headers: {
      'Authorization': 'InvalidToken'
    },
    expectedStatus: 401
  },
  {
    name: 'Test 3: Request with Bear<PERSON> but no token',
    method: 'GET',
    url: `${API_URL}/users`,
    headers: {
      'Authorization': 'Bearer '
    },
    expectedStatus: 401
  },
  {
    name: 'Test 4: Request with invalid JWT token',
    method: 'GET',
    url: `${API_URL}/users`,
    headers: {
      'Authorization': 'Bearer invalid.jwt.token'
    },
    expectedStatus: 401
  },
  {
    name: 'Test 5: Request with valid JWT token (if available)',
    method: 'GET',
    url: `${API_URL}/users`,
    headers: {
      'Authorization': 'Bearer YOUR_VALID_TOKEN_HERE'
    },
    expectedStatus: 200,
    skip: true // Set to false when you have a valid token
  }
];

async function runTest(scenario) {
  console.log(`\n🧪 Running: ${scenario.name}`);
  console.log(`📡 ${scenario.method} ${scenario.url}`);
  
  if (scenario.skip) {
    console.log('⏭️  Skipping this test');
    return;
  }

  try {
    const response = await axios({
      method: scenario.method,
      url: scenario.url,
      headers: scenario.headers,
      validateStatus: () => true // Don't throw on non-2xx status codes
    });

    console.log(`📊 Status: ${response.status}`);
    console.log(`📄 Response:`, response.data);

    if (response.status === scenario.expectedStatus) {
      console.log('✅ Test PASSED');
    } else {
      console.log('❌ Test FAILED - Unexpected status code');
    }

  } catch (error) {
    console.log(`❌ Test FAILED - Network error:`, error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting Bearer Token Authentication Tests');
  console.log(`📍 Testing against: ${BASE_URL}`);
  console.log('=' .repeat(60));

  for (const scenario of testScenarios) {
    await runTest(scenario);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
  }

  console.log('\n' + '=' .repeat(60));
  console.log('🏁 All tests completed!');
  console.log('\n📝 Instructions for manual testing:');
  console.log('1. Start your server: npm start');
  console.log('2. Open Swagger UI: http://localhost:4000/api-docs');
  console.log('3. Try the authentication endpoints to get a valid token');
  console.log('4. Use the token in the "Authorize" button in Swagger UI');
  console.log('5. Test protected endpoints with the bearer token');
}

// Helper function to decode JWT token (for debugging)
function decodeJWT(token) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    return null;
  }
}

// Export functions for manual testing
module.exports = {
  runAllTests,
  runTest,
  decodeJWT,
  testScenarios
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
} 