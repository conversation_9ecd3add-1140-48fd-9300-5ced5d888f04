// components/Sidebar.jsx
"use client";
import React, { useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  Home,
  Settings,
  MonitorPlay,
  PlusCircle,
  LifeBuoy,
  ChevronRight,
  Info,
  Menu,
  X,
  TestTube,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import BackButton from "./BackButton";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useAuth } from "@/contexts/AuthContext";
import { showLoginFirstToast } from "@/utils/toast";

const navItems = [
  { icon: <Home size={18} />, label: "Home", href: "/" },
  { icon: <Settings size={18} />, label: "Marketplace", href: "/advancePage" },
  { icon: <TestTube size={18} />, label: "Devnet", href: "/devnet" },
  { icon: <PlusCircle size={18} />, label: "Create", href: "/createCoin" },
  {
    icon: <LifeBuoy size={18} />,
    label: "Paper History",
    href: "/user/paperhistory",
  },
];

const SidebarContent = ({ isMobile, collapsed }) => {
  const pathname = usePathname();
  const router = useRouter();
  const { user } = useAuth();
  return (
    <ul
      className={
        isMobile ? "flex justify-around items-center" : "flex-col space-y-1"
      }
    >
      {navItems.map((item) => {
        const isActive = pathname === item.href;
        const isPublic = item.href === "/" || item.href === "/advancePage";
        // Special handling for Market (advancePage)
        if (item.label === "Marketplace") {
          if (isMobile) {
            return (
              <li key={item.href} className={isMobile ? "flex-1" : "w-full"}>
                <button
                  type="button"
                  onClick={() => {
                    router.push('/advancePage');
                  }}
                  className={`flex items-center gap-3 px-3 py-2 rounded-lg w-full text-left ${
                    isMobile
                      ? "flex-col justify-center text-[11px] hover:bg-transparent"
                      : "hover:bg-white/5"
                  } ${
                    isActive
                      ? "text-purple-400 font-medium"
                      : "text-gray-400 hover:text-gray-200"
                  }`}
                  aria-label={item.label}
                >
                  {React.cloneElement(item.icon, {
                    size: isMobile ? 20 : 18,
                    className: "shrink-0",
                  })}
                  {!collapsed && (
                    <span
                      className={
                        isMobile
                          ? "text-center truncate max-w-[64px]"
                          : "truncate"
                      }
                    >
                      {item.label}
                    </span>
                  )}
                </button>
              </li>
            );
          } else {
            return (
              <li key={item.href} className={isMobile ? "flex-1" : "w-full"}>
                <a
                  href="/advancePage"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`flex items-center gap-3 px-3 py-2 rounded-lg ${
                    isMobile
                      ? "flex-col justify-center text-[11px] hover:bg-transparent"
                      : "hover:bg-white/5"
                  } ${
                    isActive
                      ? "text-purple-400 font-medium"
                      : "text-gray-400 hover:text-gray-200"
                  }`}
                  aria-label={item.label}
                  onClick={e => {
                    // Removed login check for paper history, devnet, and create token
                  }}
                >
                  {React.cloneElement(item.icon, {
                    size: isMobile ? 20 : 18,
                    className: "shrink-0",
                  })}
                  {!collapsed && (
                    <span
                      className={
                        isMobile
                          ? "text-center truncate max-w-[64px]"
                          : "truncate"
                      }
                    >
                      {item.label}
                    </span>
                  )}
                </a>
              </li>
            );
          }
        }
        // Default nav item
        return (
          <li key={item.href} className={isMobile ? "flex-1" : "w-full"}>
            <Link
              href={item.href}
              className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-colors duration-200 ${
                isMobile
                  ? "flex-col justify-center text-[11px] hover:bg-transparent"
                  : "hover:bg-white/5"
              } ${
                isActive
                  ? "text-purple-400 font-medium bg-white/5"
                  : "text-gray-400 hover:text-gray-200"
              }`}
              aria-label={item.label}
              onClick={e => {
                // Removed login check for paper history, devnet, and create token
              }}
            >
              <span className="relative group">
                {React.cloneElement(item.icon, {
                  size: isMobile ? 20 : 18,
                  className: "shrink-0",
                })}
                {collapsed && !isMobile && (
                  <span className="absolute left-full ml-2 top-1/2 -translate-y-1/2 bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 pointer-events-none shadow-lg z-50 whitespace-nowrap transition-opacity duration-200">
                    {item.label}
                  </span>
                )}
              </span>
              {!collapsed && (
                <span
                  className={
                    isMobile ? "text-center truncate max-w-[64px]" : "truncate"
                  }
                >
                  {item.label}
                </span>
              )}
            </Link>
          </li>
        );
      })}
    </ul>
  );
};

// Helper function to get image URL from backend path
const getImageUrl = (mediaPath) => {
  if (!mediaPath) return "/king.png"; // fallback
  if (mediaPath.startsWith("http") || mediaPath.startsWith("data:"))
    return mediaPath;

  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
  if (!backendUrl) {
    console.warn("NEXT_PUBLIC_BACKEND_URL is not defined.");
    return "/king.png";
  }

  // Normalize Windows-style paths
  const normalizedPath = mediaPath.replace(/\\/g, "/");

  // Remove /api from backend URL if present since uploads are served at root level
  const baseUrl = backendUrl.replace(/\/api$/, "");

  // Combine the base URL and the path properly
  const finalPath = baseUrl.endsWith("/")
    ? normalizedPath.replace(/^\//, "")
    : `/${normalizedPath.replace(/^\//, "")}`;

  return `${baseUrl}${finalPath}`;
};

const ProfileSection = ({ collapsed }) => {
  const { user } = useAuth();
  const username = user?.username || user?.email?.split("@")[0] || "User";
  const profileImage = getImageUrl(user?.profileImage);
  return (
    <div
      className={`flex items-center gap-3 p-3 border-t border-white/10 transition-all duration-300 ${
        collapsed ? "justify-center" : ""
      }`}
    >
      <Avatar>
        <AvatarImage src={profileImage} alt={username} />
        <AvatarFallback>{username[0]}</AvatarFallback>
      </Avatar>
      {!collapsed && (
        <span className="font-medium truncate max-w-[120px]">{username}</span>
      )}
    </div>
  );
};

const SideBar = ({ collapsed, setCollapsed }) => {
  const pathname = usePathname();
  const isAdvancePage = pathname === "/advancePage";
  const { user } = useAuth();

  if (isAdvancePage) {
    return null;
  }

  return (
    <>
      {/* Mobile bottom navigation (visible on all pages except /advancePage, hidden on md+) */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 z-50 h-[56px] bg-black/95 backdrop-blur-sm border-t border-white/10">
        <SidebarContent isMobile={true} collapsed={false} />
      </nav>

      {/* Desktop sidebar (collapsible) */}
      <aside
        className={`hidden md:flex flex-col fixed left-0 top-0 h-screen z-40 bg-black border-r border-white/10 shadow-lg transition-all duration-300 ${
          collapsed ? "w-[70px]" : "w-[250px] lg:w-[300px]"
        }`}
        aria-label="Sidebar"
      >
        <div className="flex flex-col h-full">
          {/* Top section: logo/title and BackButton (expanded only) */}
          <div>
            <div className="flex h-16 items-center px-5 border-b border-white/10 justify-between">
              {!collapsed && (
                <span className="text-xl font-bold">DexPoolParty</span>
              )}
              <button
                className="ml-auto p-2 rounded hover:bg-white/10 transition-colors"
                onClick={() => setCollapsed((c) => !c)}
                aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {collapsed ? <ChevronRight size={22} /> : <Menu size={22} />}
              </button>
            </div>
          </div>
          <nav className="flex-1 p-3 overflow-y-auto hide-scrollbar">
            <SidebarContent isMobile={false} collapsed={collapsed} />
          </nav>
          {user && <ProfileSection collapsed={collapsed} />}
        </div>
      </aside>
    </>
  );
};

export default SideBar;
