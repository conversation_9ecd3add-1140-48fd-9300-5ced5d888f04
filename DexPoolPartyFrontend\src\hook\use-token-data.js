"use client"

import { useState, useEffect, useCallback, useRef } from "react"

export function useTokenData() {
  const [tokens, setTokens] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [lastUpdated, setLastUpdated] = useState(null)
  const [platformStatus, setPlatformStatus] = useState({
    pumpfun: { loading: false, error: null, lastFetch: null },
    launchpad: { loading: false, error: null, lastFetch: null },
    boopfun: { loading: false, error: null, lastFetch: null },
    bonk: { loading: false, error: null, lastFetch: null },
  })

  const abortControllerRef = useRef(null)
  const intervalRef = useRef(null)

  const fetchPlatformData = useCallback(async (platform, signal) => {
    const endpoints = {
      pumpfun: "/api/pumpfuncoins?limit=50",
      launchpad: "/api/launchpadcoins?limit=50",
      boopfun: "/api/boopfuncoins?limit=50",
      bonk: "/api/bonkcoins?limit=50",
    }

    try {
      setPlatformStatus((prev) => ({
        ...prev,
        [platform]: { ...prev[platform], loading: true, error: null },
      }))

      const response = await fetch(endpoints[platform], {
        signal,
        headers: {
          "Cache-Control": "no-cache",
        },
      })

      if (!response.ok) {
        throw new Error(`${platform} API returned ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()

      // Handle API errors gracefully
      if (!result.success) {
        throw new Error(result.error || `Invalid response from ${platform}`)
      }

      const platformTokens = (result.data || []).map((token) => ({
        ...token,
        platform: platform === "pumpfun" ? "pump.fun" : platform,
        isPumpFun: platform === "pumpfun",
        isLaunchpad: platform === "launchpad",
        isBoopFun: platform === "boopfun",
        isBonk: platform === "bonk",
        dataSource: platform,
        fetchedAt: new Date().toISOString(),
      }))

      setPlatformStatus((prev) => ({
        ...prev,
        [platform]: {
          loading: false,
          error: null,
          lastFetch: new Date().toISOString(),
        },
      }))

      return platformTokens
    } catch (err) {
      if (err.name === "AbortError") {
        return []
      }

      console.error(`Error fetching ${platform} data:`, err)

      setPlatformStatus((prev) => ({
        ...prev,
        [platform]: {
          loading: false,
          error: err.message,
          lastFetch: prev[platform].lastFetch,
        },
      }))

      return []
    }
  }, [])

  const fetchAllTokens = useCallback(async () => {
    setLoading(true)
    try {
      setError(null)

      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      abortControllerRef.current = new AbortController()
      const signal = abortControllerRef.current.signal

      const [pumpfunTokens, launchpadTokens, boopfunTokens, bonkTokens] = await Promise.all([
        fetchPlatformData("pumpfun", signal),
        fetchPlatformData("launchpad", signal),
        fetchPlatformData("boopfun", signal),
        fetchPlatformData("bonk", signal),
      ])

      const allTokens = [...pumpfunTokens, ...launchpadTokens, ...boopfunTokens, ...bonkTokens]

      // Remove duplicates based on mintAddress
      const uniqueTokens = allTokens.reduce((acc, current) => {
        const existing = acc.find((token) => token.mintAddress === current.mintAddress)

        if (!existing) {
          acc.push(current)
        } else {
          const currentTime = new Date(current.createdAt || 0).getTime()
          const existingTime = new Date(existing.createdAt || 0).getTime()

          if (currentTime > existingTime) {
            const index = acc.indexOf(existing)
            acc[index] = current
          }
        }
        return acc
      }, [])

      // Sort by creation time (newest first)
      uniqueTokens.sort((a, b) => {
        const timeA = new Date(a.createdAt || 0).getTime()
        const timeB = new Date(b.createdAt || 0).getTime()
        return timeB - timeA
      })

      setTokens(uniqueTokens)
      setLastUpdated(new Date().toISOString())

      // Check if we have any platform errors
      const platformErrors = Object.entries(platformStatus)
        .filter(([_, status]) => status.error)
        .map(([platform, status]) => `${platform}: ${status.error}`)

      if (platformErrors.length > 0 && uniqueTokens.length === 0) {
        setError(`All platforms failed: ${platformErrors.join(", ")}`)
      } else if (platformErrors.length > 0) {
        setError(`Partial data loaded. Issues with: ${platformErrors.join(", ")}`)
      }

      console.log(`Loaded ${uniqueTokens.length} unique tokens from ${allTokens.length} total records`)
    } catch (err) {
      if (err.name !== "AbortError") {
        setError(err.message || "Failed to fetch token data")
        console.error("Error fetching tokens:", err)
      }
    } finally {
      setLoading(false)
    }
  }, [fetchPlatformData, platformStatus])

  const refetch = useCallback(() => {
    fetchAllTokens()
  }, [fetchAllTokens])

  useEffect(() => {
    fetchAllTokens()

    intervalRef.current = setInterval(() => {
      if (!loading) {
        fetchAllTokens()
      }
    }, 120000) // 2 minutes

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    tokens,
    loading,
    error,
    lastUpdated,
    refetch,
    platformStatus,
  }
}
