# Frontend Authentication Logging Guide

## Overview
This guide helps you test and debug the frontend authentication flow with comprehensive console logging. The frontend now includes detailed logging for all authentication operations, API calls, and token management.

## 🔧 Enhanced Frontend Features

### 1. AuthContext Logging
The `AuthContext` now provides detailed logging for:
- **Authentication state changes** (login, logout, token storage)
- **localStorage operations** (token storage/retrieval)
- **Wallet connection/disconnection**
- **Session restoration** on page load

### 2. API Service Logging
The enhanced API service includes:
- **Request/response tracking** for all API calls
- **Bearer token management** (automatic header addition)
- **Error handling** with detailed logging
- **Token validation** and storage operations

### 3. Login Component Logging
The `LoginModal` component now logs:
- **Authentication flow steps** (Firebase → Backend → Storage)
- **Token generation and storage**
- **Error handling** with specific error types

## 🚀 Testing the Frontend Authentication Flow

### Step 1: Open Browser Developer Tools
1. Open your frontend application
2. Press `F12` or right-click → "Inspect"
3. Go to the **Console** tab
4. Clear the console for clean logs

### Step 2: Test Authentication Flow

#### Option A: Google OAuth Login
1. **Click "Login"** in your app
2. **Click "Continue with Google"**
3. **Watch the console logs:**

```
🔐 [AUTH] INITIALIZE_AUTH_CONTEXT
🔐 [AUTH] LOADING_STORED_DATA { hasStoredUser: false, hasStoredToken: false }
🔐 [AUTH] NO_STORED_SESSION { reason: 'No user data' }
🔐 [AUTH] AUTH_CONTEXT_READY

🔐 [LOGIN] Starting Google OAuth login process
🔐 [LOGIN] Disconnected wallet for Google login
🔐 [LOGIN] Calling Firebase signInWithGoogle...
🔐 [LOGIN] Firebase Google sign-in successful, got ID token
🔐 [LOGIN] Sending ID token to backend for JWT generation...
🔐 [LOGIN] Backend response status: 200
🔐 [LOGIN] Backend response data: { success: true, hasToken: true, hasUser: true }
🔐 [LOGIN] Backend authentication successful, storing tokens...
🔐 [LOGIN] Tokens stored in localStorage, updating auth context...

🔐 [AUTH] LOGIN_ATTEMPT { userData: { id: 'user_id', email: '<EMAIL>' }, hasToken: true }
🔐 [AUTH] USER_DATA_STORED { user: { id: 'user_id', email: '<EMAIL>' } }
🔐 [AUTH] AUTH_TOKEN_STORED { tokenLength: 123, tokenPreview: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
🔐 [AUTH] LOGIN_SUCCESS { user: { id: 'user_id', email: '<EMAIL>' }, sessionActive: true }

🔐 [LOGIN] Google login completed successfully
```

#### Option B: Apple OAuth Login
1. **Click "Continue with Apple"**
2. **Watch similar logs** with Apple-specific details

### Step 3: Test API Calls with Bearer Token

After successful login, test API calls:

```javascript
// In browser console, test API calls
const { tokenService } = await import('./src/services/api.js');

// This will automatically include the bearer token
const tokens = await tokenService.getAllTokens();
```

**Expected API logs:**
```
🌐 [API] GET_AUTH_TOKEN { hasToken: true, tokenPreview: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
🌐 [API] API_REQUEST { url: 'http://localhost:4000/api/tokens', method: 'GET', hasAuthHeader: false, hasToken: true }
🌐 [API] AUTH_HEADER_ADDED { tokenPreview: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
🌐 [API] API_RESPONSE { status: 200, statusText: 'OK', hasAuthHeader: true }
🌐 [API] GET_ALL_TOKENS_RESPONSE { success: true, tokenCount: 5 }
```

### Step 4: Test Logout Flow

1. **Click "Logout"** in your app
2. **Watch the logout logs:**

```
🔐 [AUTH] LOGOUT_ATTEMPT { currentUser: { id: 'user_id', email: '<EMAIL>' }, walletConnected: false }
🔐 [AUTH] WALLET_DISCONNECT_ATTEMPT { currentWallet: null }
🔐 [AUTH] WALLET_DISCONNECT_SUCCESS
🔐 [AUTH] LOGOUT_SUCCESS { clearedItems: ['user', 'authToken', 'firebaseToken'], sessionCleared: true }
```

## 📊 Console Log Categories

### 🔐 [AUTH] - Authentication Context
- `INITIALIZE_AUTH_CONTEXT` - Context initialization
- `LOADING_STORED_DATA` - Loading from localStorage
- `RESTORED_USER_SESSION` - Session restoration
- `LOGIN_ATTEMPT/SUCCESS/ERROR` - Login operations
- `LOGOUT_ATTEMPT/SUCCESS/ERROR` - Logout operations
- `TOKEN_STORED` - Token storage operations
- `WALLET_CONNECT/DISCONNECT` - Wallet operations

### 🌐 [API] - API Service
- `GET_AUTH_TOKEN` - Token retrieval
- `API_REQUEST` - Outgoing requests
- `AUTH_HEADER_ADDED` - Bearer token addition
- `API_RESPONSE` - Response received
- `API_ERROR` - Request failures
- `CREATE_TOKEN_ATTEMPT/RESPONSE` - Specific API operations

### 🔐 [LOGIN] - Login Component
- `Starting Google/Apple OAuth login process`
- `Firebase sign-in successful`
- `Sending ID token to backend`
- `Backend authentication successful`
- `Tokens stored in localStorage`

## 🛠️ Troubleshooting Common Issues

### 1. "No token found" in API calls
**Symptoms:** API calls fail with 401 errors
**Check:**
```javascript
// In browser console
console.log('Stored token:', localStorage.getItem('authToken'));
```
**Solution:** Ensure login completed successfully and token was stored

### 2. "Invalid token" errors
**Symptoms:** Backend returns 401 with "Token is not valid"
**Check:**
```javascript
// Check token format
const token = localStorage.getItem('authToken');
console.log('Token format:', token ? `${token.substring(0, 50)}...` : 'No token');
```
**Solution:** Token might be expired or corrupted - try logging in again

### 3. "Authorization header not found"
**Symptoms:** Backend logs show no Authorization header
**Check:** API service logs should show `AUTH_HEADER_ADDED`
**Solution:** Ensure `apiFetch` is being used instead of regular `fetch`

### 4. Firebase authentication fails
**Symptoms:** No Firebase ID token received
**Check:** Firebase configuration and popup blockers
**Solution:** Check Firebase config and allow popups

## 🔍 Debugging Tips

### 1. Monitor Token Flow
```javascript
// In browser console, monitor token changes
const originalSetItem = localStorage.setItem;
localStorage.setItem = function(key, value) {
  if (key === 'authToken') {
    console.log('🔍 Token stored:', value.substring(0, 20) + '...');
  }
  originalSetItem.apply(this, arguments);
};
```

### 2. Check API Headers
```javascript
// Monitor all fetch requests
const originalFetch = window.fetch;
window.fetch = function(...args) {
  console.log('🔍 Fetch request:', {
    url: args[0],
    headers: args[1]?.headers,
    method: args[1]?.method
  });
  return originalFetch.apply(this, args);
};
```

### 3. Validate Token Structure
```javascript
// Decode JWT token (client-side, for debugging only)
function decodeToken(token) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => 
      '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
    ).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    return null;
  }
}

// Usage
const token = localStorage.getItem('authToken');
const decoded = decodeToken(token);
console.log('Token payload:', decoded);
```

## 📱 Testing on Different Devices

### Mobile Testing
1. **Use browser developer tools** for mobile simulation
2. **Test popup behavior** for OAuth flows
3. **Check localStorage persistence** across app restarts

### Desktop Testing
1. **Test multiple browser tabs** for session sharing
2. **Test browser refresh** for session restoration
3. **Test incognito mode** for clean state testing

## 🔐 Security Considerations

1. **Token Storage:** Tokens are stored in localStorage (consider httpOnly cookies for production)
2. **Token Expiration:** JWT tokens expire after 7 days
3. **HTTPS:** Always use HTTPS in production
4. **Token Validation:** Backend validates tokens on each request

## 📞 Support

If you encounter issues:
1. **Check console logs** for detailed error information
2. **Verify Firebase configuration** is correct
3. **Ensure backend is running** and accessible
4. **Check network tab** for failed requests
5. **Validate token format** and expiration

The enhanced logging will help you identify exactly where the authentication flow is failing and provide detailed information for debugging. 