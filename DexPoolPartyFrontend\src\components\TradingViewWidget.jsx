import React, { useEffect, useRef } from 'react';

const TradingViewWidget = ({ symbol = 'BTCUSD' }) => {
  const container = useRef(null);

  useEffect(() => {
    if (!container.current) return;
    // Remove any previous widget
    container.current.innerHTML = '';
    const script = document.createElement('script');
    script.src = 'https://s3.tradingview.com/tv.js';
    script.async = true;
    script.onload = () => {
      if (window.TradingView) {
        new window.TradingView.widget({
          autosize: true,
          symbol: `BINANCE:${symbol}`,
          interval: '30',
          timezone: 'Etc/UTC',
          theme: 'dark',
          style: '1',
          locale: 'en',
          toolbar_bg: '#181a20',
          enable_publishing: false,
          hide_top_toolbar: false,
          hide_legend: false,
          container_id: container.current.id,
        });
      }
    };
    document.body.appendChild(script);
    return () => {
      // Clean up
      if (container.current) container.current.innerHTML = '';
    };
  }, [symbol]);

  return (
    <div
      id="tradingview-widget-container"
      ref={container}
      style={{ width: '100%', height: 400, minHeight: 300 }}
    />
  );
};

export default TradingViewWidget; 