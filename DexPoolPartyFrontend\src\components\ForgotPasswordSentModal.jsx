import React, { useState } from 'react';

export default function ForgotPasswordSentModal({ onClose, onSendEmail, onGoogleLogin, onAppleLogin, onPhantomLogin }) {
  const [step, setStep] = useState(1); // 1: input, 2: sent
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [sentEmail, setSentEmail] = useState("");

  const handleSend = async (e) => {
    e.preventDefault();
    setError("");
    if (!email || !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      setError("Please enter a valid email address.");
      return;
    }
    setLoading(true);
    try {
      await onSendEmail(email);
      setSentEmail(email);
      setStep(2);
    } catch (err) {
      setError(err.message || "Failed to send reset email.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <div className="bg-[#1a1a1f] p-8 rounded-2xl shadow-2xl border border-[#2a2a2f] max-w-sm w-full flex flex-col items-center">
        {step === 1 ? (
          <>
            <h2 className="text-xl font-bold text-white mb-4">Forgot Password</h2>
            <form onSubmit={handleSend} className="w-full flex flex-col items-center">
              <input
                type="email"
                className="mb-4 px-4 py-2 rounded bg-[#23272f] text-white w-full border border-[#2a2a2f] focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                disabled={loading}
                autoFocus
              />
              {error && <div className="text-red-400 text-sm mb-2">{error}</div>}
              <button
                type="submit"
                className="px-6 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold text-base border-none transition-all duration-200 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 mb-2 w-full"
                disabled={loading}
              >
                {loading ? 'Sending...' : 'Send Reset Link'}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="text-gray-400 hover:text-white text-sm mt-2"
                disabled={loading}
              >
                Cancel
              </button>
            </form>
          </>
        ) : (
          <>
            <h2 className="text-xl font-bold text-white mb-4">Check Your Email</h2>
            <p className="text-gray-300 text-center mb-4">
              A password reset link has been sent to:
            </p>
            <div className="mb-6 px-4 py-2 rounded bg-[#23272f] text-blue-400 font-mono text-center break-all">
              {sentEmail}
            </div>
            <button
              onClick={onClose}
              className="px-6 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold text-base border-none transition-all duration-200 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Close
            </button>
          </>
        )}
      </div>
    </div>
  );
} 