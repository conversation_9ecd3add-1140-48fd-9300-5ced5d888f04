import React, { useState } from "react";

const ReplyModal = ({ visible, onClose, onSubmit }) => {
  const [replyText, setReplyText] = useState("");
  const [image, setImage] = useState(null);

  const handleFileChange = (e) => {
    setImage(e.target.files[0]);
  };

  const handleSubmit = () => {
    onSubmit({ text: replyText, image });
    setReplyText("");
    setImage(null);
  };

  if (!visible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-[#2a2a3b] text-white p-6 rounded-lg w-full max-w-lg shadow-lg">
        <h2 className="text-lg font-semibold mb-4">Add a comment</h2>
        <textarea
          className="w-full h-24 p-2 rounded-md border border-slate-200 bg-[#2a2a3b] mb-2"
          placeholder="comment"
          maxLength="2000"
          value={replyText}
          onChange={(e) => setReplyText(e.target.value)}
        />
        <div className="mb-4">
          <label className="text-sm font-semibold block mb-1" htmlFor="image">
            Image (optional)
          </label>
          <input
            id="image"
            accept="image/*"
            className="w-full p-2 rounded-md border border-slate-200 bg-[#2a2a3b]"
            type="file"
            onChange={handleFileChange}
          />
        </div>
        <button
          onClick={handleSubmit}
          className="w-full bg-green-400 hover:bg-green-200 text-black py-2 rounded-md font-medium mb-2"
        >
          Post Reply
        </button>
        <div
          onClick={onClose}
          className="text-center cursor-pointer text-slate-50 hover:font-bold"
        >
          [cancel]
        </div>
      </div>
    </div>
  );
};

export default ReplyModal;
