import { initializeApp } from "firebase/app"
import {
  get<PERSON><PERSON>,
  GoogleAuthProvider,
  OAuthProvider,
  signInWithPopup,
  signInWithCredential,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,

  signOut,
  onAuthStateChanged,
  sendEmailVerification,
} from "firebase/auth"

// const firebaseConfig = {
//   apiKey: "AIzaSyCNhY8ve-NSgu2xhSfSnO6OSJ4u0AbQf1A",
//   authDomain: "chat-app-f0542.firebaseapp.com",
//   databaseURL: "https://chat-app-f0542-default-rtdb.firebaseio.com",
//   projectId: "chat-app-f0542",
//   storageBucket: "chat-app-f0542.appspot.com",
//   messagingSenderId: "406029403575",
//   appId: "1:406029403575:web:981d4439cba819de915432",
//   measurementId: "G-7C2215WQEW"
// };

const firebaseConfig = {
  apiKey: "AIzaSyCkk1o3M0Tu92Wl-H15aRxYpld1n8oaaOA",
  authDomain: "dex-pool-party.firebaseapp.com",
  databaseURL: "https://dex-pool-party-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "dex-pool-party",
  storageBucket: "dex-pool-party.firebasestorage.app",
  messagingSenderId: "334114720230",
  appId: "1:334114720230:web:0d94eef33bafcab38f6e27"
};

const app = initializeApp(firebaseConfig)
export const auth = getAuth(app)
const googleProvider = new GoogleAuthProvider()
const appleProvider = new OAuthProvider("apple.com")

// Configure Apple provider
appleProvider.addScope('email')
appleProvider.addScope('name')

// Generate nonce for Apple Sign-In (following Firebase documentation)
const generateNonce = (length) => {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let nonce = '';
  for (let i = 0; i < length; i++) {
    nonce += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return nonce;
};

// Enhanced authentication functions with backend sync
export const signInWithGoogle = async () => {
  try {
    const result = await signInWithPopup(auth, googleProvider)
    const user = result.user

    // Get Firebase ID token
    const idToken = await user.getIdToken()

    return { user, idToken }
  } catch (error) {
    console.error("Error signing in with Google", error)
    throw error
  }
}

// Updated Apple Sign-In implementation following Firebase documentation
export const signInWithApple = async () => {
  try {
    // Generate nonce for security
    const unhashedNonce = generateNonce(10);

    // Store nonce in session storage for verification
    sessionStorage.setItem('apple_nonce', unhashedNonce);

    // Configure Apple provider with nonce
    appleProvider.setCustomParameters({
      nonce: unhashedNonce
    });

    const result = await signInWithPopup(auth, appleProvider)
    const user = result.user

    // Get Firebase ID token
    const idToken = await user.getIdToken()

    // Clear nonce from session storage
    sessionStorage.removeItem('apple_nonce');

    return { user, idToken }
  } catch (error) {
    console.error("Error signing in with Apple", error)

    // Clear nonce on error
    sessionStorage.removeItem('apple_nonce');

    // Handle specific Apple Sign-In errors
    if (error.code === 'auth/missing-or-invalid-nonce') {
      throw new Error('Apple Sign-In security validation failed. Please try again.');
    } else if (error.code === 'auth/operation-not-allowed') {
      throw new Error('Apple Sign-In is not enabled for this project.');
    } else if (error.code === 'auth/popup-closed-by-user') {
      throw new Error('Sign-In popup was closed. Please try again.');
    } else if (error.code === 'auth/popup-blocked') {
      throw new Error('Sign-In popup was blocked. Please allow popups for this site.');
    } else if (error.code === 'auth/unauthorized-domain') {
      throw new Error('This domain is not authorized for Apple Sign-In.');
    }

    throw error
  }
}

// Alternative Apple Sign-In method using credential (for server-side token handling)
export const signInWithAppleCredential = async (appleIdToken, rawNonce) => {
  try {
    const credential = appleProvider.credential({
      idToken: appleIdToken,
      rawNonce: rawNonce,
    });

    const result = await signInWithCredential(auth, credential);
    const user = result.user;
    const idToken = await user.getIdToken();

    return { user, idToken };
  } catch (error) {
    console.error("Error signing in with Apple credential", error);
    throw error;
  }
}

export const signInWithEmail = async (email, password) => {
  try {
    const result = await signInWithEmailAndPassword(auth, email, password)
    const user = result.user

    // Get Firebase ID token
    const idToken = await user.getIdToken()

    return { user, idToken }
  } catch (error) {
    console.error("Error signing in with email", error)
    throw error
  }
}

export const signUpWithEmail = async (email, password) => {
  try {
    const result = await createUserWithEmailAndPassword(auth, email, password)
    const user = result.user

    // Do NOT send email verification here; backend will handle it
    await sendEmailVerification(user)

    // Get Firebase ID token
    const idToken = await user.getIdToken()

    return { user, idToken }
  } catch (error) {
    console.error("Error signing up with email", error)
    throw error
  }
}

export const resendVerificationEmail = async () => {
  try {
    const user = auth.currentUser;

    if (!user) {
      throw new Error("No authenticated user found.");
    }

    if (user.emailVerified) {
      throw new Error("Email is already verified.");
    }

    await sendEmailVerification(user);
    console.log("📧 Verification email resent successfully.");

    return { success: true, message: "Verification email sent." };
  } catch (error) {
    console.error("❌ Error resending verification email:", error);
    throw error;
  }
};

export const logout = async () => {
  try {
    await signOut(auth)
  } catch (error) {
    console.error("Error signing out", error)
    throw error
  }
}

// Helper function to get current user's ID token
export const getCurrentUserToken = async () => {
  const user = auth.currentUser
  if (user) {
    return await user.getIdToken()
  }
  return null
}

// Auth state listener
export const onAuthStateChange = (callback) => {
  return onAuthStateChanged(auth, callback)
}
