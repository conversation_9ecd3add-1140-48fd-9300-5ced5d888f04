"use client";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { getAuth, confirmPasswordReset } from "firebase/auth";
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function ResetPasswordPage() {
  const [password, setPassword] = useState("");
  const [confirm, setConfirm] = useState("");
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const params = useSearchParams();
  const oobCode = params.get("oobCode");
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    if (password !== confirm) {
      setError("Passwords do not match");
      return;
    }
    try {
      const auth = getAuth();
      await confirmPasswordReset(auth, oobCode, password);
      setSuccess(true);
    } catch (err) {
      setError("This password reset link is invalid or has expired. Please request a new one.");
    }
  };

  // Auto-redirect to login after success
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        router.push("/?isModalOpen=true");
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [success, router]);

  // Optional: auto-redirect to forgot-password after error (commented out)
  // useEffect(() => {
  //   if (error) {
  //     const timer = setTimeout(() => {
  //       router.push('/forgot-password');
  //     }, 4000);
  //     return () => clearTimeout(timer);
  //   }
  // }, [error, router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen" style={{ background: '#111115' }}>
      <Card className="w-full max-w-md" style={{ background: '#2A2A2A' }}>
        <CardHeader>
          <CardTitle className="text-2xl text-white">Reset Password</CardTitle>
        </CardHeader>
        <CardContent>
          {success ? (
            <div className="flex flex-col gap-4 items-center">
              <p className="text-primary text-center">Password reset successful! Redirecting to login...</p>
              <Button className="w-full" style={{ background: '#C27AFF', color: '#fff' }} onClick={() => router.push('/?isModalOpen=true')}>Go to Login</Button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                type="password"
                placeholder="New password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="text-white"
              />
              <Input
                type="password"
                placeholder="Confirm new password"
                value={confirm}
                onChange={(e) => setConfirm(e.target.value)}
                required  
                className="text-white"
              />
              <Button type="submit" className="w-full" style={{ background: '#C27AFF', color: '#fff' }}>Reset Password</Button>
            </form>
          )}
          {error && (
            <div className="mt-4 text-center">
              <p className="text-destructive mb-2">{error}</p>
              <Button
                variant="secondary"
                onClick={() => router.push('/forgot-password')}
              >
                Request New Reset Link
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 