"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { showToast } from "@/utils/toast";
import { useAuth } from "@/contexts/AuthContext";
import { tokenService } from "@/services/api";
import VerifyModal from "@/components/VerifyModel";
import EditProfileModal from "@/components/EditProfileModal";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

const getImageUrl = (mediaPath) => {
  if (!mediaPath) return "/king.png";
  if (mediaPath.startsWith("http") || mediaPath.startsWith("data:"))
    return mediaPath;

  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
  if (!backendUrl) {
    console.warn("NEXT_PUBLIC_BACKEND_URL is not defined.");
    return "/king.png";
  }

  // Normalize Windows-style paths
  const normalizedPath = mediaPath.replace(/\\/g, "/");

  // Remove /api from backend URL if present since uploads are served at root level
  const baseUrl = backendUrl.replace(/\/api$/, "");

  // Combine the base URL and the path properly
  const finalPath = baseUrl.endsWith("/")
    ? normalizedPath.replace(/^\//, "")
    : `/${normalizedPath.replace(/^\//, "")}`;

  return `${baseUrl}${finalPath}`;
};

const suggestedPeople = [
  {
    username: "",
    address: "2YqgUGrE2TCUBmkgAC3yj6G4m77e2p7gofPzGi8u85bj",
    followers: 3043,
    image: "",
  },
  {
    username: "tylerbrown",
    followers: 3028,
    image: "",
  },
  {
    username: "oxynd3v",
    followers: 3003,
    image: "",
  },
  {
    username: "AutoSnipe",
    followers: 3001,
  },
  {
    username: "triplej",
    followers: 2986,
  },
];

export default function ProfilePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [openModal, setOpenModal] = useState(false);
  const [modalType, setModalType] = useState(false);
  const [userTokens, setUserTokens] = useState([]);
  const [loadingTokens, setLoadingTokens] = useState(true);
  const [openEditModal, setOpenEditModal] = useState(false);

  const followers = 0;
  const following = 0;

  useEffect(() => {
    if (!user) {
      showToast.warning("Please log in first.");
      router.push("/");
      return;
    }
    const fetchTokens = async () => {
      try {
        setLoadingTokens(true);
        const response = await tokenService.getUserTokens(user.id);
        const tokens = response.tokens || [];
        setUserTokens(tokens);
      } catch (error) {
        console.error("Failed to fetch tokens:", error);
        showToast.error("Failed to load created coins.");
        setUserTokens([]);
      } finally {
        setLoadingTokens(false);
      }
    };

    fetchTokens();
  }, [user, router]);

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  const address = user.walletAddress || "unknown";
  const createdCoins = userTokens.length;

  return (
    <main className="relative px-4 w-11/12 mt-[30px] md:px-0">
      <div className="flex flex-col min-h-[calc(100svh-56px)]">
        <div className="mx-auto mt-8 grid w-11/12 items-start gap-4 p-4 text-white">
          <div className="grid grid-cols-1 gap-16 lg:grid-cols-[612px_1fr]">
            {/* Left Column */}
            <div className="flex flex-col  gap-4">
              {/* Profile Header */}
              <div className="flex flex-col md:flex-row items-start  justify-start md:justify-between">
                <div className="flex gap-4">
                  <Image
                    alt="Profile picture"
                    width={93}
                    height={93}
                    className="h-[93px] w-[93px] rounded-full border border-white/20 object-cover"
                    src={getImageUrl(user.profileImage)}
                    src={getImageUrl(user.profileImage)}
                  />
                  <div className="flex flex-col justify-center gap-1">
                    <h1 className="text-xl font-medium">{user.username}</h1>
                    <div className="mt-1 flex flex-col items-center gap-2.5 sm:flex-row">
                      <div className="group flex items-center gap-2 bg-white/5 px-2 py-1 rounded">
                        <span className="text-xs text-gray-400 group-hover:underline">
                          {address.slice(0, 6)}...{address.slice(-4)}
                        </span>
                      </div>
                      <a
                        className="text-xs text-gray-400 hover:underline"
                        href={`https://solscan.io/account/${address}`}
                        target="_blank"
                        rel="noreferrer"
                      >
                        view on solscan
                      </a>
                    </div>
                  </div>
                </div>
                <button
                  className="self-center mt-4 md:mt-0 rounded bg-slate-700 px-7 py-2 text-sm font-bold text-white hover:bg-slate-600"
                  onClick={() => setOpenEditModal(true)}
                >
                  edit
                </button>
              </div>

              {/* Status Banner */}
              {user.emailVerified && !user.isWalletConnected && (
                <div className="mt-2 rounded bg-yellow-800/30 p-3 text-sm text-yellow-200">
                  Your email is verified. Please
                  <button
                    className="underline mx-2 text-white hover:text-green-400"
                    onClick={() => {
                      setModalType("wallet");
                      setOpenModal(true);
                    }}
                  >
                    connect your wallet
                  </button>
                  to get started.
                </div>
              )}
              {!user.emailVerified && (
                <div className="mt-2 rounded bg-red-800/30 p-3 text-sm text-red-200">
                  Please
                  <button
                    className="underline mx-2 text-white hover:text-green-400"
                    onClick={() => {
                      setModalType("email");
                      setOpenModal(true);
                    }}
                  >
                    verify your email
                  </button>
                  to unlock full access.
                </div>
              )}
              {user.emailVerified && user.isWalletConnected && (
                <div className="mt-2 rounded bg-green-800/30 p-3 text-sm text-green-200">
                  All set! You're verified and connected.
                </div>
              )}

              {/* Stats */}
              <div className="flex gap-9 mt-4">
                <div className="flex flex-col">
                  <span className="text-xl font-semibold text-center">
                    {followers}
                  </span>
                  <span className="text-xs text-gray-400">followers</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-semibold text-center">
                    {following}
                  </span>
                  <span className="text-xs text-gray-400">following</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-semibold text-center">
                    {createdCoins}
                  </span>
                  <span className="text-xs text-gray-400">created coins</span>
                </div>
              </div>

              {/* Tabs using shadcn */}
              <Tabs defaultValue="coins" className="w-full mt-4 bg-none">
                <TabsList className="px-2 py-1 space-x-2 bg-[#18181b] rounded-xl flex justify-center">
                  <TabsTrigger
                    className="px-6 py-2 rounded-lg bg-[#23272f] text-[#a1a1aa] data-[state=active]:bg-[#27272a] data-[state=active]:text-[#e5e7eb] data-[state=active]:font-medium transition-colors border-none shadow-none"
                    value="coins"
                  >
                    Coins
                  </TabsTrigger>
                  <TabsTrigger
                    className="px-6 py-2 rounded-lg bg-[#23272f] text-[#a1a1aa] data-[state=active]:bg-[#27272a] data-[state=active]:text-[#e5e7eb] data-[state=active]:font-medium transition-colors border-none shadow-none"
                    value="replies"
                  >
                    Replies
                  </TabsTrigger>
                  <TabsTrigger
                    className="px-6 py-2 rounded-lg bg-[#23272f] text-[#a1a1aa] data-[state=active]:bg-[#27272a] data-[state=active]:text-[#e5e7eb] data-[state=active]:font-medium transition-colors border-none shadow-none"
                    value="notifications"
                  >
                    Notifications
                  </TabsTrigger>
                </TabsList>

                {/* Coins Tab */}
                <TabsContent value="coins">
                  <div className="mt-4 rounded border border-[#37414F] bg-[#121619] p-4 text-white">
                    {loadingTokens ? (
                      <div className="text-center text-gray-400 py-4">
                        Loading created coins...
                      </div>
                    ) : userTokens.length === 0 ? (
                      <div className="text-center text-gray-400 py-4">
                        No created coins found.
                      </div>
                    ) : (
                      <ul className="space-y-3">
                        {userTokens.map((token) => (
                          <li
                            key={token._id}
                            className="flex items-center gap-4 p-3 rounded border border-[#2a2f36] bg-[#1f2227] hover:bg-[#2a2f36] transition"
                          >
                            <div className="relative w-12 h-12 overflow-hidden rounded">
                              <img
                                src={getImageUrl(token.media)}
                                alt={token.name}
                                className="object-cover w-full h-full"
                                onError={(e) => {
                                  e.currentTarget.src = "/king.png";
                                }}
                              />
                            </div>
                            <div className="flex flex-col">
                              <Link
                                href={`/coin/${token._id}`}
                                className="font-semibold hover:underline"
                              >
                                {token.name || "Unnamed Coin"}
                              </Link>
                              <span className="text-xs text-gray-400">
                                {token.symbol} •{" "}
                                {token.status || "Status unknown"}
                              </span>
                            </div>
                            <div className="ml-auto text-right">
                              <div className="font-semibold">
                                ${token.price || "0.00"}
                              </div>
                              <div className="text-xs text-gray-400">
                                {token.createdAt
                                  ? new Date(
                                      token.createdAt
                                    ).toLocaleDateString()
                                  : ""}
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </TabsContent>

                {/* Replies Tab */}
                <TabsContent value="replies">
                  <div className="text-gray-400 text-sm py-8 text-center">
                    No replies yet.
                  </div>
                </TabsContent>

                {/* Notifications Tab */}
                <TabsContent value="notifications">
                  <div className="text-gray-400 text-sm py-8 text-center">
                    No notifications yet.
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Right Column */}
            <div className="hidden max-w-[360px] flex-col gap-4 lg:flex">
              <h3 className="text-sm font-semibold">
                Your coins{" "}
                <span className="text-[#9DA3AE]">({createdCoins})</span>
              </h3>

              {userTokens[0] && (
                <Link href={`/coin/${userTokens[0]._id}`}>
                  <div className="group mb-4 flex items-center gap-4 p-3 rounded border border-[#2a2f36] bg-[#1f2227] hover:bg-[#2a2f36] transition">
                    <div className="relative w-10 h-10 overflow-hidden rounded-full">
                      <img
                        src={getImageUrl(userTokens[0].media)}
                        alt={userTokens[0].name}
                        className="object-cover w-full h-full"
                        onError={(e) => {
                          e.currentTarget.src = "/king.png";
                        }}
                      />
                    </div>
                    <div>
                      <div className="font-semibold">{userTokens[0].name}</div>
                      <div className="text-xs text-gray-400">
                        {userTokens[0].symbol}
                      </div>
                    </div>
                  </div>
                </Link>
              )}
              <div className="hidden max-w-[360px] flex-col gap-4 lg:flex">
                <div className="max-w-[400px]">
                  <h3 className="mb-3 text-sm font-semibold">
                    people you may know
                  </h3>

                  {suggestedPeople.map((person, index) => (
                    <div
                      key={index}
                      className="mb-4 grid w-full min-w-[350px] gap-4 text-sm"
                      style={{ gridTemplateColumns: "auto auto 1fr" }}
                    >
                      <Link
                        href={`/profile/${person.username || person.address}`}
                      >
                        {person.image ? (
                          <Image
                            alt={person.username || "user"}
                            width={40}
                            height={40}
                            className="h-10 w-10 rounded-full object-contain"
                            src="/king.png"
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full border border-[#666] flex items-center justify-center text-[#666] text-xs">
                            No Image
                          </div>
                        )}
                      </Link>
                      <div className="flex flex-col justify-start">
                        <Link
                          href={`/profile/${person.username || person.address}`}
                        >
                          <div className="font-bold">
                            {person.username || ""}
                          </div>
                        </Link>
                        <div className="text-gray-500">
                          {person.followers.toLocaleString()} followers
                        </div>
                      </div>
                      <div className="flex w-full items-center justify-end">
                        <button className="h-6 rounded bg-slate-700 px-[10px] py-1 text-xs font-bold text-white hover:bg-slate-600">
                          follow
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <VerifyModal
        showModal={openModal}
        modalType={modalType}
        closeModal={() => setOpenModal(false)}
        userId={user?.id}
      />
      <EditProfileModal
        showModal={openEditModal}
        closeModal={() => setOpenEditModal(false)}
      />
    </main>
  );
}
