# Apple Sign-In Setup Guide

This guide explains how to set up Apple Sign-In for your Firebase project and the implementation details.

## 🔧 Firebase Console Setup

### 1. Enable Apple Sign-In in Firebase Console

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project (`chat-app-f0542`)
3. Navigate to **Authentication** → **Sign-in method**
4. Click on **Apple** provider
5. Enable Apple Sign-In by toggling the switch
6. Configure the following settings:

### 2. Apple Developer Account Setup

1. Go to [Apple Developer Console](https://developer.apple.com/account/)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Create a new **App ID** or use existing one
4. Enable **Sign In with Apple** capability
5. Create a **Service ID** for your web app
6. Configure the Service ID with your domain

### 3. Firebase Configuration

In Firebase Console, under Apple provider settings:

- **Service ID**: Your Apple Service ID (e.g., `com.yourcompany.yourapp`)
- **Apple Team ID**: Your Apple Developer Team ID
- **Key ID**: Your Apple Sign-In key ID
- **Private Key**: Upload your Apple Sign-In private key file

## 🚀 Implementation Details

### Frontend Implementation

The Apple Sign-In is implemented in `src/lib/firebase.js` with the following features:

1. **Nonce Generation**: Security measure to prevent replay attacks
2. **Error Handling**: Comprehensive error handling for various scenarios
3. **Session Management**: Proper cleanup of session storage
4. **Backend Integration**: Seamless integration with your backend API

### Backend Implementation

The backend handles Apple authentication in `src/controllers/userController.js`:

1. **Token Verification**: Validates Firebase ID tokens
2. **User Sync**: Syncs user data with MongoDB
3. **Error Handling**: Proper error responses for different scenarios
4. **Logging**: Detailed logging for debugging

## 🔍 Error Handling

### Common Firebase Apple Sign-In Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `auth/operation-not-allowed` | Apple Sign-In not enabled | Enable in Firebase Console |
| `auth/popup-closed-by-user` | User closed popup | Inform user to try again |
| `auth/popup-blocked` | Popup blocked by browser | Ask user to allow popups |
| `auth/unauthorized-domain` | Domain not authorized | Add domain to Firebase Console |
| `auth/missing-or-invalid-nonce` | Security validation failed | Retry authentication |
| `auth/network-request-failed` | Network error | Check internet connection |

### Backend Error Responses

| Status Code | Error Type | Description |
|-------------|------------|-------------|
| 400 | Missing Token | Firebase ID token required |
| 401 | Invalid Token | Token expired or invalid |
| 403 | Email Verification | Email needs verification |
| 500 | Server Error | Internal server error |

## 🧪 Testing

### Test Scenarios

1. **Successful Sign-In**: User completes Apple Sign-In flow
2. **Popup Blocked**: Test when popup is blocked by browser
3. **Network Error**: Test with poor internet connection
4. **Token Expired**: Test with expired tokens
5. **Email Verification**: Test with unverified email

### Debug Logging

The implementation includes comprehensive logging:

- Frontend: `🔐 [LOGIN]` prefixed logs
- Backend: `🍎 [APPLE]` prefixed logs

Check browser console and server logs for detailed information.

## 🔒 Security Considerations

1. **Nonce Validation**: Each sign-in attempt uses a unique nonce
2. **Token Verification**: Backend validates Firebase ID tokens
3. **Session Cleanup**: Nonces are cleared after use
4. **HTTPS Required**: Apple Sign-In requires HTTPS in production

## 📱 Production Deployment

### Domain Configuration

1. Add your production domain to Firebase Console
2. Configure Apple Service ID with production domain
3. Update environment variables for production
4. Test Apple Sign-In on production domain

### Environment Variables

```bash
# Required for production
NEXT_PUBLIC_FIREBASE_API_KEY=your_production_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_production_domain

# Optional for advanced setup
NEXT_PUBLIC_APPLE_CLIENT_ID=your.apple.client.id
NEXT_PUBLIC_APPLE_REDIRECT_URI=https://yourdomain.com/auth/apple/callback
```

## 🐛 Troubleshooting

### Common Issues

1. **"Apple Sign-In is not enabled"**
   - Enable Apple provider in Firebase Console
   - Check Apple Developer account configuration

2. **"Domain not authorized"**
   - Add domain to Firebase Console authorized domains
   - Configure Apple Service ID with correct domain

3. **"Popup blocked"**
   - Ask users to allow popups for your site
   - Consider implementing redirect flow for mobile

4. **"Token validation failed"**
   - Check Firebase configuration
   - Verify Apple private key and certificates

### Debug Steps

1. Check browser console for frontend errors
2. Check server logs for backend errors
3. Verify Firebase Console configuration
4. Test with different browsers and devices
5. Check Apple Developer account settings

## 📚 Additional Resources

- [Firebase Apple Sign-In Documentation](https://firebase.google.com/docs/auth/web/apple)
- [Apple Sign-In for Web](https://developer.apple.com/documentation/sign_in_with_apple/sign_in_with_apple_js)
- [Firebase Authentication Best Practices](https://firebase.google.com/docs/auth/web/best-practices)

## 🤝 Support

If you encounter issues:

1. Check the debug logs in browser console and server
2. Verify Firebase Console configuration
3. Test with the provided error handling scenarios
4. Review Apple Developer account settings
5. Contact support with detailed error logs 