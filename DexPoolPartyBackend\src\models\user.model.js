const mongoose = require("mongoose");

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - walletAddress
 *       properties:
 *         walletAddress:
 *           type: string
 *           description: User's blockchain wallet address
 *         isActive:
 *           type: boolean
 *           description: Whether the user account is active
 *           default: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

const userSchema = new mongoose.Schema(
  {
    _id: { type: String, unique: true, required: true },
    walletAddress: { type: String, unique: true, sparse: true },
    email: {
      type: String,
      unique: true,
      sparse: true,
      trim: true,
      lowercase: true,
    },
    username: { type: String },
    profileImage: { type: String },
    bio: { type: String },
    authProvider: {
      type: String,
      enum: ["wallet", "google", "email", "apple"],
      required: true,
    },
    socialLinks: {
      twitter: { type: String },
      telegram: { type: String },
    },
    createdTokens: [{ type: mongoose.Schema.Types.ObjectId, ref: "Token" }],
    favorites: [{ type: mongoose.Schema.Types.ObjectId, ref: "Token" }],
    banned: { type: Boolean, default: false },
    joinDate: { type: Date, default: Date.now },
    lastActive: { type: Date, default: Date.now },
    status: {
      type: String,
      enum: ["active", "inactive", "stopped"],
      default: "active",
    },
    stats: {
      totalSpent: { type: Number, default: 0 },
      totalEarned: { type: Number, default: 0 },
      totalTrades: { type: Number, default: 0 },
    },
    googleId: {
      type: String,
      sparse: true,
    },
    name: {
      type: String,
      trim: true,
    },
    profilePicture: String,
    isWalletConnected: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    otp: {
      code: String,
      expiresAt: Date,
    },
    emailVerified: {
      type: Boolean,
      default: false,
    },
    hasCustomUsername: {
      type: Boolean,
      default: false,
    },
    // Referral System Fields
    referralCode: {
      type: String,
      unique: true,
      sparse: true,
    },
    referredBy: {
      type: String,
      ref: "User",
    },
    referrals: [
      {
        userId: { type: String, ref: "User" },
        joinedAt: { type: Date, default: Date.now },
        totalRewardEarned: { type: Number, default: 0 },
      },
    ],
    rewards: {
      totalEarned: { type: Number, default: 0 },
      availableBalance: { type: Number, default: 0 },
      totalWithdrawn: { type: Number, default: 0 },
      referralEarnings: { type: Number, default: 0 },
      tradeBonus: { type: Number, default: 0 },
      loyaltyBonus: { type: Number, default: 0 },
    },
    paperTradeHistory: [
      {
        _id: false,
        tradeId: { type: mongoose.Schema.Types.ObjectId, auto: true },
        token: {
          name: { type: String, required: true },
          symbol: { type: String, required: true },
          address: { type: String, required: true }, // on-chain address of the token
          image: { type: String }, // logo or image URL
          decimals: { type: Number, default: 18 }, // optional but useful
        },
        buy: {
          quantity: { type: Number },
          price: { type: Number },
          totalValue: { type: Number }, // quantity * price
          timestamp: { type: Date },
        },
        sell: {
          quantity: { type: Number },
          price: { type: Number },
          totalValue: { type: Number }, // quantity * price
          timestamp: { type: Date },
        },
        state: {
          type: String,
          enum: ["pending", "partial", "completed", "cancelled"],
          default: "pending",
        },
      },
    ],
  },

  { timestamps: true }
);

// Generate unique referral code before saving
userSchema.pre("save", async function (next) {
  if (!this.referralCode && this.isNew) {
    this.referralCode = await generateUniqueReferralCode();
  }
  next();
});

async function generateUniqueReferralCode() {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let code;
  let isUnique = false;

  while (!isUnique) {
    code = "";
    for (let i = 0; i < 8; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    const User = mongoose.model("User");
    const existingUser = await User.findOne({ referralCode: code });
    if (!existingUser) {
      isUnique = true;
    }
  }

  return code;
}

module.exports = mongoose.model("User", userSchema);
