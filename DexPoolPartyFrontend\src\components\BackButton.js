'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';

const BackButton = ({ collapsed }) => {
  const router = useRouter();

  const handleBack = () => {
    if (typeof window !== 'undefined' && window.history.length <= 1) {
      router.push('/');
    } else {
      router.back();
    }
  };

  return (
    <button
      onClick={handleBack}
      className="inline-flex items-center gap-2 rounded-lg px-2 py-1.5 sm:px-3 sm:py-2 sm:bg-gray-800/50 hover:bg-gray-700/50 text-sm font-medium text-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500/50"
    >
      <ArrowLeft className="h-4 w-4" />
      {!collapsed && <span className="hidden sm:inline">Back</span>}
    </button>
  );
};

export default BackButton;