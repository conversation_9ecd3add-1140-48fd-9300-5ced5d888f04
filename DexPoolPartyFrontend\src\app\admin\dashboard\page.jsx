"use client";
import React, { useEffect, useState, useContext } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import { Line } from 'chart.js/auto';
import Sidebar from '../Sidebar';
import { API_BASE_URL } from '../../../services/api';

const handleLogout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  window.location.href = '/admin/login';
};

const AdminDashboard = () => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState(null);
  const [error, setError] = useState(null);
  const [loadingStats, setLoadingStats] = useState(true);
  const [chartData, setChartData] = useState(null);
  const [loadingChart, setLoadingChart] = useState(true);
  const [chartError, setChartError] = useState(null);
  const [mostTradedTokens, setMostTradedTokens] = useState(null);
  const [loadingTokens, setLoadingTokens] = useState(true);
  const [tokensError, setTokensError] = useState(null);

  useEffect(() => {
    if (!loading && (!user || user.role !== 'admin' || user.email !== '<EMAIL>')) {
      router.push('/admin/login'); // Redirect non-admins to admin login
    }
  }, [user, loading, router]);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoadingStats(true);
        setError(null);
        const res = await fetch(`${API_BASE_URL}/users/admin/dashboard-stats`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        });
        if (!res.ok) throw new Error('Failed to fetch stats');
        const data = await res.json();
        setStats(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoadingStats(false);
      }
    };
    if (user && user.role === 'admin') fetchStats();
  }, [user]);

  useEffect(() => {
    const fetchChartData = async () => {
      try {
        setLoadingChart(true);
        setChartError(null);
        const res = await fetch(`${API_BASE_URL}/users/admin/user-registrations-per-month`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        });
        if (!res.ok) throw new Error('Failed to fetch chart data');
        const data = await res.json();
        setChartData({
          labels: data.map((d) => d.month),
          datasets: [
            {
              label: 'New Users',
              data: data.map((d) => d.count),
              borderColor: 'rgba(147, 51, 234, 1)',
              backgroundColor: 'rgba(147, 51, 234, 0.2)',
              tension: 0.4,
            },
          ],
        });
      } catch (err) {
        setChartError(err.message);
      } finally {
        setLoadingChart(false);
      }
    };
    if (user && user.role === 'admin') fetchChartData();
  }, [user]);

  useEffect(() => {
    if (typeof window !== 'undefined' && chartData) {
      const ctx = document.getElementById('usersChart');
      if (ctx && window.Chart) {
        new window.Chart(ctx, {
          type: 'line',
          data: chartData,
          options: {
            responsive: true,
            plugins: {
              legend: { 
                position: 'top',
                labels: {
                  color: '#ffffff'
                }
              },
              title: { 
                display: true, 
                text: 'User Registrations Per Month',
                color: '#ffffff'
              },
            },
            scales: {
              x: {
                ticks: {
                  color: '#ffffff'
                },
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              },
              y: {
                ticks: {
                  color: '#ffffff'
                },
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              }
            }
          },
        });
      }
    }
  }, [chartData]);

  // Fetch most traded tokens
  useEffect(() => {
    const fetchTokens = async () => {
      try {
        setLoadingTokens(true);
        setTokensError(null);
        const res = await fetch(`${API_BASE_URL}/users/admin/most-traded-tokens`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        });
        if (!res.ok) throw new Error('Failed to fetch most traded tokens');
        const data = await res.json();
        setMostTradedTokens(data);
      } catch (err) {
        setTokensError(err.message);
      } finally {
        setLoadingTokens(false);
      }
    };
    if (user && user.role === 'admin') fetchTokens();
  }, [user]);

  // Render bar chart for most traded tokens
  useEffect(() => {
    if (typeof window !== 'undefined' && mostTradedTokens) {
      const ctx = document.getElementById('tokensChart');
      if (ctx && window.Chart) {
        new window.Chart(ctx, {
          type: 'bar',
          data: {
            labels: mostTradedTokens.map((t) => `${t.name} (${t.symbol})`),
            datasets: [
              {
                label: 'Trades',
                data: mostTradedTokens.map((t) => t.count),
                backgroundColor: 'rgba(59, 130, 246, 0.6)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 1,
              },
            ],
          },
          options: {
            responsive: true,
            plugins: {
              legend: { 
                display: false,
                labels: {
                  color: '#ffffff'
                }
              },
              title: { 
                display: true, 
                text: 'Top 5 Most Traded Tokens',
                color: '#ffffff'
              },
            },
            scales: {
              x: {
                ticks: {
                  color: '#ffffff'
                },
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              },
              y: {
                ticks: {
                  color: '#ffffff'
                },
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              }
            }
          },
        });
      }
    }
  }, [mostTradedTokens]);

  if (loading || loadingStats) return (
    <div className="min-h-screen bg-[#111115] text-white flex items-center justify-center">
      <div className="text-xl">Loading...</div>
    </div>
  );
  if (error) return (
    <div className="min-h-screen bg-[#111115] text-white flex items-center justify-center">
      <div className="text-xl text-red-400">Error: {error}</div>
    </div>
  );
  if (!stats) return null;

  // Add some icons for the cards
  const cardIcons = {
    users: '👤',
    tokens: '🪙',
    trades: '💱',
    comments: '💬',
    rewards: '🎁',
  };

  const cardColors = {
    users: 'bg-gradient-to-br from-purple-500/20 to-purple-600/20 border-purple-500/30',
    tokens: 'bg-gradient-to-br from-blue-500/20 to-blue-600/20 border-blue-500/30',
    trades: 'bg-gradient-to-br from-green-500/20 to-green-600/20 border-green-500/30',
    comments: 'bg-gradient-to-br from-pink-500/20 to-pink-600/20 border-pink-500/30',
    rewards: 'bg-gradient-to-br from-yellow-500/20 to-yellow-600/20 border-yellow-500/30',
  };

  const cardList = [
    { key: 'users', label: 'Users' },
    { key: 'tokens', label: 'Tokens' },
    { key: 'trades', label: 'Trades' },
    { key: 'comments', label: 'Comments' },
    { key: 'rewards', label: 'Rewards' },
  ];

  return (
    <div className="flex min-h-screen bg-[#111115]">
      <Sidebar onLogout={handleLogout} />
      <div className="ml-[220px] flex-1 p-8">
        <h1 className="text-3xl font-bold mb-8 text-white">Admin Dashboard</h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-10">
          {cardList.map((card) => (
            <div
              key={card.key}
              className={`${cardColors[card.key]} p-6 rounded-2xl border backdrop-blur-sm shadow-xl flex flex-col items-center justify-center min-h-[120px] transition-all duration-200 hover:scale-105`}
            >
              <span className="text-4xl mb-2">{cardIcons[card.key]}</span>
              <span className="font-semibold text-lg text-white mb-1">{card.label}</span>
              <span className="text-3xl font-bold text-white">
                {stats[card.key]}
              </span>
            </div>
          ))}
        </div>
        <div className="bg-[#1a1a1f] rounded-2xl shadow-2xl p-8 max-w-4xl mx-auto border border-[#2a2a2f]">
          <h2 className="text-2xl font-semibold mb-6 text-white">Users Over Time</h2>
          <div className="max-w-2xl mx-auto">
            {loadingChart ? (
              <div className="text-white text-center py-8">Loading chart...</div>
            ) : chartError ? (
              <div className="text-red-400 text-center py-8">Error loading chart: {chartError}</div>
            ) : (
              <canvas id="usersChart" height="200"></canvas>
            )}
          </div>
        </div>
        <div className="bg-[#1a1a1f] rounded-2xl shadow-2xl p-8 max-w-4xl mx-auto mt-8 border border-[#2a2a2f]">
          <h2 className="text-2xl font-semibold mb-6 text-white">Top 5 Most Traded Tokens</h2>
          <div className="max-w-2xl mx-auto">
            {loadingTokens ? (
              <div className="text-white text-center py-8">Loading chart...</div>
            ) : tokensError ? (
              <div className="text-red-400 text-center py-8">Error loading chart: {tokensError}</div>
            ) : (
              <canvas id="tokensChart" height="200"></canvas>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard; 