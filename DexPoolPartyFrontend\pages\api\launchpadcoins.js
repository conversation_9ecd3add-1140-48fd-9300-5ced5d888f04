const BITQUERY_ENDPOINT = "https://streaming.bitquery.io/eap"
const BITQUERY_API_KEY = process.env.BITQUERY_API_KEY || ""

const LAUNCHPAD_PROGRAM = "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj"

const LAUNCHPAD_POOLS_QUERY = `query GetLaunchpadPools($limit: Int = 100, $since: DateTime) {
  Solana {
    Instructions(
      where: {
        Instruction: {
          Program: {
            Address: { is: "${LAUNCHPAD_PROGRAM}" }
            Method: { is: "PoolCreateEvent" }
          }
        }
        Block: {
          Time: { since: $since }
        }
      }
      limit: { count: $limit }
      orderBy: { descending: Block_Time }
    ) {
      Block {
        Time
      }
      Transaction {
        Signature
        Signer
        Fee
      }
      Instruction {
        Accounts {
          Address
        }
        Program {
          Name
          Method
          AccountNames
          Arguments {
            Name
            Type
            Value {
              ... on Solana_ABI_Integer_Value_Arg {
                integer
              }
              ... on Solana_ABI_String_Value_Arg {
                string
              }
              ... on Solana_ABI_Address_Value_Arg {
                address
              }
              ... on Solana_ABI_BigInt_Value_Arg {
                bigInteger
              }
              ... on Solana_ABI_Bytes_Value_Arg {
                hex
              }
              ... on Solana_ABI_Boolean_Value_Arg {
                bool
              }
              ... on Solana_ABI_Float_Value_Arg {
                float
              }
              ... on Solana_ABI_Json_Value_Arg {
                json
              }
            }
          }
        }
      }
    }
  }
}`

function resolveIpfsUri(uri) {
  if (uri && uri.startsWith("ipfs://")) {
    return `https://ipfs.io/ipfs/${uri.substring(7)}`
  }
  return uri
}

function extractTokenInfoFromArguments(args) {
  const tokenInfo = {
    mintAddress: null,
    name: null,
    symbol: null,
    decimals: 6,
    supply: null,
    metadataUri: null,
  }

  if (!args || !Array.isArray(args)) return tokenInfo

  args.forEach((arg) => {
    if (!arg || !arg.Value) return

    switch (arg.Name) {
      case "base_mint_param":
        if (arg.Value.address) {
          tokenInfo.mintAddress = arg.Value.address
        }
        break
      case "uri":
        if (arg.Value.string) {
          tokenInfo.metadataUri = arg.Value.string
        }
        break
      default:
        if (arg.Value.string && arg.Name.includes("name")) {
          tokenInfo.name = arg.Value.string
        }
        if (arg.Value.string && arg.Name.includes("symbol")) {
          tokenInfo.symbol = arg.Value.string
        }
        if (arg.Value.integer && arg.Name.includes("decimal")) {
          tokenInfo.decimals = arg.Value.integer
        }
        if (arg.Value.bigInteger && arg.Name.includes("supply")) {
          tokenInfo.supply = arg.Value.bigInteger
        }
    }
  })

  return tokenInfo
}

async function transformLaunchpadData(data) {
  const items = data?.Solana?.Instructions || []

  if (!items.length) return []

  const transformedItemsPromises = items.map(async (item, index) => {
    const block = item.Block || {}
    const transaction = item.Transaction || {}
    const instruction = item.Instruction || {}
    const args = instruction.Arguments || []

    const tokenInfo = extractTokenInfoFromArguments(args)
    const mintAddress =
      tokenInfo.mintAddress || `launchpad-${block.Time || index}-${transaction.Signature?.substring(0, 8) || ""}`

    let logo = null
    if (tokenInfo.metadataUri) {
      const metadataUri = resolveIpfsUri(tokenInfo.metadataUri)
      try {
        const metadataResponse = await fetch(metadataUri, {
          cache: "force-cache",
          signal: AbortSignal.timeout(5000),
        })
        if (metadataResponse.ok) {
          const metadata = await metadataResponse.json()
          if (metadata && typeof metadata.image === "string") {
            logo = resolveIpfsUri(metadata.image)
          }
        }
      } catch (metadataError) {
        console.error(`Error fetching metadata for ${mintAddress}:`, metadataError)
      }
    }

    return {
      id: mintAddress,
      symbol: tokenInfo.symbol || `LP${index + 1}`,
      name: tokenInfo.name || `Launchpad Token ${index + 1}`,
      mintAddress: mintAddress,
      creator: transaction.Signer || "Unknown",
      createdAt: block.Time ? new Date(block.Time).toISOString() : new Date().toISOString(),
      createdAtFormatted: block.Time ? new Date(block.Time).toLocaleString() : "Unknown",
      blockNumber: 0,
      blockHash: "",
      transactionSignature: transaction.Signature || "",
      transactionFee: transaction.Fee || 0,
      supply: tokenInfo.supply?.toString() || "1000000000",
      postBalance: "0",
      marketCapUSD: "0",
      decimals: tokenInfo.decimals || 6,
      isMutable: true,
      fungible: true,
      metadataUri: tokenInfo.metadataUri || null,
      logo: logo || `/placeholder.svg?height=48&width=48`,
      updateAuthority: null,
      tokenStandard: null,
      programAddress: LAUNCHPAD_PROGRAM,
      primarySaleHappened: false,
      isLaunchpad: true,
      platform: "launchpad",
    }
  })

  return Promise.all(transformedItemsPromises)
}

export default async function handler(req, res) {
  try {
    const { searchParams } = new URL(req.url, `http://${req.headers.host}`)
    const limit = Number.parseInt(searchParams.get("limit") || "100")
    const since = searchParams.get("since")

    const defaultSince = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    const sinceParam = since || defaultSince

    const requestBody = {
      query: LAUNCHPAD_POOLS_QUERY,
      variables: {
        limit: limit,
        since: sinceParam,
      },
    }

    if (req.method === "POST") {
      const body = await req.json()
      const { customVariables = {} } = body
      requestBody.variables = {
        ...requestBody.variables,
        ...customVariables,
      }
    }

    console.log("Making BitQuery request for launchpad with variables:", requestBody.variables)

    const response = await fetch(BITQUERY_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BITQUERY_API_KEY}`,
        "X-API-KEY": BITQUERY_API_KEY,
      },
      body: JSON.stringify(requestBody),
    })

    // Handle non-JSON responses
    const contentType = response.headers.get("content-type")
    if (!contentType || !contentType.includes("application/json")) {
      const errorText = await response.text()
      console.error(`BitQuery API returned non-JSON response: ${response.status} - ${errorText.substring(0, 200)}`)

      return res.status(response.status).json({
        success: false,
        error: `BitQuery API error: ${response.status} ${response.statusText}`,
        details: "API quota exceeded or service unavailable",
        timestamp: new Date().toISOString(),
      })
    }

    if (!response.ok) {
      const errorData = await response.json()
      console.error(`BitQuery API error: ${response.status} ${response.statusText}`, errorData)

      return res.status(response.status).json({
        success: false,
        error: `BitQuery API error: ${response.statusText}`,
        details: errorData,
        timestamp: new Date().toISOString(),
      })
    }

    const result = await response.json()

    if (result.errors) {
      console.error("BitQuery errors:", result.errors)
      return res.status(400).json({
        success: false,
        error: "BitQuery returned GraphQL errors",
        details: result.errors,
        timestamp: new Date().toISOString(),
      })
    }

    const transformedData = await transformLaunchpadData(result.data)

    return res.status(200).json({
      success: true,
      data: transformedData,
      count: transformedData.length,
      queryType: "pool_creations",
      since: sinceParam,
      timestamp: new Date().toISOString(),
      message:
        transformedData.length > 0
          ? `Found ${transformedData.length} newly created launchpad pools.`
          : "No newly created pools found in the specified time range.",
    })
  } catch (error) {
    console.error("API Error:", error)

    return res.status(500).json({
      success: false,
      error: "Internal Server Error",
      details: error.message,
      data: [],
      count: 0,
      timestamp: new Date().toISOString(),
    })
  }
}
