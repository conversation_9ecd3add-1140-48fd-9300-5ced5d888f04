const express = require("express");
const router = express.Router();
const rewardController = require("../controllers/rewardController");

/**
 * @swagger
 * tags:
 *   name: Rewards
 *   description: Rewards management API
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Reward:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Auto-generated reward ID
 *         userId:
 *           type: string
 *           description: User ID who earned the reward
 *         type:
 *           type: string
 *           enum: [referral, trade_bonus, loyalty_bonus, daily_bonus, milestone_bonus, withdrawal]
 *         amount:
 *           type: number
 *           description: Reward amount
 *         description:
 *           type: string
 *         status:
 *           type: string
 *           enum: [pending, completed, withdrawn]
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/reward/user/{userId}:
 *   get:
 *     summary: Get user rewards summary
 *     tags: [Rewards]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User rewards summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalRewards:
 *                   type: number
 *                 totalWithdrawn:
 *                   type: number
 *                 availableBalance:
 *                   type: number
 *                 rewards:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Reward'
 *       404:
 *         description: User not found
 */

/**
 * @swagger
 * /api/reward/referral/apply:
 *   post:
 *     summary: Apply referral code
 *     tags: [Rewards]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - referralCode
 *             properties:
 *               referralCode:
 *                 type: string
 *                 description: Referral code to apply
 *                 minLength: 6
 *                 maxLength: 10
 *     responses:
 *       200:
 *         description: Referral code applied successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 reward:
 *                   $ref: '#/components/schemas/Reward'
 *       400:
 *         description: Invalid referral code
 *       401:
 *         description: Unauthorized
 *       409:
 *         description: Referral code already used
 */

/**
 * @swagger
 * /api/reward/trade-bonus:
 *   post:
 *     summary: Award trade bonus
 *     tags: [Rewards]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *             properties:
 *               amount:
 *                 type: number
 *                 description: Bonus amount to award
 *                 minimum: 0
 *     responses:
 *       200:
 *         description: Trade bonus awarded
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 reward:
 *                   $ref: '#/components/schemas/Reward'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */

/**
 * @swagger
 * /api/reward/leaderboard:
 *   get:
 *     summary: Get rewards leaderboard
 *     tags: [Rewards]
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [daily, weekly, monthly, all_time]
 *           default: all_time
 *         description: Time period for leaderboard
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 100
 *         description: Number of top users to return
 *     responses:
 *       200:
 *         description: Rewards leaderboard
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   userId:
 *                     type: string
 *                   username:
 *                     type: string
 *                   profileImage:
 *                     type: string
 *                   totalRewards:
 *                     type: number
 *                   rank:
 *                     type: integer
 */

/**
 * @swagger
 * /api/reward/withdraw:
 *   post:
 *     summary: Withdraw rewards
 *     tags: [Rewards]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - walletAddress
 *             properties:
 *               amount:
 *                 type: number
 *                 description: Amount to withdraw
 *                 minimum: 0
 *               walletAddress:
 *                 type: string
 *                 description: Wallet address for withdrawal
 *     responses:
 *       200:
 *         description: Withdrawal successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 transactionId:
 *                   type: string
 *       400:
 *         description: Invalid input or insufficient balance
 *       401:
 *         description: Unauthorized
 */

/**
 * @swagger
 * /api/reward/daily-bonus:
 *   post:
 *     summary: Claim daily bonus
 *     tags: [Rewards]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Daily bonus claimed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 reward:
 *                   $ref: '#/components/schemas/Reward'
 *                 nextClaimTime:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Already claimed today or invalid request
 *       401:
 *         description: Unauthorized
 */

// Get user rewards summary
router.get("/user/:userId", rewardController.getUserRewardsSummary);

// Apply referral code
router.post("/referral/apply", rewardController.applyReferralCode);

// Award trade bonus
router.post("/trade-bonus", rewardController.awardTradeBonus);

// Get rewards leaderboard
router.get("/leaderboard", rewardController.getRewardsLeaderboard);

// Withdraw rewards
router.post("/withdraw", rewardController.withdrawRewards);

// Claim daily bonus
router.post("/daily-bonus", rewardController.claimDailyBonus);

module.exports = router;