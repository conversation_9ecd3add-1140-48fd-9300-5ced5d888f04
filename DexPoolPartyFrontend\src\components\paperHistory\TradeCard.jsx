import React from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Coins,
  DollarSign,
  BarChart3,
  Brain,
  TestTube
} from 'lucide-react';
import { 
  getStateConfig, 
  getPredictionConfig, 
  getConfidenceColor, 
  formatSignal, 
  formatDate, 
  formatAddress 
} from '../../utils/helper';

const TradeCard = ({ trade, isDevnet = false }) => {
  const stateConfig = getStateConfig(trade?.state);
  const predictionConfig = getPredictionConfig(trade?.prediction);
  const profit = trade?.sell 
    ? trade.sell.totalValue - trade.buy.totalValue 
    : 0;
  const profitPercentage = trade?.sell 
    ? ((profit / trade.buy.totalValue) * 100).toFixed(2)
    : 0;

  return (
    <div className="relative bg-gradient-to-br from-gray-800/40 via-gray-800/30 to-gray-900/40 backdrop-blur-sm rounded-2xl p-6 shadow-2xl transition-all duration-500 hover:via-gray-800/40  border border-gray-700/30  group overflow-hidden">
      {/* Background Gradient Overlay */}
      <div className={`absolute inset-0 bg-gradient-to-r ${predictionConfig.bgGradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
      
      {/* Header with Token Info and Badges */}
      <div className="relative z-10 flex items-start justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="relative">
            {trade?.token?.image ? (
              <img
                src={trade.token.image}
                alt={trade?.token?.name || 'Token'}
                className="w-14 h-14 rounded-2xl object-cover border-2 border-gray-600/50 group-hover:border-gray-500/60 transition-all duration-300 shadow-lg"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
            ) : null}
            <div className={`w-14 h-14 bg-gradient-to-br from-gray-600/60 to-gray-700/60 rounded-2xl flex items-center justify-center border-2 border-gray-600/50 group-hover:border-gray-500/60 transition-all duration-300 shadow-lg ${trade?.token?.image ? 'hidden' : 'flex'}`}>
              <Coins className="w-7 h-7 text-gray-300" />
            </div>
            {/* Confidence Indicator */}
            {trade?.prediction?.confidence && (
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-gray-900/90 backdrop-blur-sm rounded-full flex items-center justify-center border border-gray-600/50">
                <span className={`text-xs font-bold ${getConfidenceColor(trade.prediction.confidence)}`}>
                  {trade.prediction.confidence}
                </span>
              </div>
            )}
          </div>
          <div>
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-xl font-bold text-white group-hover:text-gray-100 transition-colors">
                {trade?.token?.name || 'Unknown Token'}
              </h3>
              {isDevnet && (
                <div className="flex items-center gap-1 px-2 py-1 bg-green-500/10 backdrop-blur-sm rounded-lg border border-green-500/20">
                  <TestTube className="w-3 h-3 text-green-400" />
                  <span className="text-xs font-medium text-green-400">Devnet</span>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-400 mb-2">
              <span className="font-mono bg-gray-700/40 backdrop-blur-sm px-2 py-1 rounded-lg border border-gray-600/30">
                {trade?.token?.symbol || 'N/A'}
              </span>
              <span>•</span>
              <span className="font-mono">{formatAddress(trade?.token?.address)}</span>
            </div>
            {/* Prediction Signals */}
            {trade?.prediction?.signals && (
              <div className="flex flex-wrap gap-1">
                {trade.prediction.signals.slice(0, 2).map((signal, index) => (
                  <span
                    key={index}
                    className="text-xs px-2 py-1 bg-gray-700/30 backdrop-blur-sm text-gray-400 rounded-md border border-gray-600/20"
                  >
                    {formatSignal(signal)}
                  </span>
                ))}
                {trade.prediction.signals.length > 2 && (
                  <span className="text-xs px-2 py-1 bg-gray-700/30 backdrop-blur-sm text-gray-400 rounded-md border border-gray-600/20">
                    +{trade.prediction.signals.length - 2} more
                  </span>
                )}
              </div>
            )}
            {/* Devnet Transaction Hash */}
            {isDevnet && trade?.transactionHash && (
              <div className="mt-2">
                <span className="text-xs text-gray-500">Tx Hash: </span>
                <span className="text-xs font-mono text-gray-400">{formatAddress(trade.transactionHash)}</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex flex-col items-end gap-2">
          {/* Prediction Badge */}
          <div className={`flex items-center gap-1.5 px-3 py-1.5 rounded-xl text-sm font-medium border backdrop-blur-sm ${predictionConfig.color}`}>
            <Brain className="w-3 h-3" />
            {predictionConfig.label}
            {trade?.prediction?.confidence && (
              <span className={`text-xs ${getConfidenceColor(trade.prediction.confidence)}`}>
                {trade.prediction.confidence}%
              </span>
            )}
          </div>
          
          {/* Profit/Loss Badge */}
          {trade?.sell && (
            <div className={`flex items-center gap-1.5 px-3 py-1.5 rounded-xl text-sm font-medium border backdrop-blur-sm ${
              profit >= 0 
                ? 'bg-emerald-500/10 text-emerald-400 border-emerald-500/20' 
                : 'bg-red-500/10 text-red-400 border-red-500/20'
            }`}>
              {profit >= 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
              {profit >= 0 ? '+' : ''}${profit.toFixed(2)} ({profitPercentage}%)
            </div>
          )}
          
          {/* State Badge */}
          <div className={`flex items-center gap-1.5 px-3 py-1.5 rounded-xl text-sm font-medium border backdrop-blur-sm ${stateConfig.color}`}>
            {trade?.state === 'completed' && <CheckCircle className="w-3 h-3" />}
            {trade?.state === 'pending' && <Clock className="w-3 h-3" />}
            {trade?.state === 'partial' && <AlertCircle className="w-3 h-3" />}
            {trade?.state === 'cancelled' && <XCircle className="w-3 h-3" />}
            {!trade?.state && <AlertCircle className="w-3 h-3" />}
            {stateConfig.label}
          </div>
        </div>
      </div>

      {/* Trade Details Grid */}
      <div className="relative z-10 grid md:grid-cols-2 gap-6">
        {/* Buy Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <div className="p-2 bg-emerald-500/10 backdrop-blur-sm rounded-xl border border-emerald-500/20">
              <TrendingUp className="w-4 h-4 text-emerald-400" />
            </div>
            <h4 className="text-lg font-bold text-emerald-400">Buy Order</h4>
          </div>
          
          <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 space-y-3 border border-gray-700/30 hover:border-gray-600/40 transition-all duration-300">
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-2">
                <Coins className="w-3 h-3" />
                Quantity
              </span>
              <span className="font-bold text-white">
                {trade?.buy?.quantity?.toLocaleString() || 0} {trade?.token?.symbol || 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-2">
                <DollarSign className="w-3 h-3" />
                Price
              </span>
              <span className="font-bold text-white">${trade?.buy?.price?.toFixed(4) || '0.0000'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-2">
                <BarChart3 className="w-3 h-3" />
                Total Value
              </span>
              <span className="font-bold text-emerald-400">${trade?.buy?.totalValue?.toFixed(2) || '0.00'}</span>
            </div>
            <div className="flex justify-between items-center pt-2 border-t border-gray-700/50">
              <span className="text-gray-400 text-sm flex items-center gap-2">
                <Clock className="w-3 h-3" />
                Timestamp
              </span>
              <span className="text-sm font-mono text-gray-300">{formatDate(trade?.buy?.timestamp)}</span>
            </div>
          </div>
        </div>

        {/* Sell Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <div className={`p-2 backdrop-blur-sm rounded-xl border ${trade?.sell ? 'bg-red-500/10 border-red-500/20' : 'bg-gray-600/10 border-gray-600/20'}`}>
              <TrendingDown className={`w-4 h-4 ${trade?.sell ? 'text-red-400' : 'text-gray-400'}`} />
            </div>
            <h4 className={`text-lg font-bold ${trade?.sell ? 'text-red-400' : 'text-gray-400'}`}>
              Sell Order
            </h4>
          </div>
          
          {trade?.sell ? (
            <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 space-y-3 border border-gray-700/30 hover:border-gray-600/40 transition-all duration-300">
              <div className="flex justify-between items-center">
                <span className="text-gray-400 flex items-center gap-2">
                  <Coins className="w-3 h-3" />
                  Quantity
                </span>
                <span className="font-bold text-white">
                  {trade.sell.quantity?.toLocaleString() || 0} {trade?.token?.symbol || 'N/A'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400 flex items-center gap-2">
                  <DollarSign className="w-3 h-3" />
                  Price
                </span>
                <span className="font-bold text-white">${trade.sell.price?.toFixed(4) || '0.0000'}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400 flex items-center gap-2">
                  <BarChart3 className="w-3 h-3" />
                  Total Value
                </span>
                <span className="font-bold text-red-400">${trade.sell.totalValue?.toFixed(2) || '0.00'}</span>
              </div>
              <div className="flex justify-between items-center pt-2 border-t border-gray-700/50">
                <span className="text-gray-400 text-sm flex items-center gap-2">
                  <Clock className="w-3 h-3" />
                  Timestamp
                </span>
                <span className="text-sm font-mono text-gray-300">{formatDate(trade.sell.timestamp)}</span>
              </div>
            </div>
          ) : (
            <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 space-y-3 border border-gray-700/30">
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Clock className="w-8 h-8 text-gray-500 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">No sell order yet</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* AI Insights Footer */}
      {/* <div className="relative z-10 mt-6 pt-4 border-t border-gray-700/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs text-gray-400">
            <Brain className="w-3 h-3" />
            <span>AI Analysis</span>
            <div className="w-1 h-1 bg-gray-600 rounded-full" />
            <span className={getConfidenceColor(trade?.prediction?.confidence)}>
              {trade?.prediction?.confidence || 0}% Confidence
            </span>
          </div>
          <div className="flex items-center gap-1">
            {Array.from({ length: 5 }, (_, i) => (
              <div
                key={i}
                className={`w-1 h-1 rounded-full ${
                  i < Math.floor((trade?.prediction?.confidence || 0) / 20)
                    ? getConfidenceColor(trade?.prediction?.confidence).includes('emerald')
                      ? 'bg-emerald-400'
                      : getConfidenceColor(trade?.prediction?.confidence).includes('amber')
                      ? 'bg-amber-400'
                      : 'bg-red-400'
                    : 'bg-gray-600'
                }`}
              />
            ))}
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default TradeCard;