const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     Trade:
 *       type: object
 *       required:
 *         - userId
 *         - tokenId
 *         - type
 *         - amount
 *         - price
 *       properties:
 *         userId:
 *           type: string
 *           description: Reference to the user making the trade
 *         tokenId:
 *           type: string
 *           description: Reference to the token being traded
 *         type:
 *           type: string
 *           enum: [buy, sell]
 *           description: Type of trade operation
 *         amount:
 *           type: number
 *           description: Amount of tokens to trade
 *         price:
 *           type: number
 *           description: Price per token at the time of trade
 *         status:
 *           type: string
 *           enum: [pending, completed, cancelled]
 *           default: pending
 *           description: Current status of the trade
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

const tradeSchema = new mongoose.Schema({
    token: { type: mongoose.Schema.Types.ObjectId, ref: 'Token', required: true },
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    userWallet: { type: String, required: true },
    type: {
      type: String,
      enum: ['buy', 'sell'],
      required: true
    },
    amount: { type: Number, required: true },
    price: { type: Number, required: true },
    totalValue: { type: Number, required: true },
    txHash: { type: String },
    timestamp: { type: Date, default: Date.now },
    fees: {
      protocolFee: { type: Number, default: 0 },
      creatorFee: { type: Number, default: 0 }
    }
  }, { timestamps: true });
  
  module.exports = mongoose.model('Trade', tradeSchema);
