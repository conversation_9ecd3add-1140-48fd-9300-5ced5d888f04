const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors'); // ✅ Import CORS
const connectDB = require('./src/config/db');
const swaggerUI = require('swagger-ui-express');
const swaggerSpecs = require('./src/config/swagger');
const path = require('path');

// Load environment variables
dotenv.config();

const app = express();

// ✅ Use CORS Middleware (allows all origins by default)
app.use(cors());

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Swagger Documentation
app.use('/api-docs', swaggerUI.serve, swaggerUI.setup(swaggerSpecs));

// Routes
app.use('/api/users', require('./src/routes/userRoutes'));
app.use('/api/tokens', require('./src/routes/tokenRoutes'));
app.use('/api/trades', require('./src/routes/tradeRoutes'));
app.use('/api/comments', require('./src/routes/commentRoutes'));
app.use('/api/devnet', require('./src/routes/devnetRoutes'));
// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something broke!' });
});

// Start server
const PORT = process.env.PORT || 4000;
const startServer = async () => {
  try {
    await connectDB();
    app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
  } catch (error) {
    console.error('Server startup error:', error);
    process.exit(1);
  }
};

startServer();
