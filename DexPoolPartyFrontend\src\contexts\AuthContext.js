'use client';
import { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext(undefined);

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [walletAddress, setWalletAddress] = useState(null);
  const [loading, setLoading] = useState(true);

  // Enhanced logging utility
  const logAuth = (action, details = {}) => {
    console.log(`🔐 [AUTH] ${action}`, {
      timestamp: new Date().toISOString(),
      user: user ? { id: user.id, email: user.email } : null,
      walletAddress,
      ...details
    });
  };

  const updateUser = (newUser, newToken) => {
    logAuth('UPDATE_USER', { 
      newUser: newUser ? { id: newUser.id, email: newUser.email } : null,
      hasToken: !!newToken,
      tokenPreview: newToken ? `${newToken.substring(0, 20)}...` : null
    });

    setUser(newUser);

    if (newToken) {
      localStorage.setItem('authToken', newToken);
      logAuth('TOKEN_STORED', { 
        tokenLength: newToken.length,
        tokenPreview: `${newToken.substring(0, 20)}...`
      });
    }

    localStorage.setItem('user', JSON.stringify(newUser));
    logAuth('USER_STORED', { 
      userData: newUser ? { id: newUser.id, email: newUser.email } : null
    });
  };

  useEffect(() => {
    logAuth('INITIALIZE_AUTH_CONTEXT');
    
    try {
      const storedUser = localStorage.getItem('user');
      const storedToken = localStorage.getItem('authToken');
      const storedWallet = localStorage.getItem('walletAddress');
      
      logAuth('LOADING_STORED_DATA', {
        hasStoredUser: !!storedUser,
        hasStoredToken: !!storedToken,
        hasStoredWallet: !!storedWallet,
        tokenPreview: storedToken ? `${storedToken.substring(0, 20)}...` : null
      });

      if (storedUser && storedToken) {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
        logAuth('RESTORED_USER_SESSION', {
          user: { id: parsedUser.id, email: parsedUser.email },
          tokenValid: !!storedToken
        });
      } else {
        logAuth('NO_STORED_SESSION', {
          reason: !storedUser ? 'No user data' : 'No token data'
        });
      }
      
      if (storedWallet) {
        setWalletAddress(storedWallet);
        logAuth('RESTORED_WALLET', { walletAddress: storedWallet });
      }
    } catch (error) {
      logAuth('ERROR_LOADING_STORED_DATA', { error: error.message });
      console.error('Error loading stored user:', error);
    } finally {
      setLoading(false);
      logAuth('AUTH_CONTEXT_READY');
    }
  }, []);

  const login = (userData, token) => {
    logAuth('LOGIN_ATTEMPT', {
      userData: userData ? { id: userData.id, email: userData.email } : null,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 20)}...` : null
    });

    try {
      localStorage.setItem('user', JSON.stringify(userData));
      logAuth('USER_DATA_STORED', {
        user: { id: userData.id, email: userData.email }
      });

      if (token) {
        localStorage.setItem('authToken', token);
        logAuth('AUTH_TOKEN_STORED', {
          tokenLength: token.length,
          tokenPreview: `${token.substring(0, 20)}...`
        });
        // Set auth token as a cookie for Next.js middleware (expires in 7 days)
        const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toUTCString();
        document.cookie = `authToken=${token}; expires=${expires}; path=/`;
      }
      
      setUser(userData);
      logAuth('LOGIN_SUCCESS', {
        user: { id: userData.id, email: userData.email },
        sessionActive: true
      });
    } catch (error) {
      logAuth('LOGIN_ERROR', { error: error.message });
      console.error('Error during login:', error);
      throw new Error('Login failed');
    }
  };

  const logout = () => {
    logAuth('LOGOUT_ATTEMPT', {
      currentUser: user ? { id: user.id, email: user.email } : null,
      walletConnected: !!walletAddress
    });

    try {
      disconnectWallet(); // Clear local wallet state first
      setUser(null);
      setWalletAddress(null);
      
      // Clear all auth-related localStorage
      const clearedItems = [];
      const itemsToClear = ['user', 'token', 'authToken', 'firebaseToken', 'phantom_disconnected', 'walletAddress'];
      
      itemsToClear.forEach(item => {
        if (localStorage.getItem(item)) {
          localStorage.removeItem(item);
          clearedItems.push(item);
        }
      });

      logAuth('LOGOUT_SUCCESS', { 
        clearedItems,
        sessionCleared: true
      });

      // Remove authToken cookie for Next.js middleware
      document.cookie = 'authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

      // Disconnect Phantom wallet if connected
      if (window.solana && window.solana.isPhantom) {
        window.solana.disconnect().catch(() => {
          logAuth('PHANTOM_DISCONNECT_ERROR');
        });
        try { 
          window.solana.isConnected = false; 
          logAuth('PHANTOM_DISCONNECTED');
        } catch (e) {
          logAuth('PHANTOM_DISCONNECT_ERROR', { error: e.message });
        }
      }
    } catch (error) {
      logAuth('LOGOUT_ERROR', { error: error.message });
      console.error('Error during logout:', error);
    }
  };

  const connectWallet = (address) => {
    logAuth('WALLET_CONNECT_ATTEMPT', { walletAddress: address });
    
    try {
      setWalletAddress(address);
      localStorage.setItem('walletAddress', address);
      logAuth('WALLET_CONNECT_SUCCESS', { walletAddress: address });
    } catch (error) {
      logAuth('WALLET_CONNECT_ERROR', { error: error.message });
      console.error('Error connecting wallet:', error);
      throw new Error('Wallet connection failed');
    }
  };

  const disconnectWallet = () => {
    logAuth('WALLET_DISCONNECT_ATTEMPT', { currentWallet: walletAddress });
    
    try {
      setWalletAddress(null);
      localStorage.removeItem('walletAddress');
      logAuth('WALLET_DISCONNECT_SUCCESS');
    } catch (error) {
      logAuth('WALLET_DISCONNECT_ERROR', { error: error.message });
      console.error('Error disconnecting wallet:', error);
    }
  };

  if (loading) {
    return null;
  }

  return (
    <AuthContext.Provider value={{
      user,
      walletAddress,
      isWalletConnected: !!walletAddress,
      login,
      logout,
      updateUser,
      connectWallet,
      disconnectWallet,
      loading
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
