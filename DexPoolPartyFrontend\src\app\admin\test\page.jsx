"use client";

import React, { useState } from 'react';
import Sidebar from '../Sidebar';
import { API_BASE_URL } from '../../../services/api';

const handleLogout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  window.location.href = '/admin/login';
};

const AdminTest = () => {
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const createTestUser = async () => {
    setLoading(true);
    setMessage('');
    setError('');
    
    try {
      // First create a test user
      const createRes = await fetch(`${API_BASE_URL}/users/auth/email/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testEmail,
          password: 'TestPassword123!',
          username: 'testuser'
        }),
      });
      
      const createData = await createRes.json();
      console.log('Create user response:', createData);
      
      if (createRes.ok) {
        setMessage(`Test user created: ${testEmail}. You can now promote this user to admin.`);
      } else {
        setError(`Failed to create test user: ${createData.message}`);
      }
    } catch (err) {
      console.error('Create test user error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testAdminAccess = async () => {
    setLoading(true);
    setMessage('');
    setError('');
    
    try {
      const res = await fetch(`${API_BASE_URL}/users/admin/dashboard-stats`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      const data = await res.json();
      console.log('Admin access test response:', data);
      
      if (res.ok) {
        setMessage('✅ Admin access working! Dashboard stats: ' + JSON.stringify(data));
      } else {
        setError(`❌ Admin access failed: ${data.message}`);
      }
    } catch (err) {
      console.error('Admin access test error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen bg-[#111115]">
      <Sidebar onLogout={handleLogout} />
      <div className="ml-[220px] flex-1 p-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-[#1a1a1f] p-8 rounded-2xl shadow-2xl border border-[#2a2a2f]">
            <h2 className="text-2xl font-bold mb-6 text-white text-center">Admin Test Page</h2>
            
            <div className="space-y-6">
              {/* Test Admin Access */}
              <div className="p-4 rounded-lg bg-blue-500/10 border border-blue-500/20">
                <h3 className="text-lg font-semibold text-white mb-2">Test Admin Access</h3>
                <p className="text-gray-300 text-sm mb-3">Test if admin authentication is working</p>
                <button 
                  onClick={testAdminAccess}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Testing...' : 'Test Admin Access'}
                </button>
              </div>

              {/* Create Test User */}
              <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20">
                <h3 className="text-lg font-semibold text-white mb-2">Create Test User</h3>
                <p className="text-gray-300 text-sm mb-3">Create a test user to promote to admin</p>
                <div className="flex gap-2 mb-3">
                  <input 
                    type="email" 
                    value={testEmail} 
                    onChange={e => setTestEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="flex-1 px-3 py-2 rounded-lg bg-[#2a2a2f] border border-[#3a3a3f] text-white placeholder-gray-400"
                  />
                  <button 
                    onClick={createTestUser}
                    disabled={loading}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                  >
                    {loading ? 'Creating...' : 'Create User'}
                  </button>
                </div>
              </div>

              {/* Debug Info */}
              <div className="p-4 rounded-lg bg-gray-500/10 border border-gray-500/20">
                <h3 className="text-lg font-semibold text-white mb-2">Debug Information</h3>
                <div className="text-gray-300 text-sm space-y-1">
                  <p><strong>API URL:</strong> {API_BASE_URL}</p>
                  <p><strong>Token:</strong> {localStorage.getItem('token') ? '✅ Present' : '❌ Missing'}</p>
                  <p><strong>User:</strong> {localStorage.getItem('user') ? '✅ Present' : '❌ Missing'}</p>
                  <p><strong>Admin Email:</strong> <EMAIL></p>
                </div>
              </div>

              {/* Messages */}
              {error && (
                <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm">
                  {error}
                </div>
              )}
              {message && (
                <div className="p-3 rounded-lg bg-green-500/10 border border-green-500/20 text-green-400 text-sm">
                  {message}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminTest; 