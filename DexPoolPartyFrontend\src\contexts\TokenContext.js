// context/TokenContext.js
'use client';
import React, { createContext, useContext, useEffect, useState } from 'react';
import api from '../lib/api.js';

const TokenContext = createContext();

export const useTokenContext = () => useContext(TokenContext);

export const TokenProvider = ({ children }) => {
  const [tokenIds, setTokenIds] = useState([]);
  const [loading, setLoading] = useState(true);
  const authToken = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;

  const fetchOtherTokenIds = async () => {
    try {
      const res = await api.get(`${process.env.NEXT_PUBLIC_BACKEND_URL}/tokens/token-id`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });
      console.log(res);

      if (res.data?.ids) {
        setTokenIds(res.data.ids);
      }
    } catch (err) {
      console.error('Error fetching otherTokenIds:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (authToken) fetchOtherTokenIds();
  }, [authToken]);


  const addTokenIdIfNotExists = async (newId, coin) => {
    if (!tokenIds.includes(newId)) {
      try {
        const res = await api.post(`${process.env.NEXT_PUBLIC_BACKEND_URL}/tokens/other-token`, { tokenId: newId, coin }, {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        });
        console.log('Add token ID response:', res);

        if (res.data?.success) {
          setTokenIds((prev) => [...prev, newId]);
        }
      } catch (err) {
        console.error('Error adding token ID:', err);
      }
    }
  };

  return (
    <TokenContext.Provider value={{ tokenIds, loading, addTokenIdIfNotExists }}>
      {children}
    </TokenContext.Provider>
  );
};
