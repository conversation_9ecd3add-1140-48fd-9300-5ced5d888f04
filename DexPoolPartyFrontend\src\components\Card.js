'use client';
import { useTrending } from '../../context/trendingData.js';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect } from 'react';

const CoinCard = () => {
  const { trendingData, loading, error, refreshData } = useTrending();

  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 3000000);
    return () => clearInterval(interval);
  }, [refreshData]);

  if (loading) return (
    <div className="px-4 py-8 flex justify-center">
      <div className="animate-pulse text-gray-400">Loading coins...</div>
    </div>
  );

  if (error) return (
    <div className="px-4 py-8 text-red-500 text-center">
      Error loading data: {error}
      <button 
        onClick={refreshData}
        className="ml-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Retry
      </button>
    </div>
  );

  if (!trendingData || trendingData.length === 0) return (
    <div className="px-4 py-8 text-center text-gray-400">
      No coins found
      <button 
        onClick={refreshData}
        className="ml-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Refresh
      </button>
    </div>
  );

  const formatNumber = (num) => {
    const parsedNum = Number(num);
    if (!num) return '0';
    if (num < 1000) return  parsedNum.toFixed(2);
    return new Intl.NumberFormat('en-US', {
      notation: 'compact',
      maximumFractionDigits: 1
    }).format(num);
  };

  const getTimeAgo = (timestamp) => {
    if (!timestamp) return 'N/A';
    const seconds = Math.floor((new Date() - new Date(timestamp)) / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h`;
    return `${Math.floor(hours / 24)}d`;
  };

  const shortenAddress = (address) => {
  if (!address) return 'N/A';
  const addressStr = String(address); // Convert to string in case it's a number or other type
  if (addressStr.length <= 8) return addressStr; // Return as-is if already short
  return `${addressStr.slice(0, 4)}...${addressStr.slice(-4)}`;
};

  const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:4000/api';
  const BACKEND_ROOT = BACKEND_URL.replace(/\/api$/, '');

  function getImageUrl(logo) {
    if (!logo) return '/token.png';
    if (logo.startsWith('http')) return logo;
    return `${BACKEND_ROOT}/uploads/${logo}`;
  }

  return (
    <div className="space-y-4">
      {trendingData.map((coin) => (
        <div key={coin.id} className="flex flex-row w-full gap-[12px] pl-[12px] pr-[12px] sm:pr-[16px] pt-[12px] pb-[2px] justify-start items-center">
          {/* Coin Image Section */}
          <div className="flex flex-col items-center gap-[4px]">
            <div className="relative w-[74px] h-[74px] justify-center items-center">
              <span className="contents">
                <div className="flex bg-pump absolute top-[62px] left-[62px] p-[1px] w-[16px] h-[16px] justify-center items-center rounded-full z-30">
                  <div className="flex justify-center items-center bg-background absolute w-[14px] h-[14px] rounded-full z-30">
                    <Image 
                      alt="Pump V1" 
                      width={10} 
                      height={10} 
                      src="https://axiom.trade/images/pump.svg" 
                      className="object-cover"
                    />
                  </div>
                </div>
              </span>
              
              <div className="bg-pump/20 absolute flex p-[1px] justify-start items-center rounded-[4px] z-20">
                <div className="bg-backgroundSecondary flex p-[2px] justify-start items-center rounded-[3px]">
                  <div className="w-[68px] h-[68px] flex-shrink-0 group/image relative">
                    <Link href={`/${coin.id}`}>
                      <div className="w-full h-full relative">
                        <div className="pointer-events-none border-textPrimary/10 border-[1px] absolute w-[68px] h-[68px] z-10 rounded-[1px]"></div>
                        <Image
                          alt={coin.name}
                          width={56}
                          height={56}
                          className="rounded-[1px] w-full h-full object-cover"
                          src={getImageUrl(coin.logo)}
                          onError={(e) => {
                            e.target.src = "/token.png";
                          }}
                        />
                        <button className="absolute inset-0 bg-black/50 opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                          <i className="ri-camera-line text-white text-[24px]"></i>
                        </button>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
              
              <div className="absolute top-0 left-0 w-[74px] h-[74px] rounded-[4px] z-10 flex items-center justify-center">
                <div className="inline-flex items-center justify-center">
                  <svg width="78" height="78" viewBox="0 0 78 78">
                    <path className="text-pump opacity-40" stroke="currentColor" fill="transparent" strokeWidth="1" d="
                      M 76 76
                      L 6 76
                      Q 2 76 2 72
                      L 2 6
                      Q 2 2 6 2
                      L 72 2
                      Q 76 2 76 6
                      L 76 72
                      Q 76 76 76 76
                    "></path>
                    <path className="text-pump transition-all duration-300 ease-in-out" stroke="currentColor" fill="transparent" strokeWidth="1" strokeLinecap="round" strokeDasharray="296" strokeDashoffset="11.603200000000015" d="
                      M 76 76
                      L 6 76
                      Q 2 76 2 72
                      L 2 6
                      Q 2 2 6 2
                      L 72 2
                      Q 76 2 76 6
                      L 76 72
                      Q 76 76 76 76
                    "></path>
                  </svg>
                </div>
              </div>
            </div>
            
            <span className="contents">
              <span className="text-textTertiary text-[12px] font-medium text-center max-w-[74px]">
                <button className="text-textTertiary hover:text-primaryBlueHover transition-colors duration-[125ms] text-[12px] font-medium text-center max-w-[74px] flex items-center gap-[4px] group/copy">
                  <span>{shortenAddress(coin.id)}</span>
                </button>
              </span>
            </span>
          </div>

          {/* Coin Info Section */}
          <div className="flex flex-col flex-1 h-full gap-[20px] justify-start items-start pt-[4px] pb-[12px] overflow-hidden">
            <div className="flex flex-col w-full gap-[4px] justify-start items-start min-w-0">
              <div className="flex flex-row h-[18px] w-full gap-[4px] justify-start items-center min-w-0">
                <span className="text-textPrimary text-[16px] font-medium tracking-[-0.02em] truncate">
                  {coin.symbol || 'N/A'}
                </span>
                
                <div className="min-w-0 flex-1 overflow-hidden">
                  <span className="contents">
                    <button className="flex flex-row gap-[4px] justify-start items-center text-textTertiary hover:text-primaryBlueHover transition-colors duration-[125ms] min-w-0 overflow-hidden">
                      <span className="text-inherit text-[16px] sm:text-[16px] lg:text-[14px] xl:text-[16px] font-medium tracking-[-0.02em] xl:truncate xl:max-w-full block">
                        {coin.name}
                      </span>
                      <i className="text-inherit ri-file-copy-fill text-[14px]"></i>
                    </button>
                  </span>
                </div>
                
                <div className="flex flex-row h-[18px] gap-[4px] justify-end items-end">
                  <span className="text-textTertiary text-[12px] font-medium pb-[1.6px]">MC</span>
                  <span className="text-primaryYellow text-[16px] font-medium">
                    ${formatNumber(coin.quote?.USD?.market_cap)}
                  </span>
                </div>
              </div>
              
              <div className="flex flex-row w-full h-[18px] gap-[12px] lg:gap-[8px] xl:gap-[12px] justify-start items-center">
                <span className={`${coin.quote?.USD?.percent_change_24h >= 0 ? 'text-primaryGreen' : 'text-red-500'} text-[14px] font-medium`}>
                  {getTimeAgo(coin.last_updated)}
                </span>
                
                <div className="flex flex-row flex-shrink-0 gap-[8px] justify-start items-center [&_i]:text-[16px] [&_i]:lg:text-[14px] [&_i]:xl:text-[16px]">
                  <div>
                    <a href="#" target="_blank" rel="noopener noreferrer" className="flex items-center">
                      <i className="text-textSecondary ri-global-line text-[16px] hover:text-primaryBlueHover transition-colors duration-[125ms]"></i>
                    </a>
                  </div>
                  <a href="#" target="_blank" rel="noopener noreferrer" className="flex items-center">
                    <i className="icon-pill text-textSecondary hover:text-primaryBlueHover transition-colors duration-[125ms]" style={{fontSize: '16px'}}></i>
                  </a>
                  <a href="#" className="flex items-center">
                    <i className="text-textSecondary ri-search-line text-[16px] hover:text-primaryBlueHover transition-colors duration-[125ms]"></i>
                  </a>
                </div>
                
                <div className="flex-row flex-1 h-[18px] gap-[8px] justify-start items-center hidden sm:flex md:hidden lg:hidden xl:flex">
                  <span className="contents">
                    <div className="flex flex-row gap-[2px] h-[16px] justify-start items-center">
                      <i className="text-textTertiary ri-group-line text-[16px]"></i>
                      <span className="text-textPrimary text-[12px] font-medium">185</span>
                    </div>
                  </span>
                  <span className="contents">
                    <div className="flex flex-row gap-[2px] h-[16px] justify-center items-center flex-shrink-0">
                      <Image 
                        alt="Pro Traders" 
                        width={16} 
                        height={16} 
                        className="w-[16px] h-[16px]" 
                        src="https://axiom.trade/images/material-symbols-candlestick-chart.svg" 
                      />
                      <span className="text-textPrimary text-[12px] font-medium">62</span>
                    </div>
                  </span>
                  <span className="contents">
                    <div className="flex flex-row gap-[2px] h-[16px] justify-start items-center cursor-pointer">
                      <i className="text-textTertiary ri-vip-crown-2-line text-[16px] pb-[1.2px]"></i>
                      <span className="text-textPrimary text-[12px] font-medium">0</span>
                    </div>
                  </span>
                </div>
                
                <div className="flex flex-row flex-1 h-[18px] gap-[4px] justify-end items-start sm:pt-[3px] md:pt-0">
                  <div className="relative flex flex-row flex-1 h-[18px] gap-[4px] justify-end items-end">
                    <div className="flex flex-row h-[18px] flex-1 gap-[4px] justify-end items-end">
                      <span className="text-textTertiary text-[12px] font-medium pb-[1.6px] flex justify-center items-center">V</span>
                      <span className="text-textPrimary text-[16px] font-medium">
                        ${formatNumber(coin.quote?.USD?.volume_24h)}
                      </span>
                    </div>
                    <div className="flex flex-row justify-end items-center absolute top-[18px] right-0 h-[12px] gap-[4px] flex-shrink-0 group/image text-nowrap">
                      <span className="text-textTertiary text-[11px] font-medium">
                        TX <span className="text-textPrimary text-[11px] font-medium">370</span>
                      </span>
                      <div className="flex flex-row flex-1 min-w-[52px] max-w-[52px] h-[2px] bg-secondaryStroke rounded-full overflow-hidden">
                        <div 
                          className="h-[3px] bg-increase" 
                          style={{width: `${Math.min(100, Math.abs(coin.quote?.USD?.percent_change_24h || 0))}%`}}
                        ></div>
                        <div 
                          className="h-[3px] bg-decrease" 
                          style={{width: `${100 - Math.min(100, Math.abs(coin.quote?.USD?.percent_change_24h || 0))}%`}}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex sm:hidden md:flex lg:flex xl:hidden flex-row flex-1 h-[18px] gap-[8px] justify-start items-center pt-[3px]">
                <div className="flex flex-row gap-[2px] h-[16px] justify-start items-center">
                  <i className="text-textTertiary ri-group-line text-[16px]"></i>
                  <span className="text-textPrimary text-[12px] font-medium">185</span>
                </div>
                <div className="flex flex-row gap-[2px] h-[16px] justify-center items-center flex-shrink-0">
                  <Image 
                    alt="Pro Traders" 
                    width={16} 
                    height={16} 
                    className="w-[16px] h-[16px]" 
                    src="https://axiom.trade/images/material-symbols-candlestick-chart.svg" 
                  />
                  <span className="text-textPrimary text-[12px] font-medium">62</span>
                </div>
                <span className="contents">
                  <div className="flex flex-row gap-[2px] h-[16px] justify-start items-center cursor-pointer">
                    <i className="text-textTertiary ri-vip-crown-2-line text-[16px] pb-[1.2px]"></i>
                    <span className="text-textPrimary text-[12px] font-medium">0</span>
                  </div>
                </span>
              </div>
            </div>
            
            <div className="hidden sm:flex md:hidden lg:hidden xl:flex flex-row w-full h-[24px] gap-[4px] justify-start items-end">
              <span className="contents">
                <div className="flex flex-row gap-[4px] flex-shrink-0 h-[24px] px-[5px] justify-start items-center rounded-full bg-backgroundSecondary border-primaryStroke/50 border-[1px]">
                  <i className="ri-user-star-line text-[14px] text-primaryGreen"></i>
                  <span className="text-primaryGreen text-[12px] font-medium">14%</span>
                </div>
              </span>
              <span className="contents">
                <div className="flex flex-row gap-[4px] flex-shrink-0 w-fit h-[24px] px-[5px] justify-start items-center rounded-full bg-backgroundSecondary border-primaryStroke/50 border-[1px]">
                  <div className="w-[16px] h-[16px] flex items-center justify-center">
                    <i className="icon-chef-hat text-primaryGreen" style={{fontSize: '12px'}}></i>
                  </div>
                  <span className="text-primaryGreen text-[12px] font-medium">0%</span>
                </div>
              </span>
              <span className="contents">
                <div className="flex flex-row gap-[4px] flex-shrink-0 w-fit h-[24px] px-[5px] justify-start items-center rounded-full bg-backgroundSecondary border-primaryStroke/50 border-[1px]">
                  <Image 
                    alt="Snipers" 
                    width={14} 
                    height={14} 
                    className="w-[14px] h-[14px]" 
                    src="https://axiom.trade/images/crosshair-2-line-green.svg" 
                  />
                  <span className="text-primaryGreen text-[12px] font-medium">0%</span>
                </div>
              </span>
              <span className="contents">
                <div className="flex flex-row gap-[4px] flex-shrink-0 w-fit h-[24px] px-[5px] justify-start items-center rounded-full bg-backgroundSecondary border-primaryStroke/50 border-[1px]">
                  <Image 
                    alt="Insiders" 
                    width={14} 
                    height={14} 
                    className="w-[14px] h-[14px]" 
                    src="https://axiom.trade/images/ghost-line-green.svg" 
                  />
                  <span className="text-primaryGreen text-[12px] font-medium">0%</span>
                </div>
              </span>
              <span className="contents">
                <div className="flex flex-row gap-[4px] flex-shrink-0 w-fit h-[24px] px-[5px] justify-start items-center rounded-full bg-backgroundSecondary border-primaryStroke/50 border-[1px]">
                  <Image 
                    alt="Bundle" 
                    width={14} 
                    height={14} 
                    className="w-[14px] h-[14px]" 
                    src="https://axiom.trade/images/bundle-green.svg" 
                  />
                  <span className="text-primaryGreen text-[12px] font-medium">4%</span>
                </div>
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default CoinCard;