"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';

const ADMIN_EMAIL = '<EMAIL>';
const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL;

const AdminLogin = () => {
  const router = useRouter();
  const { login } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    if (email !== ADMIN_EMAIL) {
      setError('Only the main admin can log in.');
      return;
    }
    setLoading(true);
    try {
      const res = await fetch(`${API_BASE_URL}/users/admin/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.message || 'Login failed');
      if (data.user.role !== 'admin' || data.user.email !== ADMIN_EMAIL) {
        setError('Not authorized as admin.');
        return;
      }
      
      // Transform the user object to match expected structure
      const adminUser = {
        id: data.user._id || data.user.id,
        email: data.user.email,
        role: data.user.role,
        name: data.user.name || 'Admin',
        _id: data.user._id || data.user.id
      };
      
      // Store token and user data
      localStorage.setItem('token', data.token);
      localStorage.setItem('authToken', data.token);
      localStorage.setItem('user', JSON.stringify(adminUser));
      
      // Update AuthContext
      login(adminUser, data.token);
      
      // Redirect to dashboard
      router.push('/admin/dashboard');
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#111115]">
      <form onSubmit={handleSubmit} className="bg-[#1a1a1f] p-8 rounded-2xl shadow-2xl min-w-[320px] border border-[#2a2a2f]">
        <h2 className="text-2xl font-bold mb-6 text-white text-center">Admin Login</h2>
        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium text-gray-300">Email</label>
          <input 
            type="email" 
            value={email} 
            onChange={e => setEmail(e.target.value)} 
            required 
            className="w-full px-4 py-3 rounded-lg bg-[#2a2a2f] border border-[#3a3a3f] text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
            placeholder="Enter admin email"
          />
        </div>
        <div className="mb-6">
          <label className="block mb-2 text-sm font-medium text-gray-300">Password</label>
          <input 
            type="password" 
            value={password} 
            onChange={e => setPassword(e.target.value)} 
            required 
            className="w-full px-4 py-3 rounded-lg bg-[#2a2a2f] border border-[#3a3a3f] text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
            placeholder="Enter password"
          />
        </div>
        {error && (
          <div className="mb-4 p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm">
            {error}
          </div>
        )}
        <button 
          type="submit" 
          disabled={loading} 
          className="w-full py-3 px-4 rounded-lg bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold text-base border-none transition-all duration-200 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-[#1a1a1f] disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Logging in...' : 'Login'}
        </button>
        
        {/* Admin credentials hint */}
        <div className="mt-4 p-3 rounded-lg bg-blue-500/10 border border-blue-500/20">
          <p className="text-blue-400 text-xs text-center">
            Admin Email: <EMAIL>
          </p>
        </div>
      </form>
    </div>
  );
};

export default AdminLogin; 