"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { tokenService } from "@/services/api";

// Modern loader
function Loader() {
  return (
    <div className="flex justify-center items-center">
      <span className="inline-block w-8 h-8 border-4 border-t-blue-500 border-[#444] rounded-full animate-spin"></span>
    </div>
  );
}

// Main Page Component
const Page = () => {
  const [formData, setFormData] = useState({
    name: "",
    symbol: "",
    description: "",
    media: null,
    links: {
      telegram: "",
      website: "",
      twitter: "",
    },
  });
  const [previewUrl, setPreviewUrl] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (name === "media") {
      setFormData({ ...formData, [name]: files[0] });
      setPreviewUrl(files[0] ? URL.createObjectURL(files[0]) : null);
    } else if (name.startsWith("links.")) {
      const linkField = name.split(".")[1];
      setFormData({
        ...formData,
        links: {
          ...formData.links,
          [linkField]: value,
        },
      });
    } else if (name === "symbol") {
      setFormData({ ...formData, symbol: value.toUpperCase() });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    // Symbol validation (2-12 uppercase letters/numbers, no spaces)
    if (!/^[A-Z0-9]{2,12}$/.test(formData.symbol)) {
      setError("Symbol must be 2-12 uppercase letters/numbers, no spaces.");
      return;
    }
    // Link validation (basic URL check)
    const urlPattern =
      /^(https?:\/\/)?([\w\-]+\.)+[\w\-]+(\/[\w\-._~:/?#[\]@!$&'()*+,;=]*)?$/;
    for (const key of ["telegram", "website", "twitter"]) {
      const val = formData.links[key];
      if (val && val.length > 0 && !urlPattern.test(val)) {
        setError(`Invalid URL for ${key}`);
        return;
      }
    }
    setSubmitting(true);
    const payloadData = new FormData();
    payloadData.append("name", formData.name);
    payloadData.append("symbol", formData.symbol);
    payloadData.append("description", formData.description);

    if (formData.media) {
      payloadData.append("media", formData.media);
    }

    payloadData.append("links[telegram]", formData.links.telegram);
    payloadData.append("links[website]", formData.links.website);
    payloadData.append("links[twitter]", formData.links.twitter);

    try {
      const result = await tokenService.createToken(payloadData);
      if (result && result.message) {
        setError(result.message);
        setSubmitting(false);
        return;
      }
      router.push("/"); // Or your preferred route
    } catch (err) {
      setError("Failed to create token. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen w-full flex items-center justify-center px-2 py-10 ">
      <form
        className="w-full max-w-lg rounded-3xl px-8 py-10 space-y-8 relative"
        onSubmit={handleSubmit}
        autoComplete="off"
      >
        <h2 className="text-3xl font-extrabold text-center mb-6 text-white tracking-tight drop-shadow-lg">
          Create Your Coin
        </h2>

        {error && <div className="text-red-400 text-center mb-4">{error}</div>}

        {/* Coin Name */}
        <div>
          <label
            className="block text-base font-semibold mb-2 text-[#e5eaf3]"
            htmlFor="coin-name"
          >
            Coin Name
          </label>
          <input
            id="coin-name"
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            maxLength={32}
            required
            className="w-full border border-[#444] bg-[#23242a] text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#888] shadow-inner"
            placeholder="Coin Name"
            autoComplete="off"
          />
        </div>

        {/* Ticker Symbol */}
        <div>
          <label
            className="block text-base font-semibold mb-2 text-[#e5eaf3]"
            htmlFor="coin-symbol"
          >
            Ticker Symbol
          </label>
          <input
            id="coin-symbol"
            type="text"
            name="symbol"
            value={formData.symbol}
            onChange={handleChange}
            maxLength={12}
            required
            className="w-full border border-[#444] bg-[#23242a] text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#888] uppercase tracking-wider shadow-inner"
            placeholder="$ABCXYZ"
            autoComplete="off"
          />
        </div>

        {/* Description */}
        <div>
          <label
            className="block text-base font-semibold mb-2 text-[#e5eaf3]"
            htmlFor="coin-description"
          >
            Description
          </label>
          <textarea
            id="coin-description"
            rows={4}
            name="description"
            value={formData.description}
            onChange={handleChange}
            maxLength={500}
            required
            className="w-full border border-[#444] bg-[#23242a] text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none placeholder:text-[#888] shadow-inner"
            placeholder="Be sure to put a good description for maximum impact!"
          ></textarea>
          <div className="text-right text-xs text-[#aaa]">
            {formData.description.length}/500
          </div>
        </div>

        {/* Image or Video */}
        <div>
          <label className="block text-base font-semibold mb-2 text-[#e5eaf3]">
            Image or Video
          </label>
          <div className="flex items-center gap-4 rounded-xl p-3 border border-[#353535] bg-[#20212c]">
            {/* File input and label */}
            <div className="flex flex-col items-center justify-center w-full">
              <label
                htmlFor="file-upload"
                className="cursor-pointer bg-[#353545] text-white px-4 py-2 rounded-lg hover:bg-blue-900/70 transition text-xs font-semibold shadow"
              >
                {formData.media ? "Change File" : "Select File"}
              </label>
              <input
                type="file"
                id="file-upload"
                name="media"
                accept="image/*,video/*"
                onChange={handleChange}
                className="hidden"
              />
              <span className="text-xs text-[#aaa] mt-2 truncate max-w-[150px]">
                {formData.media ? formData.media.name : "No file selected"}
              </span>
            </div>
            {/* Preview */}
            {previewUrl && (
              <div className="rounded-lg overflow-hidden border border-[#353545] w-20 h-20 flex items-center justify-center shadow">
                {formData.media?.type.startsWith("image/") ? (
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="object-cover w-full h-full"
                  />
                ) : (
                  <video
                    src={previewUrl}
                    className="object-cover w-full h-full"
                    controls
                  />
                )}
              </div>
            )}
          </div>
        </div>

        {/* Social/Website Links */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <label
              className="block text-sm font-semibold mb-2 text-[#b4bfe0]"
              htmlFor="telegram"
            >
              Telegram
            </label>
            <input
              id="telegram"
              type="text"
              name="links.telegram"
              value={formData.links.telegram}
              onChange={handleChange}
              autoComplete="off"
              className="w-full border border-[#353535] bg-[#23242a] text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#888] shadow-inner"
              placeholder="t.me/account_name"
            />
          </div>
          <div>
            <label
              className="block text-sm font-semibold mb-2 text-[#b4bfe0]"
              htmlFor="website"
            >
              Website
            </label>
            <input
              id="website"
              type="text"
              name="links.website"
              value={formData.links.website}
              onChange={handleChange}
              autoComplete="off"
              className="w-full border border-[#353535] bg-[#23242a] text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#888] shadow-inner"
              placeholder="www.coinwebsite.com"
            />
          </div>
          <div>
            <label
              className="block text-sm font-semibold mb-2 text-[#b4bfe0]"
              htmlFor="twitter"
            >
              Twitter/X
            </label>
            <input
              id="twitter"
              type="text"
              name="links.twitter"
              value={formData.links.twitter}
              onChange={handleChange}
              autoComplete="off"
              className="w-full border border-[#353535] bg-[#23242a] text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#888] shadow-inner"
              placeholder="x.com/account_name"
            />
          </div>
        </div>

        {/* Submit */}
        <button
          type="submit"
          disabled={submitting}
          className={`w-full text-white py-3 rounded-xl font-bold text-lg shadow-lg mt-2 transition bg-gradient-to-r from-purple-500/90 to-purple-700/90 hover:from-purple-600 hover:to-purple-800 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500
            ${submitting ? "opacity-50 cursor-not-allowed" : ""}`}
        >
          {submitting ? <Loader /> : "🚀 Create Coin"}
        </button>
      </form>
    </div>
  );
};

export default Page;
