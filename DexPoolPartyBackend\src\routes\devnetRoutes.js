const express = require('express');
const router = express.Router();
const devnetController = require('../controllers/devnetController');
const auth = require('../middleware/auth');

// Get all devnet transactions (paginated)
router.get('/transactions', auth, devnetController.getAllDevnetTransactions);

// Get a single devnet transaction by ID
router.get('/transactions/:id', auth, devnetController.getDevnetTransactionById);

// Create a new devnet transaction
router.post('/transactions', auth, devnetController.createDevnetTransaction);

// Update a devnet transaction
router.put('/transactions/:id', auth, devnetController.updateDevnetTransaction);

// Delete a devnet transaction
router.delete('/transactions/:id', auth, devnetController.deleteDevnetTransaction);

// Get devnet stats
router.get('/stats', auth, devnetController.getDevnetStats);

module.exports = router; 