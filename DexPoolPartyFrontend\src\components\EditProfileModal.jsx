"use client";

import { useState, useEffect, useRef } from "react";
import { showToast } from "@/utils/toast";
import Image from "next/image";
import { API_BASE_URL } from "@/services/api";
import { useAuth } from "@/contexts/AuthContext";
import {
  Camera,
  X,
  Upload,
  User,
  Link,
  MessageCircle,
  Twitter,
  Save,
  Loader2,
  Mail,
} from "lucide-react";

export default function EditProfileModal({ showModal, closeModal }) {
  const { updateUser } = useAuth();
  const [username, setUsername] = useState("");
  const [profileImage, setProfileImage] = useState("/king.png");
  const [bio, setBio] = useState("");
  const [twitter, setTwitter] = useState("");
  const [telegram, setTelegram] = useState("");
  const [loading, setLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);
  const [email, setEmail] = useState("");

  // Fetch user data from localStorage on mount and when modal opens
  useEffect(() => {
    if (showModal) {
      const userData = JSON.parse(localStorage.getItem("user"));
      console.log("🔍 Loading user data from localStorage:", userData);
      console.log("🔍 Bio in localStorage:", userData?.bio);
      console.log("🔍 Social links in localStorage:", userData?.socialLinks);

      if (userData) {
        setUsername(userData.username || "");
        setProfileImage(
          userData.profileImage
            ? getImageUrl(userData.profileImage)
            : "/king.png"
        );
        setBio(userData.bio || "");
        setTwitter(userData.socialLinks?.twitter || "");
        setTelegram(userData.socialLinks?.telegram || "");
        setEmail(userData.email || "");
      }
    }
  }, [showModal]);

  // Helper to normalize and get full image URL
  const getImageUrl = (mediaPath) => {
    if (!mediaPath) return "/king.png";
    if (mediaPath.startsWith("http") || mediaPath.startsWith("data:"))
      return mediaPath;

    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || API_BASE_URL;
    if (!backendUrl) {
      console.warn("NEXT_PUBLIC_BACKEND_URL is not defined.");
      return "/king.png";
    }

    // Normalize Windows-style paths
    const normalizedPath = mediaPath.replace(/\\/g, "/");

    // Remove /api from backend URL if present since uploads are served at root level
    const baseUrl = backendUrl.replace(/\/api$/, "");

    // Combine the base URL and the path properly
    const finalPath = baseUrl.endsWith("/")
      ? normalizedPath.replace(/^\//, "")
      : `/${normalizedPath.replace(/^\//, "")}`;

    return `${baseUrl}${finalPath}`;
  };

  // Handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Handle drop event
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelection(e.dataTransfer.files[0]);
    }
  };

  // Trigger file input click to select image
  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  // Handle file selection (both click and drop)
  const handleFileSelection = (file) => {
    if (file && file.type.startsWith("image/")) {
      setImageLoading(true);
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImage(reader.result);
        setImageLoading(false);
      };
      reader.readAsDataURL(file);
    } else {
      showToast.error("Please select a valid image file");
    }
  };

  // Handle new image selection and preview
  const handleFileChange = (e) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelection(file);
    }
  };

  // Handle form submission to update user profile
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Get auth token first
      const authToken = localStorage.getItem("authToken");
      if (!authToken) {
        showToast.error("Authentication required. Please log in to continue.");
        setLoading(false);
        return;
      }

      // Debug: Log what we're sending
      console.log("Sending profile update with:");
      console.log("- Username:", username);
      console.log("- Bio:", bio);
      console.log("- Email:", email);
      console.log("- Social Links:", { twitter, telegram });

      const file = fileInputRef.current?.files?.[0];
      console.log("- Profile Image File:", file);

      // Try a simple JSON request first to test username update
      if (!file) {
        console.log("No file selected, using JSON request");
        const jsonPayload = {
          username: username,
          bio: bio,
          email: email,
          socialLinks: { twitter, telegram },
        };

        console.log("JSON payload:", jsonPayload);

        const response = await fetch(`${API_BASE_URL}/users/update`, {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(jsonPayload),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.message ||
              `Failed to update profile. Status: ${response.status}`
          );
        }

        const data = await response.json();
        console.log("Profile update response:", data);
        console.log("Updated user data:", data.user);
        updateUser(data.user, authToken);
        showToast.success(
          "Profile updated successfully! Your changes have been saved."
        );
        closeModal();
        return;
      }

      // If file is selected, use FormData
      console.log("File selected, using FormData request");
      const formData = new FormData();
      formData.append("username", username);
      formData.append("bio", bio);
      formData.append("socialLinks", JSON.stringify({ twitter, telegram }));
      formData.append("email", email);
      formData.append("profileImage", file);

      // Debug: Log the exact URL being called
      const updateUrl = `${API_BASE_URL}/users/update`;
      console.log("Making request to:", updateUrl);
      console.log("Request method: PUT");
      console.log(
        "Authorization header:",
        `Bearer ${authToken.substring(0, 20)}...`
      );

      const response = await fetch(updateUrl, {
        method: "PUT",
        headers: { Authorization: `Bearer ${authToken}` },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
            `Failed to update profile. Status: ${response.status}`
        );
      }

      const data = await response.json();
      console.log("Profile update response:", data);
      console.log("Updated user data:", data.user);
      updateUser(data.user, authToken);
      showToast.success(
        "Profile updated successfully! Your changes have been saved."
      );
      closeModal();
    } catch (error) {
      console.error("Failed to update profile:", error);
      showToast.error(`Update failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (!showModal) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 font-inter">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300"
        onClick={closeModal}
      />

      {/* Modal */}
      <div className="relative h-[87vh] bg-[#1f2227]  w-full max-w-2xl mx-auto rounded-2xl shadow-2xl border border-[#37414F]/50 overflow-hidden transform transition-all duration-300 scale-100">
        {/* Header */}
        <div className="relative px-4 py-3  border-b border-[#37414F]/50">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white">Edit Profile</h2>
              <p className="text-sm text-gray-400 mt-1">
                Update your profile information
              </p>
            </div>
            <button
              onClick={closeModal}
              className="p-2 rounded-full hover:bg-white/10 transition-colors duration-200 group"
              disabled={loading}
            >
              <X className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-8 py-6 max-h-[70vh] overflow-y-auto custom-scrollbar">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Profile Image Section */}
            <div className="flex flex-col items-center space-y-4">
              <div className="relative group">
                {/* Image Container */}
                <div
                  className={`relative w-32 h-32 rounded-full overflow-hidden border-4 transition-all duration-300 cursor-pointer ${
                    dragActive
                      ? "border-[#86EFAC] shadow-lg shadow-[#86EFAC]/25 scale-105"
                      : "border-[#37414F] hover:border-[#86EFAC]/50 hover:shadow-lg hover:shadow-[#86EFAC]/10"
                  }`}
                  onClick={handleImageClick}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  {imageLoading ? (
                    <div className="absolute inset-0 flex items-center justify-center bg-[#1a1d21]">
                      <Loader2 className="w-8 h-8 text-[#86EFAC] animate-spin" />
                    </div>
                  ) : (
                    <Image
                      src={getImageUrl(profileImage) || "/placeholder.svg"}
                      alt="Profile picture"
                      fill
                      sizes="128px"
                      className="object-cover transition-transform duration-300 group-hover:scale-110"
                      onError={(e) => {
                        e.currentTarget.src = "/king.png";
                        e.currentTarget.srcset = "/king.png";
                      }}
                      priority
                    />
                  )}

                  {/* Overlay */}
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <div className="text-center">
                      <Camera className="w-8 h-8 text-white mx-auto mb-1" />
                      <span className="text-xs text-white font-medium">
                        {dragActive ? "Drop here" : "Change Photo"}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Upload indicator */}
                {dragActive && (
                  <div className="absolute inset-0 rounded-full border-2 border-dashed border-[#86EFAC] bg-[#86EFAC]/10 flex items-center justify-center">
                    <Upload className="w-8 h-8 text-[#86EFAC]" />
                  </div>
                )}
              </div>

              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />

              {/* Action Buttons */}
              <div className="flex gap-3">
                <button
                  type="button"
                  onClick={handleImageClick}
                  className="px-4 py-2 text-sm font-medium rounded-lg bg-[#86EFAC]/20 hover:bg-[#86EFAC]/30 text-[#86EFAC] transition-all duration-200 flex items-center gap-2 border border-[#86EFAC]/30"
                >
                  <Upload className="w-4 h-4" />
                  Upload New
                </button>

                {profileImage !== "/king.png" && (
                  <button
                    type="button"
                    onClick={() => {
                      setProfileImage("/king.png");
                      if (fileInputRef.current) fileInputRef.current.value = "";
                      showToast.success("Profile image cleared");
                    }}
                    className="px-4 py-2 text-sm font-medium rounded-lg bg-red-500/20 hover:bg-red-500/30 text-red-400 transition-all duration-200 border border-red-500/30"
                  >
                    Remove
                  </button>
                )}
              </div>
            </div>

            {/* Form Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Username */}
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium text-gray-300">
                  <User className="w-4 h-4" />
                  Username
                </label>
                <input
                  type="text"
                  className="w-full p-3 rounded-lg bg-[#2a2f36] border border-[#37414F] text-white placeholder:text-gray-500 focus:border-[#86EFAC] focus:ring-2 focus:ring-[#86EFAC]/20 transition-all duration-200"
                  value={username}
                  onChange={(e) => {
                    console.log(
                      "Username changing from:",
                      username,
                      "to:",
                      e.target.value
                    );
                    setUsername(e.target.value);
                  }}
                  onFocus={() => console.log("Username input focused")}
                  onBlur={() => console.log("Username input blurred")}
                  onClick={() => console.log("Username input clicked")}
                  onKeyDown={(e) => console.log("Key pressed:", e.key)}
                  placeholder="Your username"
                  required
                />
                <p className="text-xs text-gray-500">
                  Choose a unique username for your profile
                </p>
              </div>

              {/* Email */}
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium text-gray-300">
                  <Mail className="w-4 h-4" />
                  Email
                </label>
                <input
                  type="email"
                  className="w-full p-3 rounded-lg bg-[#2a2f36] border border-[#37414F] text-white placeholder:text-gray-500 focus:border-[#86EFAC] focus:ring-2 focus:ring-[#86EFAC]/20 transition-all duration-200"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address"
                  required
                />
              </div>

              {/* Bio */}
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium text-gray-300">
                  <MessageCircle className="w-4 h-4" />
                  Bio
                </label>
                <textarea
                  rows={4}
                  className="resize-none w-[600px] p-3 rounded-lg bg-[#2a2f36] border border-[#37414F] text-white placeholder:text-gray-500 focus:border-[#86EFAC] focus:ring-2 focus:ring-[#86EFAC]/20 transition-all duration-200"
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  placeholder="Tell us about yourself..."
                  maxLength={200}
                />
                <p className="text-xs text-gray-500 text-right">
                  {bio.length}/200
                </p>
              </div>
            </div>

            {/* Social Links */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                <Link className="w-5 h-5" />
                Social Links
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Twitter */}
                <div className="space-y-2">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-300">
                    <Twitter className="w-4 h-4 text-blue-400" />
                    Twitter
                  </label>
                  <input
                    type="url"
                    className="w-full p-3 rounded-lg bg-[#2a2f36] border border-[#37414F] text-white placeholder:text-gray-500 focus:border-[#86EFAC] focus:ring-2 focus:ring-[#86EFAC]/20 transition-all duration-200"
                    value={twitter}
                    onChange={(e) => setTwitter(e.target.value)}
                    placeholder="https://twitter.com/username"
                  />
                </div>

                {/* Telegram */}
                <div className="space-y-2">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-300">
                    <MessageCircle className="w-4 h-4 text-blue-500" />
                    Telegram
                  </label>
                  <input
                    type="url"
                    className="w-full p-3 rounded-lg bg-[#2a2f36] border border-[#37414F] text-white placeholder:text-gray-500 focus:border-[#86EFAC] focus:ring-2 focus:ring-[#86EFAC]/20 transition-all duration-200"
                    value={telegram}
                    onChange={(e) => setTelegram(e.target.value)}
                    placeholder="https://t.me/username"
                  />
                </div>
              </div>
            </div>

            {/* Form Buttons */}
            <div className="px-8 py-14 border-t border-[#37414F]/50">
              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  className="px-6 py-3 rounded-lg bg-[#2a2f36] hover:bg-[#37414F] text-white font-medium transition-all duration-200 border border-[#37414F]"
                  onClick={closeModal}
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  onClick={handleSubmit}
                  className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#86EFAC] to-[#6EE7B7] hover:from-[#6EE7B7] hover:to-[#86EFAC] text-black font-medium transition-all duration-200 flex items-center gap-2 shadow-lg shadow-[#86EFAC]/25 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      Save Changes
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        {/* <div className="px-8 py-6 bg-[#151719] border-t border-[#37414F]/50">
          <div className="flex justify-end gap-3">
            <button
              type="button"
              className="px-6 py-3 rounded-lg bg-[#2a2f36] hover:bg-[#37414F] text-white font-medium transition-all duration-200 border border-[#37414F]"
              onClick={closeModal}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#86EFAC] to-[#6EE7B7] hover:from-[#6EE7B7] hover:to-[#86EFAC] text-black font-medium transition-all duration-200 flex items-center gap-2 shadow-lg shadow-[#86EFAC]/25 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </div> */}
      </div>
    </div>
  );
}
