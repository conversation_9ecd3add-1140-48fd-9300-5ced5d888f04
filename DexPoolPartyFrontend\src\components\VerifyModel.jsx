import React, { useState } from 'react';
import Connect2<PERSON><PERSON>tom from './Connect2Phantom'; // Assuming this is your Phantom wallet connection component

import { useAuth } from '../contexts/AuthContext'; // Make sure you have this context
import { API_BASE_URL } from '@/services/api';
import { toast } from 'sonner';
import { signInWithGoogle } from '../lib/firebase';

const VerifyModal = ({ showModal, modalType, closeModal, userId }) => {
  const { updateUser, user } = useAuth(); // Get user from context
  const authToken = localStorage.getItem("authToken");
  const [emailToLink, setEmailToLink] = useState('');
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('email'); // 'email' or 'google'
  const walletConnected = user && user.walletAddress;
  const [errorMsg, setErrorMsg] = useState("");

  if (!showModal) return null;

  // --- Handle Email Linking ---
  const handleLinkEmail = async () => {
    setLoading(true);
    setErrorMsg("");
    if (!authToken) {
      toast.error('You must be logged in to link an email.');
      setLoading(false);
      return;
    }
    if (!walletConnected) {
      setErrorMsg("Please connect your wallet before linking an email.");
      setLoading(false);
      return;
    }
    if (!emailToLink) {
      toast.error('Please enter an email address.');
      setLoading(false);
      return;
    }
    try {
      const response = await fetch(`${API_BASE_URL}/users/link-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({ email: emailToLink }),
      });
      const data = await response.json();
      if (!response.ok) {
        if (response.status === 404) {
          setErrorMsg('User not found. Please connect your wallet first.');
          return;
        }
        throw new Error(data.message || 'Failed to link email.');
      }
      updateUser(data.user, data.token);
      toast.success(data.message);
      setEmailToLink('');
      closeModal();
    } catch (error) {
      console.error('Error linking email:', error);
      toast.error(error.message || 'Failed to link email.');
    } finally {
      setLoading(false);
    }
  };

  // --- Handle Google Linking ---
  const handleLinkGoogle = async () => {
    setLoading(true);
    setErrorMsg("");
    if (!authToken) {
      toast.error('You must be logged in to link a Google account.');
      setLoading(false);
      return;
    }
    if (!walletConnected) {
      setErrorMsg("Please connect your wallet before linking Google.");
      setLoading(false);
      return;
    }
    try {
      const { idToken } = await signInWithGoogle();
      const response = await fetch(`${API_BASE_URL}/users/link-google`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({ idToken }),
      });
      const data = await response.json();
      if (!response.ok) {
        if (response.status === 404) {
          setErrorMsg('User not found. Please connect your wallet first.');
          return;
        }
        throw new Error(data.message || 'Failed to link Google account.');
      }
      updateUser(data.user, data.token);
      toast.success(data.message);
      closeModal();
    } catch (error) {
      console.error('Error linking Google account:', error);
      if (error.code === 'auth/popup-closed-by-user') {
        toast.info('Google sign-in popup closed.');
      } else if (error.code === 'auth/credential-already-in-use') {
        toast.error('This Google account is already linked to another user. Please log in with that account or unlink it first.');
      } else {
        toast.error(error.message || 'Failed to link Google account.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center">
      <div className="bg-[#23272f] p-6 rounded-xl shadow-lg w-[90%] max-w-md border border-[#27272a]">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-white">Verify Your Account</h2>
          <button onClick={closeModal} className="text-gray-400 hover:text-white text-2xl leading-none">×</button>
        </div>
        {!walletConnected ? (
          // Show wallet connection UI
          <div className="flex flex-col gap-3">
            <p className="text-gray-300 text-center mb-2">Please connect your wallet to continue.</p>
            <Connect2Phantom onConnect={closeModal} mode="login" />
          </div>
        ) : !user?.emailVerified ? (
          // Show Email/Google tabbed modal
          <>
            <div className="flex mb-6 bg-[#23272f] rounded-xl overflow-hidden border border-[#27272a]">
              <button
                className={`flex-1 py-2 transition-colors font-semibold ${
                  activeTab === 'email'
                    ? 'bg-[#27272a] text-[#e5e7eb] shadow'
                    : 'bg-[#23272f] text-[#a1a1aa] hover:bg-[#27272a]'
                }`}
                onClick={() => setActiveTab('email')}
              >
                Email
              </button>
              <button
                className={`flex-1 py-2 transition-colors font-semibold ${
                  activeTab === 'google'
                    ? 'bg-[#27272a] text-[#e5e7eb] shadow'
                    : 'bg-[#23272f] text-[#a1a1aa] hover:bg-[#27272a]'
                }`}
                onClick={() => setActiveTab('google')}
              >
                Google
              </button>
            </div>
            {errorMsg && (
              <div className="mb-4 text-red-400 text-center text-sm font-medium bg-[#2a1a1a] rounded p-2">{errorMsg}</div>
            )}
            {/* Email Tab */}
            {activeTab === 'email' && (
              <div className="flex flex-col gap-3">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="px-3 py-2 rounded bg-[#2c2c2c] text-white border border-gray-700"
                  value={emailToLink}
                  onChange={(e) => setEmailToLink(e.target.value)}
                  disabled={loading}
                />
                <button
                  onClick={handleLinkEmail}
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 rounded-xl shadow transition"
                  disabled={loading}
                >
                  {loading ? 'Sending...' : 'Send Verification'}
                </button>
              </div>
            )}
            {/* Google Tab */}
            {activeTab === 'google' && (
              <div className="flex flex-col gap-3 mt-2">
                <button
                  onClick={handleLinkGoogle}
                  className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-2 rounded-xl shadow flex items-center justify-center gap-2 transition"
                  disabled={loading}
                >
                  {loading ? 'Linking...' : (
                    <>
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M12.24 10.27c.41 0 .76.11 1.05.34l2.57-2.57c-.96-1.07-2.2-1.72-3.66-1.72C8.84 6.32 6 8.9 6 12.28s2.84 5.96 6.24 5.96c2.81 0 4.88-1.57 5.98-3.95l-.17-.18h-5.81V10.27z" fill="#4285F4"></path><path d="M0 12.28a6.1 6.1 0 011.83-4.14L4.03 9.4c-.45.98-.71 2.05-.71 3.28 0 4.48 3.58 8.16 8 8.16a7.2 7.2 0 005.15-2.07l-2.08-2.08a4.99 4.99 0 01-3.07.96c-2.3 0-4.18-1.88-4.18-4.18z" fill="#34A853"></path><path d="M23.16 9.38L23 9.28l-2.05-2.05a9.42 9.42 0 00-3.65-1.02h-5.91V2.62c0-.5-.4-.9-.9-.9h-1.02a.9.9 0 00-.9.9v3.69h-5.9c-.5 0-.9.4-.9.9v1.02c0 .5.4.9.9.9h5.9v3.69c0 .5.4.9.9.9h1.02c.5 0 .9-.4.9-.9V9.38h5.91l2.05 2.05c.45.45 1.03.7 1.62.7s1.17-.25 1.62-.7a2.29 2.29 0 000-3.24z" fill="#FBBC05"></path><path d="M12.24 6.32a6.04 6.04 0 014.28 1.77L18.1 6.32a8.03 8.03 0 00-5.86-2.53C8.84 3.79 6 6.37 6 9.75s2.84 5.96 6.24 5.96c3.4 0 6.24-2.58 6.24-5.96 0-.34-.04-.67-.09-.99z" fill="#EA4335"></path>
                      </svg>
                      Link with Google
                    </>
                  )}
                </button>
              </div>
            )}
          </>
        ) : (
          // Show success message
          <div className="text-green-400 text-center font-semibold py-6">
            Your account is fully verified!
          </div>
        )}
      </div>
    </div>
  );
};

export default VerifyModal;