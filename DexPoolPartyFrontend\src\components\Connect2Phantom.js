"use client";

import { useState, useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import { showToast } from '@/utils/toast';
import { API_BASE_URL } from '@/services/api';
import { signInWithCustomToken } from 'firebase/auth';
import { useAuth } from '@/contexts/AuthContext';

// Key for localStorage
const PHANTOM_DISCONNECTED_KEY = 'phantom_disconnected';

// Connect2Phantom now takes a prop 'onConnect' which is a function that receives the publicKey
// It no longer handles its own backend login
const Connect2Phantom = ({ onConnect, onDisconnect, isDisabled, mode = "login", userId }) => {
  const { updateUser } = useAuth();
  const [phantomInstalled, setPhantomInstalled] = useState(false);
  const [connected, setConnected] = useState(false);
  const [publicKey, setPublicKey] = useState(null);
  const isInitializing = useRef(true);
  const disconnectToastShown = useRef(false);

  // Clear any existing toasts when component mounts
  useEffect(() => {
    showToast.dismiss && showToast.dismiss();
  }, []);

  // Handle notification display with delay
  const showNotification = (type, message, description = "") => {
    showToast.dismiss && showToast.dismiss(); // Clear existing toasts
    setTimeout(() => {
      switch (type) {
        case 'success':
          showToast.success(message, { description });
          break;
        case 'error':
          showToast.error(message, { description });
          break;
        case 'info':
          showToast.info(message, { description });
          break;
        case 'warning':
          showToast.warning(message, { description });
          break;
        default:
          showToast.info(message, { description });
      }
    }, 100);
  };

  useEffect(() => {
  const provider = window.solana;
  if (provider && provider.on) {
    const handleDisconnectEvent = () => {
      setConnected(false);
      setPublicKey(null);
    };

    provider.on("disconnect", handleDisconnectEvent);

    return () => {
      provider.removeListener("disconnect", handleDisconnectEvent);
    };
  }
}, []);


  const handleDisconnect = useCallback(async () => {
    try {
      if (window.solana?.isConnected) {
        await window.solana.disconnect();
      }
      setConnected(false);
      setPublicKey(null);
      localStorage.setItem(PHANTOM_DISCONNECTED_KEY, 'true'); // Mark as manually disconnected
      localStorage.removeItem('authToken'); // Consistent with LoginModal
      localStorage.removeItem('user'); // Consistent with LoginModal
      if (onDisconnect) {
        onDisconnect();
      }
      if (!disconnectToastShown.current) {
        showNotification('info', "Wallet disconnected");
        disconnectToastShown.current = true;
      }
    } catch (error) {
      console.error('Error during disconnect:', error);
      showNotification('error', "Failed to disconnect wallet");
    }
  }, [setConnected, setPublicKey, onDisconnect]);

  const connectWallet = async () => {
    try {
      if (!window.solana || !window.solana.isPhantom) {
        showNotification('error', "Phantom wallet not found", "Please install it first");
        return;
      }

      const response = await window.solana.connect();
      const publicKeyString = response.publicKey.toString();

      // --- Best Practice: If user is logged in, link wallet to existing user ---
      let endpoint;
      let headers = {
        "Content-Type": "application/json",
      };
      let payload = { walletAddress: publicKeyString };
      const authToken = localStorage.getItem("authToken");
      const userInStorage = localStorage.getItem("user");
      const isLoggedIn = !!authToken && !!userInStorage;

      if (mode === "login") {
        if (isLoggedIn) {
          endpoint = "/users/verify/wallet";
          headers["Authorization"] = `Bearer ${authToken}`;
        } else {
          endpoint = "/users/auth/wallet";
        }
      } else if (mode === "verify") {
        endpoint = "/users/verify/wallet";
        if (!authToken) {
          showNotification('error', "Not authenticated", "Please log in first");
          return;
        }
        headers["Authorization"] = `Bearer ${authToken}`;
      }

      const res = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(payload),
      });

      const data = await res.json();
      if (!res.ok) {
        throw new Error(data.message || "Wallet operation failed");
      }

      // Always update user/token in context and localStorage
      const { user, token, appToken } = data;
      const newToken = token || appToken;
      if (user && newToken) {
        updateUser(user, newToken);
        localStorage.setItem("authToken", newToken);
        localStorage.setItem("user", JSON.stringify(user));
      }

      setConnected(true);
      setPublicKey(publicKeyString);
      localStorage.removeItem(PHANTOM_DISCONNECTED_KEY);

      if (onConnect) {
        onConnect(publicKeyString);
      }

      showNotification('success', "Phantom wallet connected", "Authentication successful!");
    } catch (error) {
      console.error("Phantom connection/auth error:", error);
      if (error.code === 4001) {
        showNotification('info', "Connection rejected", "Phantom wallet connection was rejected");
      } else {
        showNotification('error', error.message || "Failed to connect wallet");
      }
    }
  };

  const disconnectWallet = async () => {
    // This function can be called directly by the button
    await handleDisconnect();
  };

  useEffect(() => {
    const checkPhantom = async () => {
      if (window.solana?.isPhantom) {
        setPhantomInstalled(true);

        const wasManuallyDisconnected = localStorage.getItem(PHANTOM_DISCONNECTED_KEY) === 'true';

        const handleAccountChanged = (newPublicKey) => {
          if (newPublicKey) {
            const keyString = newPublicKey.toString();
            if (!connected || publicKey !== keyString) {
              setConnected(true);
              setPublicKey(keyString);
              localStorage.removeItem(PHANTOM_DISCONNECTED_KEY); // Clear disconnect flag
              // Only call onConnect if not initializing and a new public key is detected
              if (!isInitializing.current && onConnect) {
                onConnect(keyString);
              }
            }
          } else {
            handleDisconnect();
          }
        };

        const handleDisconnectEvent = () => {
          handleDisconnect();
        };

        window.solana.on('accountChanged', handleAccountChanged);
        window.solana.on('disconnect', handleDisconnectEvent);

        try {
          if (!wasManuallyDisconnected) {
            const response = await window.solana.connect({ onlyIfTrusted: true });
            handleAccountChanged(response.publicKey);
          }
        } catch (error) {
          console.log('Phantom silent connection attempt failed:', error.message);
        } finally {
          isInitializing.current = false;
        }

        return () => {
          window.solana.off('accountChanged', handleAccountChanged);
          window.solana.off('disconnect', handleDisconnectEvent);
        };
      } else {
        isInitializing.current = false;
      }
    };

    const timer = setTimeout(() => {
      checkPhantom();
    }, 100);

    return () => clearTimeout(timer);
  }, [connected, publicKey, onConnect, onDisconnect, handleDisconnect]);

  useEffect(() => {
    // Reset the disconnect toast flag when wallet is connected
    if (connected) {
      disconnectToastShown.current = false;
    }
  }, [connected]);

  if (!phantomInstalled) {
    return (
      <a
        href="https://phantom.app/download"
        target="_blank"
        rel="noopener noreferrer"
        className="block"
      >
        <button
          disabled={isDisabled}
          className={`w-full flex items-center justify-center gap-2 py-2.5 px-3 rounded-xl bg-slate-800/50 hover:bg-slate-700/50 border border-slate-600 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed text-white transition-all duration-200 text-sm`}
        >
          <Image src="/phantom.svg" alt="Phantom" width={16} height={16} className="w-4 h-4 mr-1.5" />
          <span className="font-medium">Install Phantom</span>
        </button>
      </a>
    );
  }

  return (
    <div className="w-full">
      {connected ? (
        <div className="flex flex-col items-center">
          <button
            onClick={disconnectWallet}
            className={`w-full flex items-center justify-center gap-2 py-2.5 px-3 rounded-xl bg-slate-800/50 hover:bg-slate-700/50 border border-slate-600 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed text-white transition-all duration-200 text-sm`}
            disabled={isDisabled}
          >
            <Image src="/phantom.svg" alt="Phantom" width={16} height={16} className="w-4 h-4 mr-1.5" />
            <span className="font-medium">Disconnect Phantom</span>
          </button>
          {/* You can uncomment this if you want to show the public key within the button or separately */}
          {/* <p className="mt-1 text-xs text-slate-500 truncate w-full text-center">
            Connected: {`${publicKey?.slice(0, 4)}...${publicKey?.slice(-4)}`}
          </p> */}
        </div>
      ) : (
        <button
          onClick={connectWallet}
          className={`w-full flex items-center justify-center gap-2 py-2.5 px-3 rounded-xl bg-slate-800/50 hover:bg-slate-700/50 border border-slate-600 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed text-white transition-all duration-200 text-sm`}
          disabled={isDisabled}
        >
          <Image src="/phantom.svg" alt="Phantom" width={16} height={16} className="w-4 h-4 mr-1.5" />
          <span className="font-medium">Connect Phantom</span>
        </button>
      )}
    </div>
  );
};

export default Connect2Phantom;