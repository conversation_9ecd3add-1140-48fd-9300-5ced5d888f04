const Comment = require('../models/comment.model'); // Import the Comment model
const mongoose = require('mongoose'); // Import mongoose to check for valid ObjectId
// const { init } = require('../models/user.model'); // This line is not needed here

const addComment = async (req, res) => {
  console.log('Adding comment...');

  try {
    const { content, imageUrl, parentId } = req.body;
    const tokenId = req.params.id;
    console.log(content, imageUrl, parentId);

    const userId = req.user ? req.user.id : null;
    const username = req.user ? req.user.username : 'Anonymous';
    const profileImage = req.user?.profileImage || '/king.png'; // default

    if (!userId) {
      console.log("Abort: User not authenticated");
      return res.status(401).json({ message: 'User not authenticated. Please log in.' });
    }

    // Create the comment object
    const newComment = new Comment({
      tokenId,
      userId,
      username,
      profileImage,
      content,
      imageUrl,
      parentId: parentId || null, // optional reply
    });

    // Save to DB
    const savedComment = await newComment.save();

    // Optional: If it's a reply, push this to the parent comment's replies array
    if (parentId) {
      await Comment.findByIdAndUpdate(parentId, {
        $push: { replies: savedComment._id },
      });
    }

    console.log('Comment saved to DB:', savedComment);
    return res.status(201).json({
      message: 'Comment added successfully',
      comment: savedComment,
    });

  } catch (error) {
    console.error('Error adding comment:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ message: 'Validation error: ' + error.message });
    }
    return res.status(500).json({ message: 'Failed to add comment', error: error.message });
  }
};

const getCommentsByItem = async (req, res) => {
  try {
    const tokenId = req.params.id;
    console.log(tokenId);
    
    if (!tokenId) {
      return res.status(400).json({ message: 'Invalid or missing token ID.' });
    }

    // Fetch all comments for this tokenId (including replies)
    let comments = await Comment.find({ tokenId })
      .sort({ createdAt: -1 }) // Sort by newest first
      .populate('userId', 'username profileImage')
      .lean() || [];

    // Ensure comments is always an array
    comments = Array.isArray(comments) ? comments : [];

    const commentsById = {};
    comments.forEach((comment) => {
      commentsById[comment._id] = { 
        ...comment,
        replies: [],
        // Ensure these fields are always present
        username: comment.username || comment.userId?.username || 'Anonymous',
        profileImage: comment.profileImage || comment.userId?.profileImage || '/king.png'
      };
    });

    const rootComments = [];    comments.forEach((comment) => {
      if (comment.parentId && commentsById[comment.parentId]) {
        // Add reply to parent's replies array if parent exists
        commentsById[comment.parentId].replies.push(commentsById[comment._id]);
      } else if (!comment.parentId) {
        // Only add to root comments if it's not a reply
        rootComments.push(commentsById[comment._id]);
      }
    });

    // Ensure we always return an array
    res.status(200).json({ 
      comments: rootComments || [],
      total: rootComments.length 
    });
  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({ message: 'Failed to fetch comments', error: error.message });
  }
};



const addReply = async (req, res) => {
  try {
    const { commentId } = req.params;
    const { content } = req.body;

    if (!content?.trim()) {
      return res.status(400).json({ message: "Reply content cannot be empty" });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    const parentComment = await Comment.findById(commentId);
    if (!parentComment) {
      return res.status(404).json({ message: "Parent comment not found" });
    }

    const reply = new Comment({
      content: content.trim(),
      userId: req.user.id,
      tokenId: parentComment.tokenId,
      parentId: commentId,
      username: req.user.username,
      profileImage: req.user.profileImage || "/king.png",
    });

    const savedReply = await reply.save();
    // Add the reply to parent comment's replies array
    parentComment.replies.push(savedReply._id);
    await parentComment.save();

    // Return the saved reply with proper user info
    res.status(201).json({ 
      message: "Reply added successfully", 
      reply: {
        ...savedReply.toObject(),
        username: req.user.username,
        profileImage: req.user.profileImage || "/king.png"
      } 
    });
  } catch (error) {
    console.error("Error adding reply:", error);
    res.status(500).json({ 
      message: "Failed to add reply", 
      error: error.message 
    });
  }
};

const likeComment = async (req, res) => {
  try {
    const { commentId } = req.params;
    const userId = req.user.id;

    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    const comment = await Comment.findById(commentId);
    if (!comment) {
      return res.status(404).json({ message: "Comment not found" });
    }

    // Check if user already liked the comment
    if (comment.likedBy && comment.likedBy.includes(userId)) {
      return res.status(400).json({ 
        success: false, 
        message: "Comment already liked by this user",
        likes: comment.likes || 0
      });
    }

    // Add user to likedBy array and increment likes
    await Comment.findByIdAndUpdate(commentId, {
      $addToSet: { likedBy: userId },
      $inc: { likes: 1 }
    });

    const updatedComment = await Comment.findById(commentId);
    
    res.status(200).json({
      success: true,
      message: "Comment liked successfully",
      likes: updatedComment.likes || 0
    });
  } catch (error) {
    console.error("Error liking comment:", error);
    res.status(500).json({ 
      success: false,
      message: "Failed to like comment", 
      error: error.message 
    });
  }
};

const unlikeComment = async (req, res) => {
  try {
    const { commentId } = req.params;
    const userId = req.user.id;

    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    const comment = await Comment.findById(commentId);
    if (!comment) {
      return res.status(404).json({ message: "Comment not found" });
    }

    // Check if user has liked the comment
    if (!comment.likedBy || !comment.likedBy.includes(userId)) {
      return res.status(400).json({ 
        success: false, 
        message: "Comment not liked by this user",
        likes: comment.likes || 0
      });
    }

    // Remove user from likedBy array and decrement likes
    await Comment.findByIdAndUpdate(commentId, {
      $pull: { likedBy: userId },
      $inc: { likes: -1 }
    });

    const updatedComment = await Comment.findById(commentId);
    
    res.status(200).json({
      success: true,
      message: "Comment unliked successfully",
      likes: updatedComment.likes || 0
    });
  } catch (error) {
    console.error("Error unliking comment:", error);
    res.status(500).json({ 
      success: false,
      message: "Failed to unlike comment", 
      error: error.message 
    });
  }
};

module.exports = { addComment, getCommentsByItem, addReply, likeComment, unlikeComment };