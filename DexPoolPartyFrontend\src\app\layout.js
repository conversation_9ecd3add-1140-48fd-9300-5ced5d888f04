// app/layout.js
"use client";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import SideBar from "@/components/Sidebar";
import Navbar from "@/components/Navbar";
import { CoinMarketProvider } from '../../context/context.js';
import { TrendingProvider } from '@/contexts/trendingData.js';
import Footer from "@/components/Footer";
import Animation from "@/components/Animation";
import { ProgressBar } from "@/components/ProgressBar";
import 'nprogress/nprogress.css';
import { Suspense, useEffect, useState } from "react";
import { AuthProvider } from '@/contexts/AuthContext';
import { Toaster } from "sonner";
import { X as LucideX } from "lucide-react";
import { usePathname } from "next/navigation";
import WalletContextProvider from "@/contexts/WalletContextProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({ children }) {
  const pathname = usePathname();
  const isAdvancePage = pathname === '/advancePage';
  const isAdminPage = pathname.startsWith('/admin');
  
  // Sidebar collapsed state managed here
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Dynamically set main content padding based on sidebar state
  const mainContentDesktopPaddingClass =
    !isAdvancePage && !isAdminPage
      ? sidebarCollapsed
        ? 'md:pl-[70px]'
        : 'md:pl-[250px] lg:pl-[300px]'
      : 'md:pl-0';

  return (
    <html lang="en">
      <head>
        <style jsx global>{`
          #nprogress {
            pointer-events: none;
          }
          #nprogress .bar {
            background: #9507b5;
            position: fixed;
            z-index: 1031;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
          }
          #nprogress .peg {
            display: block;
            position: absolute;
            right: 0px;
            width: 100px;
            height: 100%;
            box-shadow: 0 0 10px #50C878, 0 0 5px #50C878;
            opacity: 1.0;
            transform: rotate(3deg) translate(0px, -4px);
          }
        `}</style>
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <TrendingProvider>
          <AuthProvider>
            <Suspense fallback={null}>
              <ProgressBar />
            </Suspense>
            <WalletContextProvider>
              <CoinMarketProvider>
                <div className="min-h-screen bg-[#111115] text-white relative overflow-x-hidden">
                  <div className="flex justify-center items-center z-10 mb-10">
                  </div>
                  {/* Only show Navbar for non-admin pages */}
                  {!isAdminPage && <Navbar />}
                  <div className="flex flex-col md:flex-row min-h-[calc(100vh-64px)]">
                    {/* SideBar will manage its own display types */}
                    {/* Only show SideBar on desktop for /advancePage (Market) and exclude admin pages */}
                    {(!isAdvancePage && !isAdminPage) || (isAdvancePage && typeof window !== 'undefined' && window.innerWidth >= 768) ? (
                      <SideBar collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
                    ) : null}
                    {/* Main content area - apply dynamic padding */}
                    <main className={`flex-1 w-full min-w-0 relative transition-all duration-300 ease-in-out ${mainContentDesktopPaddingClass}`}>
                      <Toaster position="top-right" richColors closeButton />
                      {children}
                    </main>
                  </div>
                </div>
              </CoinMarketProvider>
            </WalletContextProvider>
            {/* Only show Footer for non-admin pages */}
            {!isAdminPage && <Footer sidebarCollapsed={sidebarCollapsed} />}
          </AuthProvider>
        </TrendingProvider>
      </body>
    </html>
  );
}