const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     Comment:
 *       type: object
 *       required:
 *         - tokenId
 *         - userId
 *         - content
 *       properties:
 *         tokenId:
 *           type: string
 *         userId:
 *           type: string
 *         username:
 *           type: string
 *         profileImage:
 *           type: string
 *         content:
 *           type: string
 *         imageUrl:
 *           type: string
 *         parentId:
 *           type: string
 *         replies:
 *           type: array
 *           items:
 *             type: string
 *         likes:
 *           type: number
 *         isActive:
 *           type: boolean
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 */

const commentSchema = new mongoose.Schema({
  tokenId: {
    type: String,
    required: true
  },
  userId: {
    type: String,
    required: true
  },
  username: {
    type: String
  },
  profileImage: {
    type: String,
    default: '/king.png'
  },
  content: {
    type: String,
    required: true,
    maxlength: 2000
  },
  imageUrl: {
    type: String
  },
  parentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
    default: null
  },
  replies: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment'
    }
  ],
  likes: {
    type: Number,
    default: 0
  },
  likedBy: [
    {
      type: String // Array of user IDs who liked this comment
    }
  ],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Restrict nesting: Only allow reply if parent is a top-level comment
commentSchema.pre('save', async function (next) {
  if (this.parentId) {
    const parent = await mongoose.model('Comment').findById(this.parentId);
    if (parent && parent.parentId) {
      const err = new Error('Replies to replies are not allowed.');
      return next(err);
    }
  }
  next();
});

// Indexes
commentSchema.index({ tokenId: 1, createdAt: -1 });
commentSchema.index({ parentId: 1 });

module.exports = mongoose.model('Comment', commentSchema);
